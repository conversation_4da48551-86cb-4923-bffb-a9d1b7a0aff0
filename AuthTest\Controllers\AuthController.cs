using AuthTest.Models;
using AuthTest.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace AuthTest.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly UserService _userService;
    private readonly TokenService _tokenService;
    private readonly ILogger<AuthController> _logger;

    public AuthController(UserService userService, TokenService tokenService, ILogger<AuthController> logger)
    {
        _userService = userService;
        _tokenService = tokenService;
        _logger = logger;
    }

    [HttpPost("login")]
    [AllowAnonymous]
    public ActionResult<LoginResponse> Login([FromBody] LoginRequest request)
    {
        try
        {
            var user = _userService.ValidateCredentials(request.Username, request.Password);
            if (user == null)
            {
                return Unauthorized(new LoginResponse
                {
                    Success = false,
                    Message = "Nom d'utilisateur ou mot de passe incorrect"
                });
            }

            // Generate JWT token
            var token = _tokenService.GenerateJwtToken(user.Id, user.Username, user.Email, user.Roles);

            // Generate refresh token
            var refreshToken = _tokenService.GenerateRefreshToken(user.Id);

            return Ok(new LoginResponse
            {
                Success = true,
                Token = token,
                RefreshToken = refreshToken.Token,
                Expiration = refreshToken.Expires
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'authentification de l'utilisateur {Username}", request.Username);
            return StatusCode(500, new LoginResponse
            {
                Success = false,
                Message = "Une erreur est survenue lors de l'authentification"
            });
        }
    }

    [HttpPost("refresh-token")]
    [AllowAnonymous]
    public ActionResult<LoginResponse> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        try
        {
            var userId = _tokenService.ValidateRefreshToken(request.Token, request.RefreshToken);
            if (userId == null)
            {
                return Unauthorized(new LoginResponse
                {
                    Success = false,
                    Message = "Token de rafraîchissement invalide ou expiré"
                });
            }

            var user = _userService.GetById(userId);
            if (user == null)
            {
                return Unauthorized(new LoginResponse
                {
                    Success = false,
                    Message = "Utilisateur non trouvé"
                });
            }

            // Generate new JWT token
            var newToken = _tokenService.GenerateJwtToken(user.Id, user.Username, user.Email, user.Roles);

            // Generate new refresh token
            var newRefreshToken = _tokenService.GenerateRefreshToken(user.Id);

            return Ok(new LoginResponse
            {
                Success = true,
                Token = newToken,
                RefreshToken = newRefreshToken.Token,
                Expiration = newRefreshToken.Expires
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du rafraîchissement du token");
            return StatusCode(500, new LoginResponse
            {
                Success = false,
                Message = "Une erreur est survenue lors du rafraîchissement du token"
            });
        }
    }

    [HttpPost("logout")]
    [Authorize]
    public IActionResult Logout()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            // Revoke all refresh tokens for the user
            _tokenService.RevokeAllRefreshTokens(userId);

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la déconnexion");
            return StatusCode(500, "Une erreur est survenue lors de la déconnexion");
        }
    }

    [HttpGet("me")]
    [Authorize]
    public ActionResult<UserInfo> GetCurrentUser()
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized("Utilisateur non authentifié");
            }

            var user = _userService.GetById(userId);
            if (user == null)
            {
                return NotFound("Utilisateur non trouvé");
            }

            return Ok(new UserInfo
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                Roles = user.Roles
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'utilisateur actuel");
            return StatusCode(500, "Une erreur est survenue lors de la récupération de l'utilisateur actuel");
        }
    }

    [HttpPost("register")]
    [AllowAnonymous]
    public ActionResult<LoginResponse> Register([FromBody] RegisterRequest request)
    {
        try
        {
            // Create the user
            var user = _userService.CreateUser(request.Username, request.Email, request.Password, new[] { "User" });

            // Generate JWT token
            var token = _tokenService.GenerateJwtToken(user.Id, user.Username, user.Email, user.Roles);

            // Generate refresh token
            var refreshToken = _tokenService.GenerateRefreshToken(user.Id);

            return Ok(new LoginResponse
            {
                Success = true,
                Token = token,
                RefreshToken = refreshToken.Token,
                Expiration = refreshToken.Expires
            });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new LoginResponse
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'enregistrement de l'utilisateur {Username}", request.Username);
            return StatusCode(500, new LoginResponse
            {
                Success = false,
                Message = "Une erreur est survenue lors de l'enregistrement"
            });
        }
    }

    [HttpPost("forgot-password")]
    [AllowAnonymous]
    public IActionResult ForgotPassword([FromBody] ForgotPasswordRequest request)
    {
        try
        {
            var token = _userService.GeneratePasswordResetToken(request.Email);
            if (token == null)
            {
                // Don't reveal that the user does not exist
                return Ok();
            }

            // In a real application, send an email with the reset link
            // For this example, we'll just log the token
            _logger.LogInformation("Password reset token for {Email}: {Token}", request.Email, token);

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la demande de réinitialisation de mot de passe pour {Email}", request.Email);
            return StatusCode(500, "Une erreur est survenue lors de la demande de réinitialisation de mot de passe");
        }
    }

    [HttpPost("reset-password")]
    [AllowAnonymous]
    public IActionResult ResetPassword([FromBody] ResetPasswordRequest request)
    {
        try
        {
            var result = _userService.ResetPassword(request.Email, request.Token, request.NewPassword);
            if (!result)
            {
                return BadRequest("Token invalide ou expiré");
            }

            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la réinitialisation du mot de passe pour {Email}", request.Email);
            return StatusCode(500, "Une erreur est survenue lors de la réinitialisation du mot de passe");
        }
    }
}
