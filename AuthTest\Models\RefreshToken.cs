namespace AuthTest.Models;

/// <summary>
/// Represents a refresh token for JWT authentication.
/// </summary>
public class RefreshToken
{
    /// <summary>
    /// Gets or sets the token value.
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the expiration date of the token.
    /// </summary>
    public DateTime Expires { get; set; }

    /// <summary>
    /// Gets or sets the creation date of the token.
    /// </summary>
    public DateTime Created { get; set; }

    /// <summary>
    /// Gets or sets the user ID associated with the token.
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Gets a value indicating whether the token is expired.
    /// </summary>
    public bool IsExpired => DateTime.UtcNow >= Expires;
}
