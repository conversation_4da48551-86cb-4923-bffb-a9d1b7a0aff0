namespace AuthTest.Models;

/// <summary>
/// Represents a user in the system.
/// </summary>
public class User
{
    /// <summary>
    /// Gets or sets the user ID.
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the username.
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the email.
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the password hash.
    /// </summary>
    public string PasswordHash { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the roles.
    /// </summary>
    public List<string> Roles { get; set; } = new List<string>();

    /// <summary>
    /// Gets or sets the failed login attempts.
    /// </summary>
    public int FailedLoginAttempts { get; set; } = 0;

    /// <summary>
    /// Gets or sets the account lockout end date.
    /// </summary>
    public DateTime? LockoutEnd { get; set; }

    /// <summary>
    /// Gets a value indicating whether the account is locked out.
    /// </summary>
    public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd.Value > DateTime.UtcNow;

    /// <summary>
    /// Gets or sets a value indicating whether two-factor authentication is enabled.
    /// </summary>
    public bool TwoFactorEnabled { get; set; } = false;

    /// <summary>
    /// Gets or sets the two-factor secret key.
    /// </summary>
    public string? TwoFactorSecretKey { get; set; }

    /// <summary>
    /// Gets or sets the reset password token.
    /// </summary>
    public string? ResetPasswordToken { get; set; }

    /// <summary>
    /// Gets or sets the reset password token expiration.
    /// </summary>
    public DateTime? ResetPasswordTokenExpiration { get; set; }
}
