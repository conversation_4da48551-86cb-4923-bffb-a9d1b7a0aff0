using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using AuthTest.Models;
using Microsoft.IdentityModel.Tokens;

namespace AuthTest.Services;

/// <summary>
/// Service for JWT token generation and validation.
/// </summary>
public class TokenService
{
    private readonly IConfiguration _configuration;
    private readonly Dictionary<string, List<RefreshToken>> _refreshTokens = new();

    /// <summary>
    /// Initializes a new instance of the <see cref="TokenService"/> class.
    /// </summary>
    /// <param name="configuration">The configuration.</param>
    public TokenService(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    /// <summary>
    /// Generates a JWT token for a user.
    /// </summary>
    /// <param name="userId">The user ID.</param>
    /// <param name="username">The username.</param>
    /// <param name="email">The email.</param>
    /// <param name="roles">The roles.</param>
    /// <returns>The JWT token.</returns>
    public string GenerateJwtToken(string userId, string username, string email, IEnumerable<string> roles)
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? "DefaultSecretKey123456789012345678901234"));
        var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, userId),
            new Claim(ClaimTypes.Name, username),
            new Claim(ClaimTypes.Email, email)
        };

        // Add roles to claims
        foreach (var role in roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Audience"],
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(Convert.ToDouble(_configuration["Jwt:ExpirationMinutes"] ?? "60")),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    /// <summary>
    /// Generates a refresh token for a user.
    /// </summary>
    /// <param name="userId">The user ID.</param>
    /// <returns>The refresh token.</returns>
    public RefreshToken GenerateRefreshToken(string userId)
    {
        var randomBytes = new byte[64];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);

        var refreshToken = new RefreshToken
        {
            Token = Convert.ToBase64String(randomBytes),
            Expires = DateTime.UtcNow.AddDays(7), // Refresh tokens last 7 days
            Created = DateTime.UtcNow,
            UserId = userId
        };

        // Store the refresh token
        if (!_refreshTokens.ContainsKey(userId))
        {
            _refreshTokens[userId] = new List<RefreshToken>();
        }

        // Remove expired tokens
        _refreshTokens[userId].RemoveAll(t => t.IsExpired);

        // Add the new token
        _refreshTokens[userId].Add(refreshToken);

        return refreshToken;
    }

    /// <summary>
    /// Validates a refresh token.
    /// </summary>
    /// <param name="token">The token to validate.</param>
    /// <param name="refreshToken">The refresh token to validate.</param>
    /// <returns>The user ID if the token is valid, null otherwise.</returns>
    public string? ValidateRefreshToken(string token, string refreshToken)
    {
        var principal = GetPrincipalFromExpiredToken(token);
        if (principal == null)
        {
            return null;
        }

        var userId = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            return null;
        }

        // Check if the user has any refresh tokens
        if (!_refreshTokens.ContainsKey(userId))
        {
            return null;
        }

        // Find the refresh token
        var storedToken = _refreshTokens[userId].FirstOrDefault(t => t.Token == refreshToken);
        if (storedToken == null || storedToken.IsExpired)
        {
            return null;
        }

        return userId;
    }

    /// <summary>
    /// Revokes all refresh tokens for a user.
    /// </summary>
    /// <param name="userId">The user ID.</param>
    public void RevokeAllRefreshTokens(string userId)
    {
        if (_refreshTokens.ContainsKey(userId))
        {
            _refreshTokens.Remove(userId);
        }
    }

    /// <summary>
    /// Gets the principal from an expired token.
    /// </summary>
    /// <param name="token">The expired token.</param>
    /// <returns>The principal if the token is valid, null otherwise.</returns>
    private ClaimsPrincipal? GetPrincipalFromExpiredToken(string token)
    {
        var tokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = false, // We don't care about the token's expiration date
            ValidateIssuerSigningKey = true,
            ValidIssuer = _configuration["Jwt:Issuer"],
            ValidAudience = _configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? "DefaultSecretKey123456789012345678901234"))
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        
        try
        {
            var principal = tokenHandler.ValidateToken(token, tokenValidationParameters, out var securityToken);
            
            if (securityToken is not JwtSecurityToken jwtSecurityToken || 
                !jwtSecurityToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
            {
                return null;
            }

            return principal;
        }
        catch
        {
            return null;
        }
    }
}
