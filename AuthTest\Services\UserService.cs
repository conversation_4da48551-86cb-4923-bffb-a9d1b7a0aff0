using System.Security.Cryptography;
using AuthTest.Models;

namespace AuthTest.Services;

/// <summary>
/// Service for user management.
/// </summary>
public class UserService
{
    private readonly PasswordService _passwordService;
    private readonly Dictionary<string, User> _users = new();
    private const int MaxFailedAttempts = 5;
    private readonly TimeSpan _lockoutDuration = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Initializes a new instance of the <see cref="UserService"/> class.
    /// </summary>
    /// <param name="passwordService">The password service.</param>
    public UserService(PasswordService passwordService)
    {
        _passwordService = passwordService;
        SeedUsers();
    }

    /// <summary>
    /// Gets a user by username.
    /// </summary>
    /// <param name="username">The username.</param>
    /// <returns>The user if found, null otherwise.</returns>
    public User? GetByUsername(string username)
    {
        return _users.Values.FirstOrDefault(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Gets a user by email.
    /// </summary>
    /// <param name="email">The email.</param>
    /// <returns>The user if found, null otherwise.</returns>
    public User? GetByEmail(string email)
    {
        return _users.Values.FirstOrDefault(u => u.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Gets a user by ID.
    /// </summary>
    /// <param name="id">The user ID.</param>
    /// <returns>The user if found, null otherwise.</returns>
    public User? GetById(string id)
    {
        return _users.TryGetValue(id, out var user) ? user : null;
    }

    /// <summary>
    /// Validates a user's credentials.
    /// </summary>
    /// <param name="username">The username.</param>
    /// <param name="password">The password.</param>
    /// <returns>The user if the credentials are valid, null otherwise.</returns>
    public User? ValidateCredentials(string username, string password)
    {
        var user = GetByUsername(username) ?? GetByEmail(username);
        if (user == null)
        {
            return null;
        }

        // Check if the account is locked out
        if (user.IsLockedOut)
        {
            return null;
        }

        // Verify the password
        if (!_passwordService.VerifyPassword(password, user.PasswordHash))
        {
            // Increment failed login attempts
            user.FailedLoginAttempts++;

            // Lock the account if too many failed attempts
            if (user.FailedLoginAttempts >= MaxFailedAttempts)
            {
                user.LockoutEnd = DateTime.UtcNow.Add(_lockoutDuration);
            }

            return null;
        }

        // Reset failed login attempts on successful login
        user.FailedLoginAttempts = 0;
        user.LockoutEnd = null;

        return user;
    }

    /// <summary>
    /// Creates a new user.
    /// </summary>
    /// <param name="username">The username.</param>
    /// <param name="email">The email.</param>
    /// <param name="password">The password.</param>
    /// <param name="roles">The roles.</param>
    /// <returns>The created user.</returns>
    public User CreateUser(string username, string email, string password, IEnumerable<string> roles)
    {
        // Check if the username or email already exists
        if (GetByUsername(username) != null)
        {
            throw new InvalidOperationException($"Username '{username}' is already taken.");
        }

        if (GetByEmail(email) != null)
        {
            throw new InvalidOperationException($"Email '{email}' is already registered.");
        }

        // Create the user
        var user = new User
        {
            Id = Guid.NewGuid().ToString(),
            Username = username,
            Email = email,
            PasswordHash = _passwordService.HashPassword(password),
            Roles = roles.ToList()
        };

        // Add the user to the dictionary
        _users[user.Id] = user;

        return user;
    }

    /// <summary>
    /// Generates a password reset token for a user.
    /// </summary>
    /// <param name="email">The user's email.</param>
    /// <returns>The reset token if the user exists, null otherwise.</returns>
    public string? GeneratePasswordResetToken(string email)
    {
        var user = GetByEmail(email);
        if (user == null)
        {
            return null;
        }

        // Generate a random token
        var randomBytes = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        var token = Convert.ToBase64String(randomBytes);

        // Set the token and expiration
        user.ResetPasswordToken = token;
        user.ResetPasswordTokenExpiration = DateTime.UtcNow.AddHours(1); // Token valid for 1 hour

        return token;
    }

    /// <summary>
    /// Resets a user's password using a reset token.
    /// </summary>
    /// <param name="email">The user's email.</param>
    /// <param name="token">The reset token.</param>
    /// <param name="newPassword">The new password.</param>
    /// <returns>True if the password was reset, false otherwise.</returns>
    public bool ResetPassword(string email, string token, string newPassword)
    {
        var user = GetByEmail(email);
        if (user == null)
        {
            return false;
        }

        // Check if the token is valid
        if (user.ResetPasswordToken != token || 
            !user.ResetPasswordTokenExpiration.HasValue || 
            user.ResetPasswordTokenExpiration.Value < DateTime.UtcNow)
        {
            return false;
        }

        // Reset the password
        user.PasswordHash = _passwordService.HashPassword(newPassword);
        user.ResetPasswordToken = null;
        user.ResetPasswordTokenExpiration = null;
        user.FailedLoginAttempts = 0;
        user.LockoutEnd = null;

        return true;
    }

    /// <summary>
    /// Seeds the users dictionary with some default users.
    /// </summary>
    private void SeedUsers()
    {
        // Create admin user
        var adminId = Guid.NewGuid().ToString();
        _users[adminId] = new User
        {
            Id = adminId,
            Username = "admin",
            Email = "<EMAIL>",
            PasswordHash = _passwordService.HashPassword("admin123"),
            Roles = new List<string> { "Administrator" }
        };

        // Create regular user
        var userId = Guid.NewGuid().ToString();
        _users[userId] = new User
        {
            Id = userId,
            Username = "user",
            Email = "<EMAIL>",
            PasswordHash = _passwordService.HashPassword("user123"),
            Roles = new List<string> { "User" }
        };
    }
}
