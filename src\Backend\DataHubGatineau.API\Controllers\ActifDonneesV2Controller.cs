﻿using Asp.Versioning;
using DataHubGatineau.Application.DTOs;
using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Text;
using System.Text.Json;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// ContrÃ´leur pour les opÃ©rations sur les actifs de donnÃ©es (version 2).
/// </summary>
[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/ActifDonnees")]
public class ActifDonneesV2Controller : ApiControllerBase
{
    private readonly IServiceActifDonnees _serviceActifDonnees;
    // private readonly IServiceExecutionRegleQualite _serviceExecutionRegleQualite;
    private readonly ILogger<ActifDonneesV2Controller> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ActifDonneesV2Controller"/>.
    /// </summary>
    /// <param name="serviceActifDonnees">Service des actifs de donnÃ©es.</param>
    /// <param name="logger">Logger.</param>
    public ActifDonneesV2Controller(
        IServiceActifDonnees serviceActifDonnees,
        // IServiceExecutionRegleQualite serviceExecutionRegleQualite,
        ILogger<ActifDonneesV2Controller> logger)
    {
        _serviceActifDonnees = serviceActifDonnees;
        // _serviceExecutionRegleQualite = serviceExecutionRegleQualite;
        _logger = logger;
    }

    /// <summary>
    /// Obtient tous les actifs de donnÃ©es.
    /// </summary>
    /// <param name="includeType">Inclure le type d'actif de donnÃ©es.</param>
    /// <param name="includeFormat">Inclure le format d'actif de donnÃ©es.</param>
    /// <param name="includeSource">Inclure la source d'actif de donnÃ©es.</param>
    /// <param name="includeFrequence">Inclure la frÃ©quence de mise Ã  jour.</param>
    /// <param name="includeStatut">Inclure le statut d'actif de donnÃ©es.</param>
    /// <param name="includeDomaine">Inclure le domaine de gouvernance.</param>
    /// <param name="includeConnexion">Inclure la connexion Ã  la source de donnÃ©es.</param>
    /// <returns>Une collection d'actifs de donnÃ©es.</returns>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ActifDonneesDTO>>> ObtenirTous(
        [FromQuery] bool includeType = true,
        [FromQuery] bool includeFormat = false,
        [FromQuery] bool includeSource = false,
        [FromQuery] bool includeFrequence = false,
        [FromQuery] bool includeStatut = false,
        [FromQuery] bool includeDomaine = false,
        [FromQuery] bool includeConnexion = false,
        [FromQuery] int limite = 100)
    {
        try
        {
            // Limiter le nombre de résultats pour éviter les problèmes de performance
            if (limite > 500)
            {
                limite = 500; // Maximum 500 résultats
            }

            // Construire les options d'inclusion
            var includes = ActifDonneesInclude.None;
            if (includeType) includes |= ActifDonneesInclude.Type;
            if (includeFormat) includes |= ActifDonneesInclude.Format;
            if (includeSource) includes |= ActifDonneesInclude.Source;
            if (includeFrequence) includes |= ActifDonneesInclude.FrequenceMiseAJour;
            if (includeStatut) includes |= ActifDonneesInclude.Statut;
            if (includeDomaine) includes |= ActifDonneesInclude.DomaineGouvernance;
            if (includeConnexion) includes |= ActifDonneesInclude.ConnexionSourceDonnees;

            _logger.LogInformation("Obtention de tous les actifs de donnÃ©es avec les inclusions: {Includes}, limite: {Limite}", includes, limite);

            var actifs = await _serviceActifDonnees.ObtenirTousAsync(includes);

            // Appliquer la limite côté application
            var actifsLimites = actifs.Take(limite);

            var actifsDTO = actifsLimites.Select(MapToDTO);
            return Ok(actifsDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de tous les actifs de donnÃ©es");
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des actifs de donnÃ©es");
        }
    }

    /// <summary>
    /// Convertit un actif de donnÃ©es en DTO.
    /// </summary>
    /// <param name="actif">Actif de donnÃ©es Ã  convertir.</param>
    /// <returns>DTO de l'actif de donnÃ©es.</returns>
    private static ActifDonneesDTO MapToDTO(ActifDonnees actif)
    {
        var dto = new ActifDonneesDTO
        {
            Id = actif.Id,
            Nom = actif.Nom,
            Description = actif.Description,
            Type = actif.TypeActifDonnees?.Nom,
            TypeActifDonneesId = actif.TypeActifDonneesId,
            TypeActifDonneesNom = actif.TypeActifDonnees?.Nom,
            Format = actif.FormatActifDonnees?.Nom,
            FormatActifDonneesId = actif.FormatActifDonneesId,
            FormatActifDonneesNom = actif.FormatActifDonnees?.Nom,
            Source = actif.SourceActifDonnees?.Nom,
            SourceActifDonneesId = actif.SourceActifDonneesId,
            SourceActifDonneesNom = actif.SourceActifDonnees?.Nom,
            Proprietaire = actif.Proprietaire,
            ClassificationSensibilite = actif.ClassificationSensibilite,
            DateDerniereMiseAJour = actif.DateDerniereMiseAJour,
            FrequenceMiseAJour = actif.FrequenceMiseAJour?.Nom,
            FrequenceMiseAJourId = actif.FrequenceMiseAJourId,
            FrequenceMiseAJourNom = actif.FrequenceMiseAJour?.Nom,
            CheminAcces = actif.CheminAcces,
            DomaineGouvernanceId = actif.DomaineGouvernanceId,
            EstElementCritique = actif.EstElementCritique,
            ConnexionSourceDonneesId = actif.ConnexionSourceDonneesId,
            Statut = actif.StatutActifDonnees?.Nom,
            StatutActifDonneesId = actif.StatutActifDonneesId,
            StatutActifDonneesNom = actif.StatutActifDonnees?.Nom,
            DateCreation = actif.DateCreation,
            DateModification = actif.DateModification,
            CreePar = actif.CreePar,
            ModifiePar = actif.ModifiePar
        };

        // Ajouter les produits de donnÃ©es si disponibles
        if (actif.ProduitsDonnees != null && actif.ProduitsDonnees.Any())
        {
            dto.ProduitsDonnees = actif.ProduitsDonnees.Select(p => new ProduitDonneesDTO
            {
                Id = p.Id,
                Nom = p.Nom,
                Description = p.Description,
                Proprietaire = p.Proprietaire,
                DomaineGouvernanceId = p.DomaineGouvernanceId,
                DateCreation = p.DateCreation,
                DateModification = p.DateModification,
                CreePar = p.CreePar,
                ModifiePar = p.ModifiePar
            }).ToList();
        }

        // Ajouter les mÃ©tadonnÃ©es si disponibles
        if (actif.Metadonnees != null && actif.Metadonnees.Any())
        {
            dto.Metadonnees = actif.Metadonnees.Select(m => new MetadonneeDTO
            {
                Id = m.Id,
                Nom = m.Nom,
                Valeur = m.Valeur,
                TypeId = m.TypeId,
                CategorieId = m.CategorieId,
                DateCreation = m.DateCreation,
                DateModification = m.DateModification,
                CreePar = m.CreePar,
                ModifiePar = m.ModifiePar
            }).ToList();
        }

        return dto;
    }

    /// <summary>
    /// Obtient un actif de donnÃ©es par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de l'actif de donnÃ©es.</param>
    /// <param name="includeAll">Inclure toutes les propriÃ©tÃ©s de navigation.</param>
    /// <param name="includeType">Inclure le type d'actif de donnÃ©es.</param>
    /// <param name="includeFormat">Inclure le format d'actif de donnÃ©es.</param>
    /// <param name="includeSource">Inclure la source d'actif de donnÃ©es.</param>
    /// <param name="includeFrequence">Inclure la frÃ©quence de mise Ã  jour.</param>
    /// <param name="includeStatut">Inclure le statut d'actif de donnÃ©es.</param>
    /// <param name="includeDomaine">Inclure le domaine de gouvernance.</param>
    /// <param name="includeConnexion">Inclure la connexion Ã  la source de donnÃ©es.</param>
    /// <param name="includeMetadonnees">Inclure les mÃ©tadonnÃ©es.</param>
    /// <param name="includeProduitsDonnees">Inclure les produits de donnÃ©es.</param>
    /// <returns>L'actif de donnÃ©es correspondant Ã  l'identifiant spÃ©cifiÃ©.</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ActifDonneesDTO>> ObtenirParId(
        Guid id,
        [FromQuery] bool includeAll = false,
        [FromQuery] bool includeType = true,
        [FromQuery] bool includeFormat = true,
        [FromQuery] bool includeSource = true,
        [FromQuery] bool includeFrequence = true,
        [FromQuery] bool includeStatut = true,
        [FromQuery] bool includeDomaine = true,
        [FromQuery] bool includeConnexion = false,
        [FromQuery] bool includeMetadonnees = true,
        [FromQuery] bool includeProduitsDonnees = true)
    {
        try
        {
            // Construire les options d'inclusion
            var includes = ActifDonneesInclude.None;

            if (includeAll)
            {
                includes = ActifDonneesInclude.All;
            }
            else
            {
                if (includeType) includes |= ActifDonneesInclude.Type;
                if (includeFormat) includes |= ActifDonneesInclude.Format;
                if (includeSource) includes |= ActifDonneesInclude.Source;
                if (includeFrequence) includes |= ActifDonneesInclude.FrequenceMiseAJour;
                if (includeStatut) includes |= ActifDonneesInclude.Statut;
                if (includeDomaine) includes |= ActifDonneesInclude.DomaineGouvernance;
                if (includeConnexion) includes |= ActifDonneesInclude.ConnexionSourceDonnees;
                if (includeMetadonnees) includes |= ActifDonneesInclude.Metadonnees;
                if (includeProduitsDonnees) includes |= ActifDonneesInclude.ProduitsDonnees;
            }

            _logger.LogInformation("Obtention de l'actif de donnÃ©es {Id} avec les inclusions: {Includes}", id, includes);

            var actif = await _serviceActifDonnees.ObtenirParIdAsync(id, includes);
            if (actif == null)
            {
                return NotFound($"L'actif de donnÃ©es avec l'identifiant {id} n'a pas Ã©tÃ© trouvÃ©.");
            }

            var actifDTO = MapToDTO(actif);
            return Ok(actifDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de l'actif de donnÃ©es avec l'ID {ActifId}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention de l'actif de donnÃ©es");
        }
    }

    /// <summary>
    /// Obtient les actifs de donnÃ©es par type.
    /// </summary>
    /// <param name="type">Type d'actif de donnÃ©es.</param>
    /// <returns>Une collection d'actifs de donnÃ©es du type spÃ©cifiÃ©.</returns>
    [HttpGet("parType/{type}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ActifDonneesDTO>>> ObtenirParType(string type)
    {
        try
        {
            var actifs = await _serviceActifDonnees.ObtenirParTypeAsync(type);
            var actifsDTO = actifs.Select(MapToDTO);
            return Ok(actifsDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des actifs de donnÃ©es de type {Type}", type);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des actifs de donnÃ©es");
        }
    }

    /// <summary>
    /// Obtient les actifs de donnÃ©es par classification de sensibilitÃ©.
    /// </summary>
    /// <param name="classification">Classification de sensibilitÃ©.</param>
    /// <returns>Une collection d'actifs de donnÃ©es avec la classification spÃ©cifiÃ©e.</returns>
    [HttpGet("parClassification/{classification}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ActifDonneesDTO>>> ObtenirParClassification(ClassificationSensibilite classification)
    {
        try
        {
            var actifs = await _serviceActifDonnees.ObtenirParClassificationAsync(classification);
            var actifsDTO = actifs.Select(MapToDTO);
            return Ok(actifsDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des actifs de donnÃ©es de classification {Classification}", classification);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des actifs de donnÃ©es");
        }
    }

    /// <summary>
    /// Obtient les actifs de donnÃ©es par propriÃ©taire.
    /// </summary>
    /// <param name="proprietaire">PropriÃ©taire des actifs de donnÃ©es.</param>
    /// <returns>Une collection d'actifs de donnÃ©es appartenant au propriÃ©taire spÃ©cifiÃ©.</returns>
    [HttpGet("parProprietaire/{proprietaire}")]
    [ResponseCache(Duration = 300)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ActifDonneesDTO>>> ObtenirParProprietaire(string proprietaire)
    {
        try
        {
            var actifs = await _serviceActifDonnees.ObtenirParProprietaireAsync(proprietaire);
            var actifsDTO = actifs.Select(MapToDTO);
            return Ok(actifsDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des actifs de donnÃ©es du propriÃ©taire {Proprietaire}", proprietaire);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de l'obtention des actifs de donnÃ©es");
        }
    }

    /// <summary>
    /// Recherche des actifs de donnÃ©es par nom.
    /// </summary>
    /// <param name="terme">Terme de recherche.</param>
    /// <param name="limite">Nombre maximum de résultats à retourner (par défaut: 50).</param>
    /// <returns>Une collection d'actifs de donnÃ©es correspondant au terme de recherche.</returns>
    [HttpGet("rechercher")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<ActifDonneesDTO>>> RechercherParNom(
        [FromQuery] string terme,
        [FromQuery] int limite = 50)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(terme))
            {
                return BadRequest("Le terme de recherche ne peut pas Ãªtre vide.");
            }

            // Limiter le nombre de résultats pour éviter les problèmes de performance
            if (limite > 100)
            {
                limite = 100; // Maximum 100 résultats
            }

            // Utiliser une mÃ©thode optimisÃ©e pour la recherche avec inclusions minimales
            var actifs = await _serviceActifDonnees.RechercherAsync(terme, ActifDonneesInclude.Basic);

            // Appliquer la limite côté application si nécessaire
            var actifsLimites = actifs.Take(limite);

            _logger.LogInformation("Recherche d'actifs de donnÃ©es avec le terme '{Terme}' a retournÃ© {Count} rÃ©sultats (limité à {Limite})",
                terme, actifsLimites.Count(), limite);

            var actifsDTO = actifsLimites.Select(MapToDTO);

            // Ajouter des en-tÃªtes de cache pour le navigateur
            Response.Headers["Cache-Control"] = "private, max-age=60";

            return Ok(actifsDTO);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche d'actifs de donnÃ©es avec le terme {Terme}", terme);
            return StatusCode(StatusCodes.Status500InternalServerError, "Une erreur est survenue lors de la recherche d'actifs de donnÃ©es");
        }
    }

    /// <summary>
    /// Ajoute un nouvel actif de donnÃ©es.
    /// </summary>
    /// <param name="actifDTO">DTO de l'actif de donnÃ©es Ã  ajouter.</param>
    /// <returns>L'actif de donnÃ©es ajoutÃ©.</returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ActifDonneesDTO>> Ajouter([FromBody] ActifDonneesCreationDTO actifDTO)
    {
        try
        {
            // Enregistrer les en-tÃªtes de la requÃªte pour le dÃ©bogage
            var headers = Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString());
            _logger.LogInformation("En-tÃªtes de la requÃªte: {Headers}", JsonSerializer.Serialize(headers));

            _logger.LogInformation("Tentative d'ajout d'un actif de donnÃ©es: {ActifDTO}", JsonSerializer.Serialize(actifDTO));

            if (actifDTO == null)
            {
                _logger.LogWarning("L'actif de donnÃ©es est null");
                return BadRequest("L'actif de donnÃ©es ne peut pas Ãªtre null.");
            }

            // VÃ©rifier la validitÃ© du modÃ¨le
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();

                _logger.LogWarning("Validation du modÃ¨le Ã©chouÃ©e: {Errors}", JsonSerializer.Serialize(errors));
                return BadRequest(new { Errors = errors });
            }

            // Journaliser les donnÃ©es reÃ§ues pour le dÃ©bogage
            _logger.LogInformation("DonnÃ©es reÃ§ues pour l'ajout d'un actif de donnÃ©es:");
            _logger.LogInformation("- Nom: {Nom}", actifDTO.Nom);
            _logger.LogInformation("- Description: {Description}", actifDTO.Description);
            _logger.LogInformation("- Type: {Type}", actifDTO.Type);
            _logger.LogInformation("- Format: {Format}", actifDTO.Format);
            _logger.LogInformation("- Source: {Source}", actifDTO.Source);
            _logger.LogInformation("- Proprietaire: {Proprietaire}", actifDTO.Proprietaire);
            _logger.LogInformation("- ClassificationSensibilite: {ClassificationSensibilite}", actifDTO.ClassificationSensibilite);
            _logger.LogInformation("- DateDerniereMiseAJour: {DateDerniereMiseAJour}", actifDTO.DateDerniereMiseAJour);
            _logger.LogInformation("- FrequenceMiseAJour: {FrequenceMiseAJour}", actifDTO.FrequenceMiseAJour);
            _logger.LogInformation("- CheminAcces: {CheminAcces}", actifDTO.CheminAcces);
            _logger.LogInformation("- DomaineGouvernanceId: {DomaineGouvernanceId}", actifDTO.DomaineGouvernanceId);
            _logger.LogInformation("- EstElementCritique: {EstElementCritique}", actifDTO.EstElementCritique);
            _logger.LogInformation("- ConnexionSourceDonneesId: {ConnexionSourceDonneesId}", actifDTO.ConnexionSourceDonneesId);
            _logger.LogInformation("- Statut: {Statut}", actifDTO.Statut);

            // VÃ©rifier les propriÃ©tÃ©s requises manuellement
            if (string.IsNullOrEmpty(actifDTO.Nom))
            {
                _logger.LogWarning("Le nom de l'actif de donnÃ©es est requis");
                return BadRequest("Le nom de l'actif de donnÃ©es est requis.");
            }

            // Convertir le DTO en entitÃ©
            var actif = new ActifDonnees
            {
                Nom = actifDTO.Nom,
                Description = actifDTO.Description,
                Proprietaire = actifDTO.Proprietaire,
                ClassificationSensibilite = actifDTO.ClassificationSensibilite,
                DateDerniereMiseAJour = actifDTO.DateDerniereMiseAJour,
                CheminAcces = actifDTO.CheminAcces,
                DomaineGouvernanceId = actifDTO.DomaineGouvernanceId,
                EstElementCritique = actifDTO.EstElementCritique,
                ConnexionSourceDonneesId = actifDTO.ConnexionSourceDonneesId,
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now,
                CreePar = "SystÃ¨me",
                ModifiePar = "SystÃ¨me"
            };

            // Journaliser les IDs reÃ§us
            _logger.LogInformation("IDs reÃ§us: TypeId={TypeId}, FormatId={FormatId}, SourceId={SourceId}, FrequenceId={FrequenceId}, StatutId={StatutId}",
                actifDTO.TypeActifDonneesId, actifDTO.FormatActifDonneesId, actifDTO.SourceActifDonneesId, actifDTO.FrequenceMiseAJourId, actifDTO.StatutActifDonneesId);

            // Utiliser directement les IDs s'ils sont fournis
            if (actifDTO.TypeActifDonneesId.HasValue && actifDTO.TypeActifDonneesId != Guid.Empty)
            {
                actif.TypeActifDonneesId = actifDTO.TypeActifDonneesId;
                _logger.LogInformation("TypeActifDonneesId dÃ©fini directement: {TypeId}", actifDTO.TypeActifDonneesId);
            }

            if (actifDTO.FormatActifDonneesId.HasValue && actifDTO.FormatActifDonneesId != Guid.Empty)
            {
                actif.FormatActifDonneesId = actifDTO.FormatActifDonneesId;
                _logger.LogInformation("FormatActifDonneesId dÃ©fini directement: {FormatId}", actifDTO.FormatActifDonneesId);
            }

            if (actifDTO.SourceActifDonneesId.HasValue && actifDTO.SourceActifDonneesId != Guid.Empty)
            {
                actif.SourceActifDonneesId = actifDTO.SourceActifDonneesId;
                _logger.LogInformation("SourceActifDonneesId dÃ©fini directement: {SourceId}", actifDTO.SourceActifDonneesId);
            }

            if (actifDTO.FrequenceMiseAJourId.HasValue && actifDTO.FrequenceMiseAJourId != Guid.Empty)
            {
                actif.FrequenceMiseAJourId = actifDTO.FrequenceMiseAJourId;
                _logger.LogInformation("FrequenceMiseAJourId dÃ©fini directement: {FrequenceId}", actifDTO.FrequenceMiseAJourId);
            }

            if (actifDTO.StatutActifDonneesId.HasValue && actifDTO.StatutActifDonneesId != Guid.Empty)
            {
                actif.StatutActifDonneesId = actifDTO.StatutActifDonneesId;
                _logger.LogInformation("StatutActifDonneesId dÃ©fini directement: {StatutId}", actifDTO.StatutActifDonneesId);
            }

            // Journaliser les valeurs reÃ§ues
            _logger.LogInformation("CrÃ©ation d'un actif de donnÃ©es avec les valeurs: Type={Type}, Format={Format}, Source={Source}, Frequence={Frequence}, Statut={Statut}, TypeId={TypeId}, FormatId={FormatId}, SourceId={SourceId}, FrequenceId={FrequenceId}, StatutId={StatutId}",
                actifDTO.Type, actifDTO.Format, actifDTO.Source, actifDTO.FrequenceMiseAJour, actifDTO.Statut,
                actifDTO.TypeActifDonneesId, actifDTO.FormatActifDonneesId, actifDTO.SourceActifDonneesId, actifDTO.FrequenceMiseAJourId, actifDTO.StatutActifDonneesId);

            // VÃ©rifier si les IDs sont dÃ©jÃ  dÃ©finis
            if (actif.TypeActifDonneesId == null || actif.TypeActifDonneesId == Guid.Empty)
            {
                // Rechercher le type d'actif de donnÃ©es par nom seulement si l'ID n'est pas dÃ©jÃ  dÃ©fini
                var types = await _serviceActifDonnees.ObtenirTypesActifDonneesAsync();
                _logger.LogInformation("Types disponibles: {Types}", JsonSerializer.Serialize(types.Select(t => new { t.Id, t.Nom })));

                if (!string.IsNullOrEmpty(actifDTO.Type))
                {
                    var type = types.FirstOrDefault(t => t.Nom.Equals(actifDTO.Type, StringComparison.OrdinalIgnoreCase));
                    if (type != null)
                    {
                        actif.TypeActifDonneesId = type.Id;
                        _logger.LogInformation("Type d'actif de donnÃ©es trouvÃ© par nom: {TypeId}", type.Id);
                    }
                    else
                    {
                        _logger.LogWarning("Type d'actif de donnÃ©es non trouvÃ©: {Type}", actifDTO.Type);
                        // Ne pas utiliser de valeur par dÃ©faut si un type spÃ©cifique a Ã©tÃ© demandÃ©
                    }
                }
            }
            else
            {
                _logger.LogInformation("TypeActifDonneesId dÃ©jÃ  dÃ©fini: {TypeId}", actif.TypeActifDonneesId);
            }

            // VÃ©rifier si le format est dÃ©jÃ  dÃ©fini
            if (actif.FormatActifDonneesId == null || actif.FormatActifDonneesId == Guid.Empty)
            {
                // Rechercher le format d'actif de donnÃ©es par nom seulement si l'ID n'est pas dÃ©jÃ  dÃ©fini
                var formats = await _serviceActifDonnees.ObtenirFormatsActifDonneesAsync();
                _logger.LogInformation("Formats disponibles: {Formats}", JsonSerializer.Serialize(formats.Select(f => new { f.Id, f.Nom })));

                if (!string.IsNullOrEmpty(actifDTO.Format))
                {
                    var format = formats.FirstOrDefault(f => f.Nom.Equals(actifDTO.Format, StringComparison.OrdinalIgnoreCase));
                    if (format != null)
                    {
                        actif.FormatActifDonneesId = format.Id;
                        _logger.LogInformation("Format d'actif de donnÃ©es trouvÃ©: {FormatId}", format.Id);
                    }
                    else
                    {
                        _logger.LogWarning("Format d'actif de donnÃ©es non trouvÃ©: {Format}", actifDTO.Format);
                        // Ne pas utiliser de valeur par dÃ©faut si un format spÃ©cifique a Ã©tÃ© demandÃ©
                    }
                }
            }
            else
            {
                _logger.LogInformation("FormatActifDonneesId dÃ©jÃ  dÃ©fini: {FormatId}", actif.FormatActifDonneesId);
            }

            // VÃ©rifier si la source est dÃ©jÃ  dÃ©finie
            if (actif.SourceActifDonneesId == null || actif.SourceActifDonneesId == Guid.Empty)
            {
                // Rechercher la source d'actif de donnÃ©es par nom seulement si l'ID n'est pas dÃ©jÃ  dÃ©fini
                var sources = await _serviceActifDonnees.ObtenirSourcesActifDonneesAsync();
                _logger.LogInformation("Sources disponibles: {Sources}", JsonSerializer.Serialize(sources.Select(s => new { s.Id, s.Nom })));

                if (!string.IsNullOrEmpty(actifDTO.Source))
                {
                    var source = sources.FirstOrDefault(s => s.Nom.Equals(actifDTO.Source, StringComparison.OrdinalIgnoreCase));
                    if (source != null)
                    {
                        actif.SourceActifDonneesId = source.Id;
                        _logger.LogInformation("Source d'actif de donnÃ©es trouvÃ©e: {SourceId}", source.Id);
                    }
                    else
                    {
                        _logger.LogWarning("Source d'actif de donnÃ©es non trouvÃ©e: {Source}", actifDTO.Source);
                        // Ne pas utiliser de valeur par dÃ©faut si une source spÃ©cifique a Ã©tÃ© demandÃ©e
                    }
                }
            }
            else
            {
                _logger.LogInformation("SourceActifDonneesId dÃ©jÃ  dÃ©fini: {SourceId}", actif.SourceActifDonneesId);
            }

            // VÃ©rifier si la frÃ©quence est dÃ©jÃ  dÃ©finie
            if (actif.FrequenceMiseAJourId == null || actif.FrequenceMiseAJourId == Guid.Empty)
            {
                // Rechercher la frÃ©quence de mise Ã  jour par nom seulement si l'ID n'est pas dÃ©jÃ  dÃ©fini
                var frequences = await _serviceActifDonnees.ObtenirFrequencesMiseAJourAsync();
                _logger.LogInformation("FrÃ©quences disponibles: {Frequences}", JsonSerializer.Serialize(frequences.Select(f => new { f.Id, f.Nom })));

                if (!string.IsNullOrEmpty(actifDTO.FrequenceMiseAJour))
                {
                    var frequence = frequences.FirstOrDefault(f => f.Nom.Equals(actifDTO.FrequenceMiseAJour, StringComparison.OrdinalIgnoreCase));
                    if (frequence != null)
                    {
                        actif.FrequenceMiseAJourId = frequence.Id;
                        _logger.LogInformation("FrÃ©quence de mise Ã  jour trouvÃ©e: {FrequenceId}", frequence.Id);
                    }
                    else
                    {
                        _logger.LogWarning("FrÃ©quence de mise Ã  jour non trouvÃ©e: {Frequence}", actifDTO.FrequenceMiseAJour);
                        // Ne pas utiliser de valeur par dÃ©faut si une frÃ©quence spÃ©cifique a Ã©tÃ© demandÃ©e
                    }
                }
            }
            else
            {
                _logger.LogInformation("FrequenceMiseAJourId dÃ©jÃ  dÃ©fini: {FrequenceId}", actif.FrequenceMiseAJourId);
            }

            // VÃ©rifier si le statut est dÃ©jÃ  dÃ©fini
            if (actif.StatutActifDonneesId == null || actif.StatutActifDonneesId == Guid.Empty)
            {
                // Rechercher le statut d'actif de donnÃ©es par nom seulement si l'ID n'est pas dÃ©jÃ  dÃ©fini
                var statuts = await _serviceActifDonnees.ObtenirStatutsActifDonneesAsync();
                _logger.LogInformation("Statuts disponibles: {Statuts}", JsonSerializer.Serialize(statuts.Select(s => new { s.Id, s.Nom })));

                if (!string.IsNullOrEmpty(actifDTO.Statut))
                {
                    var statut = statuts.FirstOrDefault(s => s.Nom.Equals(actifDTO.Statut, StringComparison.OrdinalIgnoreCase));
                    if (statut != null)
                    {
                        actif.StatutActifDonneesId = statut.Id;
                        _logger.LogInformation("Statut d'actif de donnÃ©es trouvÃ©: {StatutId}", statut.Id);
                    }
                    else
                    {
                        _logger.LogWarning("Statut d'actif de donnÃ©es non trouvÃ©: {Statut}", actifDTO.Statut);
                        // Ne pas utiliser de valeur par dÃ©faut si un statut spÃ©cifique a Ã©tÃ© demandÃ©
                    }
                }
                else
                {
                    // Si aucun statut n'est spÃ©cifiÃ©, utiliser "Brouillon" par dÃ©faut
                    var brouillon = statuts.FirstOrDefault(s => s.Nom.Equals("Brouillon", StringComparison.OrdinalIgnoreCase));
                    if (brouillon != null)
                    {
                        actif.StatutActifDonneesId = brouillon.Id;
                        _logger.LogInformation("Utilisation du statut Brouillon par dÃ©faut: {StatutId}", brouillon.Id);
                    }
                }
            }
            else
            {
                _logger.LogInformation("StatutActifDonneesId dÃ©jÃ  dÃ©fini: {StatutId}", actif.StatutActifDonneesId);
            }

            var actifAjoute = await _serviceActifDonnees.AjouterAsync(actif);
            var actifDTOResult = MapToDTO(actifAjoute);

            return CreatedAtAction(nameof(ObtenirParId), new { id = actifAjoute.Id }, actifDTOResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout de l'actif de donnÃ©es");
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de l'ajout de l'actif de donnÃ©es: {ex.Message}");
        }
    }

    /// <summary>
    /// Met Ã  jour un actif de donnÃ©es existant.
    /// </summary>
    /// <param name="id">Identifiant de l'actif de donnÃ©es.</param>
    /// <param name="actifDTO">DTO de l'actif de donnÃ©es Ã  mettre Ã  jour.</param>
    /// <returns>Aucun contenu si la mise Ã  jour est rÃ©ussie.</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> MettreAJour(Guid id, ActifDonneesMiseAJourDTO actifDTO)
    {
        try
        {
            _logger.LogInformation("Tentative de mise Ã  jour de l'actif de donnÃ©es {Id}: {ActifDTO}", id, JsonSerializer.Serialize(actifDTO));

            if (actifDTO == null)
            {
                return BadRequest("L'actif de donnÃ©es ne peut pas Ãªtre null.");
            }

            var actifExistant = await _serviceActifDonnees.ObtenirParIdAsync(id);
            if (actifExistant == null)
            {
                return NotFound($"L'actif de donnÃ©es avec l'identifiant {id} n'a pas Ã©tÃ© trouvÃ©.");
            }

            // Mettre Ã  jour les propriÃ©tÃ©s de l'actif existant
            if (!string.IsNullOrEmpty(actifDTO.Nom))
            {
                actifExistant.Nom = actifDTO.Nom;
            }

            if (actifDTO.Description != null)
            {
                actifExistant.Description = actifDTO.Description;
            }

            if (actifDTO.Proprietaire != null)
            {
                actifExistant.Proprietaire = actifDTO.Proprietaire;
            }

            if (actifDTO.ClassificationSensibilite.HasValue)
            {
                actifExistant.ClassificationSensibilite = actifDTO.ClassificationSensibilite.Value;
            }

            if (actifDTO.DateDerniereMiseAJour.HasValue)
            {
                actifExistant.DateDerniereMiseAJour = actifDTO.DateDerniereMiseAJour;
            }

            if (actifDTO.CheminAcces != null)
            {
                actifExistant.CheminAcces = actifDTO.CheminAcces;
            }

            if (actifDTO.DomaineGouvernanceId.HasValue)
            {
                actifExistant.DomaineGouvernanceId = actifDTO.DomaineGouvernanceId;
            }

            if (actifDTO.EstElementCritique.HasValue)
            {
                actifExistant.EstElementCritique = actifDTO.EstElementCritique.Value;
            }

            if (actifDTO.ConnexionSourceDonneesId.HasValue)
            {
                actifExistant.ConnexionSourceDonneesId = actifDTO.ConnexionSourceDonneesId;
            }

            // Mise Ã  jour des propriÃ©tÃ©s de rÃ©fÃ©rence
            _logger.LogInformation("Mise Ã  jour d'un actif de donnÃ©es avec les valeurs: Type={Type}, Format={Format}, Source={Source}, Frequence={Frequence}, Statut={Statut}",
                actifDTO.Type, actifDTO.Format, actifDTO.Source, actifDTO.FrequenceMiseAJour, actifDTO.Statut);

            // Mise Ã  jour du type
            if (!string.IsNullOrEmpty(actifDTO.Type))
            {
                var types = await _serviceActifDonnees.ObtenirTypesActifDonneesAsync();
                var type = types.FirstOrDefault(t => t.Nom.Equals(actifDTO.Type, StringComparison.OrdinalIgnoreCase));
                if (type != null)
                {
                    actifExistant.TypeActifDonneesId = type.Id;
                    actifExistant.TypeActifDonnees = type;
                    _logger.LogInformation("Type d'actif de donnÃ©es mis Ã  jour: {TypeId} - {TypeNom}", type.Id, type.Nom);
                }
            }

            // Mise Ã  jour du format
            if (!string.IsNullOrEmpty(actifDTO.Format))
            {
                var formats = await _serviceActifDonnees.ObtenirFormatsActifDonneesAsync();
                var format = formats.FirstOrDefault(f => f.Nom.Equals(actifDTO.Format, StringComparison.OrdinalIgnoreCase));
                if (format != null)
                {
                    actifExistant.FormatActifDonneesId = format.Id;
                    actifExistant.FormatActifDonnees = format;
                    _logger.LogInformation("Format d'actif de donnÃ©es mis Ã  jour: {FormatId} - {FormatNom}", format.Id, format.Nom);
                }
            }

            // Mise Ã  jour de la source
            if (!string.IsNullOrEmpty(actifDTO.Source))
            {
                var sources = await _serviceActifDonnees.ObtenirSourcesActifDonneesAsync();
                var source = sources.FirstOrDefault(s => s.Nom.Equals(actifDTO.Source, StringComparison.OrdinalIgnoreCase));
                if (source != null)
                {
                    actifExistant.SourceActifDonneesId = source.Id;
                    actifExistant.SourceActifDonnees = source;
                    _logger.LogInformation("Source d'actif de donnÃ©es mise Ã  jour: {SourceId} - {SourceNom}", source.Id, source.Nom);
                }
            }

            // Mise Ã  jour de la frÃ©quence de mise Ã  jour
            if (!string.IsNullOrEmpty(actifDTO.FrequenceMiseAJour))
            {
                var frequences = await _serviceActifDonnees.ObtenirFrequencesMiseAJourAsync();
                var frequence = frequences.FirstOrDefault(f => f.Nom.Equals(actifDTO.FrequenceMiseAJour, StringComparison.OrdinalIgnoreCase));
                if (frequence != null)
                {
                    actifExistant.FrequenceMiseAJourId = frequence.Id;
                    actifExistant.FrequenceMiseAJour = frequence;
                    _logger.LogInformation("FrÃ©quence de mise Ã  jour mise Ã  jour: {FrequenceId} - {FrequenceNom}", frequence.Id, frequence.Nom);
                }
            }

            // Mise Ã  jour du statut
            if (!string.IsNullOrEmpty(actifDTO.Statut))
            {
                var statuts = await _serviceActifDonnees.ObtenirStatutsActifDonneesAsync();
                var statut = statuts.FirstOrDefault(s => s.Nom.Equals(actifDTO.Statut, StringComparison.OrdinalIgnoreCase));
                if (statut != null)
                {
                    actifExistant.StatutActifDonneesId = statut.Id;
                    actifExistant.StatutActifDonnees = statut;
                    _logger.LogInformation("Statut d'actif de donnÃ©es mis Ã  jour: {StatutId} - {StatutNom}", statut.Id, statut.Nom);
                }
            }

            await _serviceActifDonnees.MettreAJourAsync(actifExistant);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise Ã  jour de l'actif de donnÃ©es {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de la mise Ã  jour de l'actif de donnÃ©es: {ex.Message}");
        }
    }

    /// <summary>
    /// Supprime un actif de donnÃ©es.
    /// </summary>
    /// <param name="id">Identifiant de l'actif de donnÃ©es Ã  supprimer.</param>
    /// <returns>Aucun contenu si la suppression est rÃ©ussie.</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        try
        {
            _logger.LogInformation("Tentative de suppression de l'actif de donnÃ©es {Id}", id);

            var actifExistant = await _serviceActifDonnees.ObtenirParIdAsync(id);
            if (actifExistant == null)
            {
                return NotFound($"L'actif de donnÃ©es avec l'identifiant {id} n'a pas Ã©tÃ© trouvÃ©.");
            }

            var resultat = await _serviceActifDonnees.SupprimerAsync(id);
            if (resultat)
            {
                return NoContent();
            }
            else
            {
                return StatusCode(StatusCodes.Status500InternalServerError, "La suppression de l'actif de donnÃ©es a Ã©chouÃ©.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'actif de donnÃ©es {Id}", id);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de la suppression de l'actif de donnÃ©es: {ex.Message}");
        }
    }

    /// <summary>
    /// Endpoint de test pour vÃ©rifier que le contrÃ´leur fonctionne correctement.
    /// </summary>
    /// <returns>Un message de test.</returns>
    [HttpGet("test")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public IActionResult Test()
    {
        _logger.LogInformation("Test du contrÃ´leur ActifDonneesV2");

        // VÃ©rifier que les services sont correctement injectÃ©s
        if (_serviceActifDonnees == null)
        {
            _logger.LogError("Le service ActifDonnees est null");
            return StatusCode(StatusCodes.Status500InternalServerError, "Le service ActifDonnees est null");
        }

        return Ok(new { Message = "Le contrÃ´leur ActifDonneesV2 fonctionne correctement",
                        Date = DateTime.Now,
                        ServiceActifDonnees = _serviceActifDonnees.GetType().FullName });
    }

    /// <summary>
    /// Endpoint de test pour vÃ©rifier que les donnÃ©es de rÃ©fÃ©rence sont correctement chargÃ©es.
    /// </summary>
    /// <returns>Les donnÃ©es de rÃ©fÃ©rence.</returns>
    [HttpGet("test-references")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> TestReferences()
    {
        _logger.LogInformation("Test des donnÃ©es de rÃ©fÃ©rence");

        try
        {
            var types = await _serviceActifDonnees.ObtenirTypesActifDonneesAsync();
            var formats = await _serviceActifDonnees.ObtenirFormatsActifDonneesAsync();
            var sources = await _serviceActifDonnees.ObtenirSourcesActifDonneesAsync();
            var frequences = await _serviceActifDonnees.ObtenirFrequencesMiseAJourAsync();
            var statuts = await _serviceActifDonnees.ObtenirStatutsActifDonneesAsync();

            return Ok(new {
                Types = types.Select(t => new { t.Id, t.Nom }).ToList(),
                Formats = formats.Select(f => new { f.Id, f.Nom }).ToList(),
                Sources = sources.Select(s => new { s.Id, s.Nom }).ToList(),
                Frequences = frequences.Select(f => new { f.Id, f.Nom }).ToList(),
                Statuts = statuts.Select(s => new { s.Id, s.Nom }).ToList()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du test des donnÃ©es de rÃ©fÃ©rence");
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors du test des donnÃ©es de rÃ©fÃ©rence: {ex.Message}");
        }
    }

    /// <summary>
    /// Endpoint de test pour ajouter un actif de donnÃ©es avec des valeurs par dÃ©faut.
    /// </summary>
    /// <returns>L'actif de donnÃ©es ajoutÃ©.</returns>
    [HttpGet("test-ajouter")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<ActifDonneesDTO>> TestAjouter()
    {
        try
        {
            _logger.LogInformation("Test d'ajout d'un actif de donnÃ©es avec des valeurs par dÃ©faut");

            // CrÃ©er un DTO avec des valeurs par dÃ©faut
            var actifDTO = new ActifDonneesCreationDTO
            {
                Nom = "Test Actif " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                Description = "Description de test",
                Type = "Fichier",
                Format = "CSV",
                Source = "Interne",
                Proprietaire = "Test",
                ClassificationSensibilite = ClassificationSensibilite.Public,
                DateDerniereMiseAJour = DateTime.Now,
                FrequenceMiseAJour = "Quotidienne",
                CheminAcces = "/test/chemin",
                EstElementCritique = false,
                Statut = "Brouillon"
            };

            _logger.LogInformation("DTO de test: {ActifDTO}", JsonSerializer.Serialize(actifDTO));

            // Convertir le DTO en entitÃ©
            var actif = new ActifDonnees
            {
                Nom = actifDTO.Nom,
                Description = actifDTO.Description,
                Proprietaire = actifDTO.Proprietaire,
                ClassificationSensibilite = actifDTO.ClassificationSensibilite,
                DateDerniereMiseAJour = actifDTO.DateDerniereMiseAJour,
                CheminAcces = actifDTO.CheminAcces,
                EstElementCritique = actifDTO.EstElementCritique
            };

            // Rechercher les entitÃ©s de rÃ©fÃ©rence
            if (!string.IsNullOrEmpty(actifDTO.Type))
            {
                var types = await _serviceActifDonnees.ObtenirTypesActifDonneesAsync();
                _logger.LogInformation("Types disponibles: {Types}", JsonSerializer.Serialize(types.Select(t => new { t.Id, t.Nom })));

                var type = types.FirstOrDefault(t => t.Nom.Equals(actifDTO.Type, StringComparison.OrdinalIgnoreCase));
                if (type != null)
                {
                    actif.TypeActifDonneesId = type.Id;
                }
                else if (types.Any())
                {
                    actif.TypeActifDonneesId = types.First().Id;
                }
            }

            if (!string.IsNullOrEmpty(actifDTO.Format))
            {
                var formats = await _serviceActifDonnees.ObtenirFormatsActifDonneesAsync();
                var format = formats.FirstOrDefault(f => f.Nom.Equals(actifDTO.Format, StringComparison.OrdinalIgnoreCase));
                if (format != null)
                {
                    actif.FormatActifDonneesId = format.Id;
                }
                else if (formats.Any())
                {
                    actif.FormatActifDonneesId = formats.First().Id;
                }
            }

            if (!string.IsNullOrEmpty(actifDTO.Source))
            {
                var sources = await _serviceActifDonnees.ObtenirSourcesActifDonneesAsync();
                var source = sources.FirstOrDefault(s => s.Nom.Equals(actifDTO.Source, StringComparison.OrdinalIgnoreCase));
                if (source != null)
                {
                    actif.SourceActifDonneesId = source.Id;
                }
                else if (sources.Any())
                {
                    actif.SourceActifDonneesId = sources.First().Id;
                }
            }

            if (!string.IsNullOrEmpty(actifDTO.FrequenceMiseAJour))
            {
                var frequences = await _serviceActifDonnees.ObtenirFrequencesMiseAJourAsync();
                var frequence = frequences.FirstOrDefault(f => f.Nom.Equals(actifDTO.FrequenceMiseAJour, StringComparison.OrdinalIgnoreCase));
                if (frequence != null)
                {
                    actif.FrequenceMiseAJourId = frequence.Id;
                }
                else if (frequences.Any())
                {
                    actif.FrequenceMiseAJourId = frequences.First().Id;
                }
            }

            if (!string.IsNullOrEmpty(actifDTO.Statut))
            {
                var statuts = await _serviceActifDonnees.ObtenirStatutsActifDonneesAsync();
                var statut = statuts.FirstOrDefault(s => s.Nom.Equals(actifDTO.Statut, StringComparison.OrdinalIgnoreCase));
                if (statut != null)
                {
                    actif.StatutActifDonneesId = statut.Id;
                }
                else if (statuts.Any())
                {
                    actif.StatutActifDonneesId = statuts.First().Id;
                }
            }

            var actifAjoute = await _serviceActifDonnees.AjouterAsync(actif);
            var actifDTOResult = MapToDTO(actifAjoute);

            return CreatedAtAction(nameof(ObtenirParId), new { id = actifAjoute.Id }, actifDTOResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du test d'ajout d'un actif de donnÃ©es");
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors du test d'ajout d'un actif de donnÃ©es: {ex.Message}");
        }
    }

    /// <summary>
    /// Endpoint de debug pour ajouter un actif de donnÃ©es.
    /// </summary>
    /// <param name="actifDTO">DTO de l'actif de donnÃ©es Ã  ajouter.</param>
    /// <returns>L'actif de donnÃ©es ajoutÃ©.</returns>
    [HttpPost("debug-ajouter")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<object>> DebugAjouter([FromBody] object actifDTO)
    {
        try
        {
            _logger.LogInformation("Debug d'ajout d'un actif de donnÃ©es: {ActifDTO}", JsonSerializer.Serialize(actifDTO));

            // RÃ©cupÃ©rer les donnÃ©es de rÃ©fÃ©rence
            var types = await _serviceActifDonnees.ObtenirTypesActifDonneesAsync();
            var formats = await _serviceActifDonnees.ObtenirFormatsActifDonneesAsync();
            var sources = await _serviceActifDonnees.ObtenirSourcesActifDonneesAsync();
            var frequences = await _serviceActifDonnees.ObtenirFrequencesMiseAJourAsync();
            var statuts = await _serviceActifDonnees.ObtenirStatutsActifDonneesAsync();

            // CrÃ©er un actif de donnÃ©es par dÃ©faut
            var actif = new ActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Debug Actif " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                Description = "Description de debug",
                Proprietaire = "Debug",
                ClassificationSensibilite = ClassificationSensibilite.Public,
                DateDerniereMiseAJour = DateTime.Now,
                CheminAcces = "/debug/chemin",
                EstElementCritique = false,
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now,
                CreePar = "Debug",
                ModifiePar = "Debug"
            };

            // Assigner des valeurs par dÃ©faut pour les rÃ©fÃ©rences
            if (types.Any())
            {
                actif.TypeActifDonneesId = types.First().Id;
            }

            if (formats.Any())
            {
                actif.FormatActifDonneesId = formats.First().Id;
            }

            if (sources.Any())
            {
                actif.SourceActifDonneesId = sources.First().Id;
            }

            if (frequences.Any())
            {
                actif.FrequenceMiseAJourId = frequences.First().Id;
            }

            if (statuts.Any())
            {
                actif.StatutActifDonneesId = statuts.First().Id;
            }

            // Ajouter l'actif de donnÃ©es
            var actifAjoute = await _serviceActifDonnees.AjouterAsync(actif);

            // Retourner les informations de debug
            return Ok(new
            {
                RequestBody = actifDTO,
                CreatedActif = actifAjoute,
                References = new
                {
                    Types = types.Select(t => new { t.Id, t.Nom }).ToList(),
                    Formats = formats.Select(f => new { f.Id, f.Nom }).ToList(),
                    Sources = sources.Select(s => new { s.Id, s.Nom }).ToList(),
                    Frequences = frequences.Select(f => new { f.Id, f.Nom }).ToList(),
                    Statuts = statuts.Select(s => new { s.Id, s.Nom }).ToList()
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du debug d'ajout d'un actif de donnÃ©es");
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors du debug d'ajout d'un actif de donnÃ©es: {ex.Message}");
        }
    }

    /// <summary>
    /// Met Ã  jour tous les actifs de donnÃ©es sans type ou source avec des valeurs par dÃ©faut.
    /// </summary>
    /// <returns>Le nombre d'actifs de donnÃ©es mis Ã  jour.</returns>
    [HttpPost("mettreAJourValeursParDefaut")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<int>> MettreAJourValeursParDefaut()
    {
        try
        {
            _logger.LogInformation("Tentative de mise Ã  jour des valeurs par dÃ©faut pour tous les actifs de donnÃ©es");

            var nombreMisesAJour = await _serviceActifDonnees.MettreAJourValeursParDefautAsync();

            _logger.LogInformation("{NombreMisesAJour} actifs de donnÃ©es ont Ã©tÃ© mis Ã  jour avec des valeurs par dÃ©faut", nombreMisesAJour);

            return Ok(nombreMisesAJour);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise Ã  jour des valeurs par dÃ©faut pour les actifs de donnÃ©es");
            return StatusCode(StatusCodes.Status500InternalServerError, $"Une erreur est survenue lors de la mise Ã  jour des valeurs par dÃ©faut: {ex.Message}");
        }
    }

    /// <summary>
    /// Endpoint temporaire pour diagnostiquer les types d'actifs.
    /// </summary>
    [HttpGet("debug/types")]
    public async Task<ActionResult> DebugTypes()
    {
        try
        {
            // Obtenir tous les types d'actifs
            var types = await _serviceActifDonnees.ObtenirTypesActifDonneesAsync();

            var result = new
            {
                TotalTypes = types.Count(),
                Types = types.Select(t => new { t.Id, t.Nom, t.Description }).ToList()
            };

            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erreur: {ex.Message}");
        }
    }

    /// <summary>
    /// Endpoint temporaire pour assigner un type à un actif.
    /// </summary>
    [HttpPost("{id:guid}/assign-type/{typeNom}")]
    public async Task<ActionResult> AssignerType(Guid id, string typeNom)
    {
        try
        {
            // Trouver le type par nom
            var types = await _serviceActifDonnees.ObtenirTypesActifDonneesAsync();
            var type = types.FirstOrDefault(t => t.Nom == typeNom);

            if (type == null)
            {
                return NotFound($"Type '{typeNom}' non trouvé");
            }

            // Trouver l'actif
            var actif = await _serviceActifDonnees.ObtenirParIdAsync(id);

            if (actif == null)
            {
                return NotFound($"Actif '{id}' non trouvé");
            }

            // Assigner le type
            actif.TypeActifDonneesId = type.Id;
            await _serviceActifDonnees.MettreAJourAsync(actif);

            return Ok($"Type '{typeNom}' assigné à l'actif '{actif.Nom}'");
        }
        catch (Exception ex)
        {
            return StatusCode(500, $"Erreur: {ex.Message}");
        }
    }

    /// <summary>
    /// Exécute toutes les règles de qualité pour un actif de données.
    /// </summary>
    /*[HttpPost("{id:guid}/execute-quality-rules")]
    public async Task<ActionResult> ExecuterReglesQualite(Guid id)
    {
        try
        {
            _logger.LogInformation("Exécution des règles de qualité pour l'actif {ActifId}", id);

            var resultats = await _serviceExecutionRegleQualite.ExecuterToutesReglesAsync(id);

            var response = new
            {
                ActifId = id,
                NombreReglesExecutees = resultats.Count(),
                Resultats = resultats.Select(r => new
                {
                    r.Id,
                    r.RegleQualiteId,
                    r.ValeurMesuree,
                    Statut = r.Statut.ToString(),
                    r.Message,
                    r.DateExecution
                })
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'exécution des règles de qualité pour l'actif {ActifId}", id);
            return StatusCode(500, $"Erreur: {ex.Message}");
        }
    }

    /// <summary>
    /// Exécute une règle de qualité spécifique.
    /// </summary>
    [HttpPost("execute-rule/{regleId:guid}")]
    public async Task<ActionResult> ExecuterRegleQualite(Guid regleId)
    {
        try
        {
            _logger.LogInformation("Exécution de la règle de qualité {RegleId}", regleId);

            var resultat = await _serviceExecutionRegleQualite.ExecuterRegleAsync(regleId);

            var response = new
            {
                resultat.Id,
                resultat.RegleQualiteId,
                resultat.ValeurMesuree,
                Statut = resultat.Statut.ToString(),
                resultat.Message,
                resultat.DateExecution
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'exécution de la règle de qualité {RegleId}", regleId);
            return StatusCode(500, $"Erreur: {ex.Message}");
        }
    }*/

    /// <summary>
    /// Obtient les politiques associées à un actif de données.
    /// </summary>
    /// <param name="id">Identifiant de l'actif de données.</param>
    /// <returns>Liste des politiques associées.</returns>
    [HttpGet("{id:guid}/politiques")]
    [ProducesResponseType(typeof(IEnumerable<object>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> ObtenirPolitiquesAssociees(Guid id)
    {
        try
        {
            var actif = await _serviceActifDonnees.ObtenirParIdAvecEntitesLieesAsync(id);
            if (actif == null)
            {
                return NotFound($"Actif de données avec l'ID {id} non trouvé");
            }

            var politiques = actif.Politiques?.Select(p => new
            {
                p.Id,
                p.Code,
                p.Titre,
                p.Categorie,
                p.Statut,
                p.Version,
                p.DateEntreeVigueur,
                p.DateExpiration,
                p.EstActive
            }) ?? Enumerable.Empty<object>();

            return Ok(politiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des politiques pour l'actif {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}
