using Microsoft.AspNetCore.Mvc;
using DataHubGatineau.Domain.Entities.Metadata;
using DataHubGatineau.Domain.Interfaces;
using Asp.Versioning;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour la gestion des schémas de métadonnées.
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
public class SchemasMetadonneesController : ControllerBase
{
    private readonly IDepotSchemaMetadonnees _depotSchemaMetadonnees;
    private readonly IDepotDefinitionMetadonnee _depotDefinitionMetadonnee;
    private readonly ILogger<SchemasMetadonneesController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="SchemasMetadonneesController"/>.
    /// </summary>
    public SchemasMetadonneesController(
        IDepotSchemaMetadonnees depotSchemaMetadonnees,
        IDepotDefinitionMetadonnee depotDefinitionMetadonnee,
        ILogger<SchemasMetadonneesController> logger)
    {
        _depotSchemaMetadonnees = depotSchemaMetadonnees;
        _depotDefinitionMetadonnee = depotDefinitionMetadonnee;
        _logger = logger;
    }

    /// <summary>
    /// Obtient tous les schémas de métadonnées.
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<SchemaMetadonnees>>> ObtenirTous()
    {
        try
        {
            var schemas = await _depotSchemaMetadonnees.ObtenirTousAvecDefinitionsAsync();
            return Ok(schemas);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des schémas de métadonnées");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient un schéma de métadonnées par son identifiant.
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<SchemaMetadonnees>> ObtenirParId(Guid id)
    {
        try
        {
            var schema = await _depotSchemaMetadonnees.ObtenirParIdAvecDefinitionsAsync(id);
            if (schema == null)
            {
                return NotFound($"Schéma avec l'ID {id} non trouvé");
            }
            return Ok(schema);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du schéma {SchemaId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les schémas de métadonnées par type d'actif.
    /// </summary>
    [HttpGet("type-actif/{typeActif}")]
    public async Task<ActionResult<IEnumerable<SchemaMetadonnees>>> ObtenirParTypeActif(string typeActif)
    {
        try
        {
            var schemas = await _depotSchemaMetadonnees.ObtenirParTypeActifAsync(typeActif);
            return Ok(schemas);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des schémas pour le type d'actif {TypeActif}", typeActif);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les schémas de métadonnées actifs.
    /// </summary>
    [HttpGet("actifs")]
    public async Task<ActionResult<IEnumerable<SchemaMetadonnees>>> ObtenirActifs()
    {
        try
        {
            var schemas = await _depotSchemaMetadonnees.ObtenirActifsAsync();
            return Ok(schemas);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des schémas actifs");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Crée un schéma de métadonnées par défaut pour le type "Table" (endpoint temporaire).
    /// </summary>
    [HttpPost("creer-schema-table")]
    public async Task<ActionResult<SchemaMetadonnees>> CreerSchemaTable()
    {
        try
        {
            // Vérifier si un schéma actif existe déjà pour le type "Table"
            var schemaExistant = await _depotSchemaMetadonnees.ObtenirActifParTypeActifAsync("Table");
            if (schemaExistant != null)
            {
                return Ok(schemaExistant);
            }

            // Créer le schéma de métadonnées pour le type "Table"
            var schema = new SchemaMetadonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Schéma Standard pour Tables",
                Description = "Schéma de métadonnées standard pour les tables de base de données, incluant les informations de statut, qualité et gouvernance.",
                TypeActif = "Table",
                Version = "1.0",
                EstActif = true,
                DateCreation = DateTime.UtcNow,
                DateModification = DateTime.UtcNow,
                CreePar = "System",
                ModifiePar = "System"
            };

            // Créer les définitions de métadonnées
            var definitions = new List<DefinitionMetadonnee>
            {
                new() {
                    Id = Guid.NewGuid(),
                    SchemaMetadonneesId = schema.Id,
                    Nom = "Statut Document",
                    Description = "Statut du document ou de la table dans le cycle de vie (Brouillon, En révision, Approuvé, Archivé)",
                    TypeDonnee = "Liste",
                    EstObligatoire = true,
                    EstCalcule = false,
                    ValeurParDefaut = "Brouillon",
                    ReglesValidation = "[\"Brouillon\", \"En révision\", \"Approuvé\", \"Publié\", \"Archivé\"]",
                    Ordre = 1,
                    DateCreation = DateTime.UtcNow,
                    DateModification = DateTime.UtcNow,
                    CreePar = "System",
                    ModifiePar = "System"
                },
                new() {
                    Id = Guid.NewGuid(),
                    SchemaMetadonneesId = schema.Id,
                    Nom = "Niveau de Qualité",
                    Description = "Niveau de qualité des données dans cette table (Bronze, Argent, Or)",
                    TypeDonnee = "Liste",
                    EstObligatoire = true,
                    EstCalcule = false,
                    ValeurParDefaut = "Bronze",
                    ReglesValidation = "[\"Bronze\", \"Argent\", \"Or\"]",
                    Ordre = 2,
                    DateCreation = DateTime.UtcNow,
                    DateModification = DateTime.UtcNow,
                    CreePar = "System",
                    ModifiePar = "System"
                },
                new() {
                    Id = Guid.NewGuid(),
                    SchemaMetadonneesId = schema.Id,
                    Nom = "Criticité Métier",
                    Description = "Niveau de criticité de cette table pour les opérations métier",
                    TypeDonnee = "Liste",
                    EstObligatoire = true,
                    EstCalcule = false,
                    ValeurParDefaut = "Moyenne",
                    ReglesValidation = "[\"Faible\", \"Moyenne\", \"Élevée\", \"Critique\"]",
                    Ordre = 3,
                    DateCreation = DateTime.UtcNow,
                    DateModification = DateTime.UtcNow,
                    CreePar = "System",
                    ModifiePar = "System"
                }
            };

            schema.DefinitionsMetadonnees = definitions;

            await _depotSchemaMetadonnees.AjouterAsync(schema);

            _logger.LogInformation("Schéma de métadonnées créé pour le type 'Table' avec {Count} définitions", definitions.Count);

            return CreatedAtAction(nameof(ObtenirParId), new { id = schema.Id }, schema);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du schéma pour le type 'Table'");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient le schéma de métadonnées actif pour un type d'actif.
    /// </summary>
    [HttpGet("actif/type-actif/{typeActif}")]
    public async Task<ActionResult<SchemaMetadonnees>> ObtenirActifParTypeActif(string typeActif)
    {
        try
        {
            var schema = await _depotSchemaMetadonnees.ObtenirActifParTypeActifAsync(typeActif);
            if (schema == null)
            {
                return NotFound($"Aucun schéma actif trouvé pour le type d'actif {typeActif}");
            }
            return Ok(schema);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération du schéma actif pour le type d'actif {TypeActif}", typeActif);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Crée un nouveau schéma de métadonnées.
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<SchemaMetadonnees>> Creer([FromBody] SchemaMetadonnees schema)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            schema.Id = Guid.NewGuid();
            schema.DateCreation = DateTime.UtcNow;
            schema.DateModification = DateTime.UtcNow;
            schema.CreePar = "Utilisateur"; // TODO: Obtenir l'utilisateur actuel du contexte
            schema.ModifiePar = "Utilisateur"; // TODO: Obtenir l'utilisateur actuel du contexte

            var schemaCreated = await _depotSchemaMetadonnees.AjouterAsync(schema);
            return CreatedAtAction(nameof(ObtenirParId), new { id = schemaCreated.Id }, schemaCreated);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création du schéma de métadonnées");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Met à jour un schéma de métadonnées.
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<SchemaMetadonnees>> MettreAJour(Guid id, [FromBody] SchemaMetadonnees schema)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var schemaExistant = await _depotSchemaMetadonnees.ObtenirParIdAsync(id);
            if (schemaExistant == null)
            {
                return NotFound($"Schéma avec l'ID {id} non trouvé");
            }

            // Actualiser les propriétés de l'entité existante (évite le conflit de tracking)
            schemaExistant.Nom = schema.Nom;
            schemaExistant.Description = schema.Description;
            schemaExistant.TypeActif = schema.TypeActif;
            schemaExistant.Version = schema.Version;
            schemaExistant.EstActif = schema.EstActif;
            schemaExistant.ModifiePar = schema.ModifiePar;
            schemaExistant.DateModification = DateTime.UtcNow;

            // Sauvegarder directement sans passer par MettreAJourAsync (entité déjà trackée)
            await _depotSchemaMetadonnees.SauvegarderChangementsAsync();
            return Ok(schemaExistant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du schéma {SchemaId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Active un schéma de métadonnées.
    /// </summary>
    [HttpPost("{id}/activer")]
    public async Task<ActionResult<SchemaMetadonnees>> Activer(Guid id)
    {
        try
        {
            var schema = await _depotSchemaMetadonnees.ActiverSchemaAsync(id);
            return Ok(schema);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'activation du schéma {SchemaId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Crée une nouvelle version d'un schéma de métadonnées.
    /// </summary>
    [HttpPost("{id}/nouvelle-version")]
    public async Task<ActionResult<SchemaMetadonnees>> CreerNouvelleVersion(Guid id, [FromBody] string nouvelleVersion)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(nouvelleVersion))
            {
                return BadRequest("La nouvelle version est requise");
            }

            var nouveauSchema = await _depotSchemaMetadonnees.CreerNouvelleVersionAsync(id, nouvelleVersion);
            return CreatedAtAction(nameof(ObtenirParId), new { id = nouveauSchema.Id }, nouveauSchema);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création d'une nouvelle version du schéma {SchemaId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Supprime un schéma de métadonnées.
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Supprimer(Guid id)
    {
        try
        {
            var peutEtreSupprime = await _depotSchemaMetadonnees.PeutEtreSupprime(id);
            if (!peutEtreSupprime)
            {
                return BadRequest("Le schéma actif ne peut pas être supprimé");
            }

            var supprime = await _depotSchemaMetadonnees.SupprimerAsync(id);
            if (!supprime)
            {
                return NotFound($"Schéma avec l'ID {id} non trouvé");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du schéma {SchemaId}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les types d'actifs ayant des schémas.
    /// </summary>
    [HttpGet("types-actifs")]
    public async Task<ActionResult<IEnumerable<string>>> ObtenirTypesActifs()
    {
        try
        {
            var typesActifs = await _depotSchemaMetadonnees.ObtenirTypesActifsAsync();
            return Ok(typesActifs);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des types d'actifs");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les statistiques des schémas de métadonnées.
    /// </summary>
    [HttpGet("statistiques")]
    public async Task<ActionResult<Dictionary<string, int>>> ObtenirStatistiques()
    {
        try
        {
            var statistiques = await _depotSchemaMetadonnees.ObtenirStatistiquesAsync();
            return Ok(statistiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    // ===== GESTION DES DÉFINITIONS DE MÉTADONNÉES =====

    /// <summary>
    /// Obtient les définitions de métadonnées pour un schéma.
    /// </summary>
    [HttpGet("{schemaId}/definitions")]
    public async Task<ActionResult<IEnumerable<DefinitionMetadonnee>>> ObtenirDefinitions(Guid schemaId)
    {
        try
        {
            var definitions = await _depotDefinitionMetadonnee.ObtenirParSchemaAsync(schemaId);
            return Ok(definitions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des définitions pour le schéma {SchemaId}", schemaId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Ajoute une définition de métadonnée à un schéma.
    /// </summary>
    [HttpPost("{schemaId}/definitions")]
    public async Task<ActionResult<DefinitionMetadonnee>> AjouterDefinition(Guid schemaId, [FromBody] DefinitionMetadonnee definition)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Vérifier que le schéma existe
            var schema = await _depotSchemaMetadonnees.ObtenirParIdAsync(schemaId);
            if (schema == null)
            {
                return NotFound($"Schéma avec l'ID {schemaId} non trouvé");
            }

            definition.Id = Guid.NewGuid();
            definition.SchemaMetadonneesId = schemaId;
            definition.DateCreation = DateTime.UtcNow;
            definition.DateModification = DateTime.UtcNow;
            definition.CreePar = "Utilisateur"; // TODO: Obtenir l'utilisateur actuel du contexte
            definition.ModifiePar = "Utilisateur"; // TODO: Obtenir l'utilisateur actuel du contexte

            // Obtenir le prochain ordre si non spécifié
            if (definition.Ordre == 0)
            {
                definition.Ordre = await _depotDefinitionMetadonnee.ObtenirProchainOrdreAsync(schemaId);
            }

            var definitionCreated = await _depotDefinitionMetadonnee.AjouterAsync(definition);
            return CreatedAtAction(nameof(ObtenirDefinitionParId), new { schemaId, definitionId = definitionCreated.Id }, definitionCreated);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout de la définition au schéma {SchemaId}", schemaId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient une définition de métadonnée par son identifiant.
    /// </summary>
    [HttpGet("{schemaId}/definitions/{definitionId}")]
    public async Task<ActionResult<DefinitionMetadonnee>> ObtenirDefinitionParId(Guid schemaId, Guid definitionId)
    {
        try
        {
            var definition = await _depotDefinitionMetadonnee.ObtenirParIdAvecSchemaAsync(definitionId);
            if (definition == null || definition.SchemaMetadonneesId != schemaId)
            {
                return NotFound($"Définition avec l'ID {definitionId} non trouvée dans le schéma {schemaId}");
            }
            return Ok(definition);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la définition {DefinitionId}", definitionId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Met à jour une définition de métadonnée.
    /// </summary>
    [HttpPut("{schemaId}/definitions/{definitionId}")]
    public async Task<ActionResult<DefinitionMetadonnee>> MettreAJourDefinition(Guid schemaId, Guid definitionId, [FromBody] DefinitionMetadonnee definition)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var definitionExistante = await _depotDefinitionMetadonnee.ObtenirParIdAsync(definitionId);
            if (definitionExistante == null || definitionExistante.SchemaMetadonneesId != schemaId)
            {
                return NotFound($"Définition avec l'ID {definitionId} non trouvée dans le schéma {schemaId}");
            }

            // Actualiser les propriétés de l'entité existante (évite le conflit de tracking)
            definitionExistante.Nom = definition.Nom;
            definitionExistante.Description = definition.Description;
            definitionExistante.TypeDonnee = definition.TypeDonnee;
            definitionExistante.EstObligatoire = definition.EstObligatoire;
            definitionExistante.EstCalcule = definition.EstCalcule;
            definitionExistante.ValeurParDefaut = definition.ValeurParDefaut;
            definitionExistante.ReglesValidation = definition.ReglesValidation;
            definitionExistante.Ordre = definition.Ordre;
            definitionExistante.ModifiePar = definition.ModifiePar;
            definitionExistante.DateModification = DateTime.UtcNow;

            // Sauvegarder directement sans passer par MettreAJourAsync (entité déjà trackée)
            await _depotDefinitionMetadonnee.SauvegarderChangementsAsync();
            return Ok(definitionExistante);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la définition {DefinitionId}", definitionId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Supprime une définition de métadonnée.
    /// </summary>
    [HttpDelete("{schemaId}/definitions/{definitionId}")]
    public async Task<ActionResult> SupprimerDefinition(Guid schemaId, Guid definitionId)
    {
        try
        {
            var definition = await _depotDefinitionMetadonnee.ObtenirParIdAsync(definitionId);
            if (definition == null || definition.SchemaMetadonneesId != schemaId)
            {
                return NotFound($"Définition avec l'ID {definitionId} non trouvée dans le schéma {schemaId}");
            }

            var peutEtreSupprimee = await _depotDefinitionMetadonnee.PeutEtreSupprimee(definitionId);
            if (!peutEtreSupprimee)
            {
                return BadRequest("Cette définition ne peut pas être supprimée car elle est utilisée");
            }

            var supprime = await _depotDefinitionMetadonnee.SupprimerAsync(definitionId);
            if (!supprime)
            {
                return NotFound($"Définition avec l'ID {definitionId} non trouvée");
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la définition {DefinitionId}", definitionId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Met à jour l'ordre des définitions dans un schéma.
    /// </summary>
    [HttpPut("{schemaId}/definitions/ordre")]
    public async Task<ActionResult> MettreAJourOrdreDefinitions(Guid schemaId, [FromBody] Dictionary<Guid, int> ordresDefinitions)
    {
        try
        {
            await _depotDefinitionMetadonnee.MettreAJourOrdreDefinitionsAsync(schemaId, ordresDefinitions);
            return Ok();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de l'ordre des définitions pour le schéma {SchemaId}", schemaId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}
