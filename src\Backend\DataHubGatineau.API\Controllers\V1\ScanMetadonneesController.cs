﻿using Asp.Versioning;
using DataHubGatineau.Application.DTOs;
using DataHubGatineau.Application.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers.V1;

/// <summary>
/// ContrÃ´leur pour la gestion du scan de mÃ©tadonnÃ©es.
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
//[Authorize] // Temporalmente deshabilitado para pruebas
public class ScanMetadonneesController : ControllerBase
{
    private readonly IServiceScanMetadonnees _serviceScanMetadonnees;
    private readonly Application.Interfaces.IServiceConnexionSourceDonnees _serviceConnexionSourceDonnees;
    private readonly IServiceConfigurationScan _serviceConfigurationScan;
    private readonly ILogger<ScanMetadonneesController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ScanMetadonneesController"/>.
    /// </summary>
    /// <param name="serviceScanMetadonnees">Le service de scan de mÃ©tadonnÃ©es.</param>
    /// <param name="serviceConnexionSourceDonnees">Le service de connexion aux sources de donnÃ©es.</param>
    /// <param name="serviceConfigurationScan">Le service de configuration de scan.</param>
    /// <param name="logger">Le logger.</param>
    public ScanMetadonneesController(
        IServiceScanMetadonnees serviceScanMetadonnees,
        Application.Interfaces.IServiceConnexionSourceDonnees serviceConnexionSourceDonnees,
        IServiceConfigurationScan serviceConfigurationScan,
        ILogger<ScanMetadonneesController> logger)
    {
        _serviceScanMetadonnees = serviceScanMetadonnees;
        _serviceConnexionSourceDonnees = serviceConnexionSourceDonnees;
        _serviceConfigurationScan = serviceConfigurationScan;
        _logger = logger;
    }

    /// <summary>
    /// Teste la connexion Ã  une source de donnÃ©es.
    /// </summary>
    /// <param name="connexionId">L'identifiant de la connexion Ã  tester.</param>
    /// <returns>Le rÃ©sultat du test de connexion.</returns>
    [HttpPost("{connexionId}/tester-connexion")]
    public async Task<ActionResult<bool>> TesterConnexionAsync(Guid connexionId)
    {
        try
        {
            _logger.LogInformation("Test de connexion pour la source de donnÃ©es {ConnexionId}", connexionId);

            var connexion = await _serviceConnexionSourceDonnees.ObtenirParIdAsync(connexionId);
            if (connexion == null)
            {
                return NotFound($"Connexion source de donnÃ©es avec l'ID {connexionId} introuvable");
            }

            // Mapper le DTO vers l'entitÃ© pour le service de scan
            var connexionEntite = new Domain.Entites.ConnexionSourceDonnees
            {
                Id = connexion.Id,
                Nom = connexion.Nom,
                TypeSource = connexion.TypeSource,
                ChaineConnexion = connexion.ChaineConnexion
            };

            var resultat = await _serviceScanMetadonnees.TesterConnexionAsync(connexionEntite);
            return Ok(resultat);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du test de connexion pour {ConnexionId}", connexionId);
            return StatusCode(500, "Erreur interne du serveur lors du test de connexion");
        }
    }

    /// <summary>
    /// Obtient un aperÃ§u des mÃ©tadonnÃ©es disponibles dans une source de donnÃ©es.
    /// </summary>
    /// <param name="connexionId">L'identifiant de la connexion source de donnÃ©es.</param>
    /// <param name="filtres">Les filtres Ã  appliquer (optionnel).</param>
    /// <returns>L'aperÃ§u des mÃ©tadonnÃ©es disponibles.</returns>
    [HttpPost("{connexionId}/apercu")]
    public async Task<ActionResult<ApercuMetadonneesDTO>> ObtenirApercuMetadonneesAsync(
        Guid connexionId,
        [FromBody] FiltresScanDTO? filtres = null)
    {
        try
        {
            _logger.LogInformation("GÃ©nÃ©ration d'aperÃ§u des mÃ©tadonnÃ©es pour la connexion {ConnexionId}", connexionId);

            var connexion = await _serviceConnexionSourceDonnees.ObtenirParIdAsync(connexionId);
            if (connexion == null)
            {
                return NotFound($"Connexion source de donnÃ©es avec l'ID {connexionId} introuvable");
            }

            // Mapper le DTO vers l'entitÃ© pour le service de scan
            var connexionEntite = new Domain.Entites.ConnexionSourceDonnees
            {
                Id = connexion.Id,
                Nom = connexion.Nom,
                TypeSource = connexion.TypeSource,
                ChaineConnexion = connexion.ChaineConnexion
            };

            var apercu = await _serviceScanMetadonnees.ObtenirApercuMetadonneesAsync(connexionEntite, filtres);
            return Ok(apercu);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la gÃ©nÃ©ration d'aperÃ§u pour {ConnexionId}", connexionId);
            return StatusCode(500, "Erreur interne du serveur lors de la gÃ©nÃ©ration d'aperÃ§u");
        }
    }

    /// <summary>
    /// Valide une configuration de scan.
    /// </summary>
    /// <param name="configurationId">L'identifiant de la configuration Ã  valider.</param>
    /// <returns>Le rÃ©sultat de la validation.</returns>
    [HttpPost("configurations/{configurationId}/valider")]
    public async Task<ActionResult<ValidationConfigurationDTO>> ValiderConfigurationAsync(Guid configurationId)
    {
        try
        {
            _logger.LogInformation("Validation de la configuration de scan {ConfigurationId}", configurationId);

            var configuration = await _serviceConfigurationScan.ObtenirParIdAsync(configurationId);
            if (configuration == null)
            {
                return NotFound($"Configuration de scan avec l'ID {configurationId} introuvable");
            }

            // Mapper le DTO vers l'entitÃ© pour le service de scan
            var configurationEntite = new Domain.Entites.ConfigurationScan
            {
                Id = configuration.Id,
                ConnexionSourceDonneesId = configuration.ConnexionSourceDonneesId,
                FiltresInclusion = configuration.FiltresInclusion,
                FiltresExclusion = configuration.FiltresExclusion,
                IncluireMetadonneesTechniques = configuration.IncluireMetadonneesTechniques,
                IncluireStatistiques = configuration.IncluireStatistiques,
                MettreAJourLignage = configuration.MettreAJourLignage,
                EffectuerEchantillonnage = configuration.EffectuerEchantillonnage
            };

            var validation = await _serviceScanMetadonnees.ValiderConfigurationAsync(configurationEntite);
            return Ok(validation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la validation de la configuration {ConfigurationId}", configurationId);
            return StatusCode(500, "Erreur interne du serveur lors de la validation");
        }
    }

    /// <summary>
    /// ExÃ©cute un scan manuel de mÃ©tadonnÃ©es.
    /// </summary>
    /// <param name="configurationId">L'identifiant de la configuration de scan Ã  exÃ©cuter.</param>
    /// <returns>Le rÃ©sultat du scan.</returns>
    [HttpPost("configurations/{configurationId}/executer")]
    public async Task<ActionResult> ExecuterScanManuelAsync(Guid configurationId)
    {
        try
        {
            _logger.LogInformation("Exécution manuelle du scan pour la configuration {ConfigurationId}", configurationId);

            // Obtenir la configuration
            var configuration = await _serviceConfigurationScan.ObtenirParIdAsync(configurationId);
            if (configuration == null)
            {
                return NotFound(new { Success = false, Message = "Configuration de scan non trouvée" });
            }

            // Convertir le DTO en entité pour le service
            var configurationEntite = new Domain.Entites.ConfigurationScan
            {
                Id = configuration.Id,
                Nom = configuration.Nom,
                ConnexionSourceDonneesId = configuration.ConnexionSourceDonneesId,
                EstActive = configuration.EstActive,
                FrequenceExecution = configuration.FrequenceExecution,
                ProchaineScanPrevue = configuration.ProchaineScanPrevue,
                DateCreation = configuration.DateCreation,
                DateModification = configuration.DateModification
            };

            // Exécuter le scan et obtenir les résultats détaillés
            var resultatScan = await _serviceScanMetadonnees.ExecuterScanAsync(configurationEntite);

            if (resultatScan.EstReussi)
            {
                // Obtenir les actifs créés récemment pour cette configuration
                var actifsCreés = await ObtenirActifsCreésParScan(configurationId);

                var resultatDetaille = new
                {
                    NombreActifsCreés = resultatScan.ActifsDecouverts,
                    DureeScan = resultatScan.DateFin.HasValue ?
                        $"{(resultatScan.DateFin.Value - resultatScan.DateDebut).TotalSeconds:F1}s" : "En cours",
                    Statut = "Terminé avec succès",
                    ActifsCreés = actifsCreés.Select(a => new {
                        Nom = a.Nom,
                        Type = a.TypeActifDonnees?.Nom ?? "Table"
                    }).ToList()
                };

                return Ok(resultatDetaille);
            }
            else
            {
                return BadRequest(new {
                    Success = false,
                    Message = resultatScan.MessageErreur ?? "Échec de l'exécution du scan",
                    NombreActifsCreés = 0,
                    Statut = "Échec"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'exécution du scan pour la configuration {ConfigurationId}", configurationId);
            return StatusCode(500, new {
                Success = false,
                Message = "Erreur interne du serveur lors de l'exécution du scan",
                NombreActifsCreés = 0,
                Statut = "Erreur"
            });
        }
    }

    private async Task<IEnumerable<Domain.Entites.ActifDonnees>> ObtenirActifsCreésParScan(Guid configurationId)
    {
        try
        {
            // Pour l'instant, retourner une liste vide
            // TODO: Implémenter la récupération des actifs créés récemment
            await Task.CompletedTask;
            return Enumerable.Empty<Domain.Entites.ActifDonnees>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erreur lors de la récupération des actifs créés pour le scan {ConfigurationId}", configurationId);
            return Enumerable.Empty<Domain.Entites.ActifDonnees>();
        }
    }

    /// <summary>
    /// Obtient l'historique des scans pour une configuration.
    /// </summary>
    /// <param name="configurationId">L'identifiant de la configuration.</param>
    /// <returns>L'historique des scans.</returns>
    [HttpGet("configurations/{configurationId}/historique")]
    public async Task<ActionResult<IEnumerable<HistoriqueScanDTO>>> ObtenirHistoriqueScanAsync(Guid configurationId)
    {
        try
        {
            _logger.LogInformation("Obtention de l'historique des scans pour la configuration {ConfigurationId}", configurationId);

            // Note: Vous devrez implÃ©menter cette mÃ©thode dans IServiceHistoriqueScan
            // var historique = await _serviceHistoriqueScan.ObtenirParConfigurationIdAsync(configurationId);

            // Pour l'instant, retourner une liste vide
            return Ok(new List<HistoriqueScanDTO>());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de l'historique pour la configuration {ConfigurationId}", configurationId);
            return StatusCode(500, "Erreur interne du serveur lors de l'obtention de l'historique");
        }
    }

    /// <summary>
    /// Obtient les statistiques de scan pour une connexion.
    /// </summary>
    /// <param name="connexionId">L'identifiant de la connexion.</param>
    /// <returns>Les statistiques de scan.</returns>
    [HttpGet("connexions/{connexionId}/statistiques")]
    public async Task<ActionResult<object>> ObtenirStatistiquesScanAsync(Guid connexionId)
    {
        try
        {
            _logger.LogInformation("Obtention des statistiques de scan pour la connexion {ConnexionId}", connexionId);

            // Obtenir un aperÃ§u pour avoir des statistiques de base
            var connexion = await _serviceConnexionSourceDonnees.ObtenirParIdAsync(connexionId);
            if (connexion == null)
            {
                return NotFound($"Connexion source de donnÃ©es avec l'ID {connexionId} introuvable");
            }

            var connexionEntite = new Domain.Entites.ConnexionSourceDonnees
            {
                Id = connexion.Id,
                Nom = connexion.Nom,
                TypeSource = connexion.TypeSource,
                ChaineConnexion = connexion.ChaineConnexion
            };

            var apercu = await _serviceScanMetadonnees.ObtenirApercuMetadonneesAsync(connexionEntite);

            var statistiques = new
            {
                NombreSchemas = apercu.Schemas.Count,
                NombreTotalTables = apercu.NombreTotalTables,
                NombreTotalColonnes = apercu.NombreTotalColonnes,
                TablesAvecActifsExistants = apercu.Schemas.SelectMany(s => s.Tables).Count(t => t.ActifExiste),
                DateDerniereAnalyse = apercu.DateGeneration,
                TypeSource = apercu.TypeSource
            };

            return Ok(statistiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des statistiques pour la connexion {ConnexionId}", connexionId);
            return StatusCode(500, "Erreur interne du serveur lors de l'obtention des statistiques");
        }
    }
}
