﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\DataHubGatineau.Domain\DataHubGatineau.Domain.csproj" />
    <ProjectReference Include="..\..\Shared\DataHubGatineau.Core\DataHubGatineau.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.4" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.4" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.5.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.5.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="MySql.Data" Version="9.1.0" />
    <PackageReference Include="Npgsql" Version="8.0.5" />
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.6.1" />
    <!-- Azure AD / MSAL.NET Integration -->
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="Microsoft.Graph" Version="5.36.0" />
    <!-- Active Directory Local / LDAP Integration -->
    <PackageReference Include="System.DirectoryServices" Version="7.0.0" />
    <PackageReference Include="System.DirectoryServices.AccountManagement" Version="7.0.0" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
    <DefineConstants>$(DefineConstants);WINDOWS</DefineConstants>
  </PropertyGroup>

</Project>
