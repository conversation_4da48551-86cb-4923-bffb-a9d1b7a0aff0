using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Application.Services;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace DataHubGatineau.Application;

/// <summary>
/// Extensions pour la configuration des services d'application.
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Ajoute les services d'application à la collection de services.
    /// </summary>
    /// <param name="services">Collection de services.</param>
    /// <returns>Collection de services mise à jour.</returns>
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        // Enregistrement des services
        services.AddScoped<IServiceActifDonnees, ServiceActifDonnees>();
        services.AddScoped<IServiceTypeMetadonnee, ServiceTypeMetadonnee>();
        services.AddScoped<IServiceCategorieMetadonnee, ServiceCategorieMetadonnee>();
        services.AddScoped<IServiceMetadonnee, ServiceMetadonnee>();
        services.AddScoped<IServiceRegleQualite, ServiceRegleQualite>();
        services.AddScoped<IServiceResultatRegleQualite, ServiceResultatRegleQualite>();
        // services.AddScoped<IServiceExecutionRegleQualite, ServiceExecutionRegleQualite>();
        services.AddScoped<IServiceTermeGlossaire, ServiceTermeGlossaire>();
        services.AddScoped<IServiceDomaineGouvernance, ServiceDomaineGouvernance>();
        services.AddScoped<IServiceDomaineAffaires, ServiceDomaineAffaires>();
        services.AddScoped<IServiceProduitDonnees, ServiceProduitDonnees>();

        return services;
    }
}
