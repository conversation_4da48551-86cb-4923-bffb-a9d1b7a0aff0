using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;
using DataHubGatineau.Domain.Interfaces;
using TypeActifDonneesEntity = DataHubGatineau.Domain.Entites.TypeActifDonnees;

namespace DataHubGatineau.Application.Interfaces;

/// <summary>
/// Interface du service des actifs de données.
/// </summary>
public interface IServiceActifDonnees
{
    /// <summary>
    /// Obtient un actif de données par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de l'actif de données.</param>
    /// <returns>L'actif de données si trouvé, sinon null.</returns>
    Task<ActifDonnees?> ObtenirParIdAsync(Guid id);

    /// <summary>
    /// Obtient un actif de données par son identifiant avec les inclusions spécifiées.
    /// </summary>
    /// <param name="id">Identifiant de l'actif de données.</param>
    /// <param name="includes">Propriétés de navigation à inclure.</param>
    /// <returns>L'actif de données si trouvé, sinon null.</returns>
    Task<ActifDonnees?> ObtenirParIdAsync(Guid id, ActifDonneesInclude includes);

    /// <summary>
    /// Obtient un actif de données par son identifiant avec toutes les entités liées.
    /// </summary>
    /// <param name="id">Identifiant de l'actif de données.</param>
    /// <returns>L'actif de données avec toutes les entités liées si trouvé, sinon null.</returns>
    Task<ActifDonnees?> ObtenirParIdAvecEntitesLieesAsync(Guid id);

    /// <summary>
    /// Obtient tous les actifs de données.
    /// </summary>
    /// <returns>Une collection d'actifs de données.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirTousAsync();

    /// <summary>
    /// Obtient tous les actifs de données avec les inclusions spécifiées.
    /// </summary>
    /// <param name="includes">Propriétés de navigation à inclure.</param>
    /// <returns>Une collection d'actifs de données.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirTousAsync(ActifDonneesInclude includes);

    /// <summary>
    /// Obtient les actifs de données par type.
    /// </summary>
    /// <param name="type">Type d'actif de données.</param>
    /// <returns>Une collection d'actifs de données du type spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParTypeAsync(string type);

    /// <summary>
    /// Obtient les actifs de données par classification de sensibilité.
    /// </summary>
    /// <param name="classification">Classification de sensibilité.</param>
    /// <returns>Une collection d'actifs de données avec la classification spécifiée.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParClassificationAsync(ClassificationSensibilite classification);

    /// <summary>
    /// Obtient les actifs de données par propriétaire.
    /// </summary>
    /// <param name="proprietaire">Propriétaire des actifs de données.</param>
    /// <returns>Une collection d'actifs de données appartenant au propriétaire spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParProprietaireAsync(string proprietaire);

    /// <summary>
    /// Obtient les actifs de données par terme du glossaire.
    /// </summary>
    /// <param name="termeId">Identifiant du terme du glossaire.</param>
    /// <returns>Une collection d'actifs de données associés au terme spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParTermeGlossaireAsync(int termeId);

    /// <summary>
    /// Obtient les actifs de données par domaine de gouvernance.
    /// </summary>
    /// <param name="domaineGouvernanceId">Identifiant du domaine de gouvernance.</param>
    /// <returns>Une collection d'actifs de données associés au domaine de gouvernance spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParDomaineGouvernanceAsync(Guid domaineGouvernanceId);

    /// <summary>
    /// Obtient les actifs de données par connexion à la source de données.
    /// </summary>
    /// <param name="connexionSourceDonneesId">Identifiant de la connexion à la source de données.</param>
    /// <returns>Une collection d'actifs de données associés à la connexion spécifiée.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParConnexionSourceDonneesAsync(Guid connexionSourceDonneesId);

    /// <summary>
    /// Obtient les actifs de données par statut.
    /// </summary>
    /// <param name="statut">Statut des actifs de données.</param>
    /// <returns>Une collection d'actifs de données avec le statut spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParStatutAsync(string statut);

    /// <summary>
    /// Obtient le lignage des données pour un actif de données.
    /// </summary>
    /// <param name="actifId">Identifiant de l'actif de données.</param>
    /// <returns>Une collection d'actifs de données représentant le lignage.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirLignageAsync(Guid actifId);

    /// <summary>
    /// Ajoute un nouvel actif de données.
    /// </summary>
    /// <param name="actif">Actif de données à ajouter.</param>
    /// <returns>L'actif de données ajouté.</returns>
    Task<ActifDonnees> AjouterAsync(ActifDonnees actif);

    /// <summary>
    /// Met à jour un actif de données existant.
    /// </summary>
    /// <param name="actif">Actif de données à mettre à jour.</param>
    /// <returns>L'actif de données mis à jour.</returns>
    Task<ActifDonnees> MettreAJourAsync(ActifDonnees actif);

    /// <summary>
    /// Supprime un actif de données.
    /// </summary>
    /// <param name="id">Identifiant de l'actif de données à supprimer.</param>
    /// <returns>True si la suppression a réussi, sinon false.</returns>
    Task<bool> SupprimerAsync(Guid id);

    /// <summary>
    /// Obtient tous les types d'actifs de données.
    /// </summary>
    /// <returns>Une collection de types d'actifs de données.</returns>
    Task<IEnumerable<TypeActifDonneesEntity>> ObtenirTypesActifDonneesAsync();

    /// <summary>
    /// Obtient tous les formats d'actifs de données.
    /// </summary>
    /// <returns>Une collection de formats d'actifs de données.</returns>
    Task<IEnumerable<FormatActifDonnees>> ObtenirFormatsActifDonneesAsync();

    /// <summary>
    /// Obtient toutes les sources d'actifs de données.
    /// </summary>
    /// <returns>Une collection de sources d'actifs de données.</returns>
    Task<IEnumerable<SourceActifDonnees>> ObtenirSourcesActifDonneesAsync();

    /// <summary>
    /// Obtient toutes les fréquences de mise à jour.
    /// </summary>
    /// <returns>Une collection de fréquences de mise à jour.</returns>
    Task<IEnumerable<FrequenceMiseAJour>> ObtenirFrequencesMiseAJourAsync();

    /// <summary>
    /// Obtient tous les statuts d'actifs de données.
    /// </summary>
    /// <returns>Une collection de statuts d'actifs de données.</returns>
    Task<IEnumerable<StatutActifDonnees>> ObtenirStatutsActifDonneesAsync();

    /// <summary>
    /// Met à jour tous les actifs de données sans type ou source avec des valeurs par défaut.
    /// </summary>
    /// <returns>Le nombre d'actifs de données mis à jour.</returns>
    Task<int> MettreAJourValeursParDefautAsync();

    /// <summary>
    /// Recherche des actifs de données par terme de recherche.
    /// </summary>
    /// <param name="terme">Terme de recherche.</param>
    /// <returns>Une collection d'actifs de données correspondant au terme de recherche.</returns>
    Task<IEnumerable<ActifDonnees>> RechercherAsync(string terme);

    /// <summary>
    /// Recherche des actifs de données par terme de recherche avec les inclusions spécifiées.
    /// </summary>
    /// <param name="terme">Terme de recherche.</param>
    /// <param name="includes">Propriétés de navigation à inclure.</param>
    /// <returns>Une collection d'actifs de données correspondant au terme de recherche.</returns>
    Task<IEnumerable<ActifDonnees>> RechercherAsync(string terme, ActifDonneesInclude includes);
}
