using AutoMapper;
using DataHubGatineau.Application.DTOs.Identity;
using DataHubGatineau.Application.Services.Identity.Interfaces;
using DataHubGatineau.Domain.Entites.Identity;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
#if WINDOWS
using System.DirectoryServices;
using System.DirectoryServices.AccountManagement;
#endif

namespace DataHubGatineau.Application.Services.Identity;

/// <summary>
/// Service pour l'authentification Active Directory local.
/// </summary>
public partial class ServiceAuthentificationActiveDirectory : IServiceAuthentificationActiveDirectory
{
    private readonly IUniteDeTravail _uniteDeTravail;
    private readonly IMapper _mapper;
    private readonly ILogger<ServiceAuthentificationActiveDirectory> _logger;
    private readonly IConfiguration _configuration;
    private readonly IServiceAuthentification _serviceAuthentification;
    private readonly IServiceUtilisateur _serviceUtilisateur;
    private readonly ActiveDirectoryConfigurationDTO _adConfig;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ServiceAuthentificationActiveDirectory"/>.
    /// </summary>
    public ServiceAuthentificationActiveDirectory(
        IUniteDeTravail uniteDeTravail,
        IMapper mapper,
        ILogger<ServiceAuthentificationActiveDirectory> logger,
        IConfiguration configuration,
        IServiceAuthentification serviceAuthentification,
        IServiceUtilisateur serviceUtilisateur)
    {
        _uniteDeTravail = uniteDeTravail;
        _mapper = mapper;
        _logger = logger;
        _configuration = configuration;
        _serviceAuthentification = serviceAuthentification;
        _serviceUtilisateur = serviceUtilisateur;

        // Configuration Active Directory
        _adConfig = new ActiveDirectoryConfigurationDTO
        {
            Domaine = _configuration["ActiveDirectory:Domaine"] ?? "GATINEAU",
            ServeurLdap = _configuration["ActiveDirectory:ServeurLdap"] ?? "ldap://gatineau.local",
            PortLdap = int.Parse(_configuration["ActiveDirectory:PortLdap"] ?? "389"),
            BaseDn = _configuration["ActiveDirectory:BaseDn"] ?? "DC=gatineau,DC=local",
            UtilisateurService = _configuration["ActiveDirectory:UtilisateurService"] ?? string.Empty,
            MotDePasseService = _configuration["ActiveDirectory:MotDePasseService"] ?? string.Empty,
            UtiliserSsl = bool.Parse(_configuration["ActiveDirectory:UtiliserSsl"] ?? "true"),
            TimeoutConnexion = int.Parse(_configuration["ActiveDirectory:TimeoutConnexion"] ?? "30")
        };
    }

    /// <inheritdoc/>
    public async Task<ResultatAuthentificationDTO> AuthentifierAsync(string nomUtilisateur, string motDePasse, string? domaine = null)
    {
        try
        {
            _logger.LogInformation("Début de l'authentification Active Directory pour l'utilisateur {NomUtilisateur}", nomUtilisateur);

            // Valider les identifiants contre Active Directory
            var identifiantsValides = await ValiderIdentifiantsAsync(nomUtilisateur, motDePasse, domaine);
            if (!identifiantsValides)
            {
                _logger.LogWarning("Échec de l'authentification Active Directory pour l'utilisateur {NomUtilisateur}", nomUtilisateur);
                return new ResultatAuthentificationDTO
                {
                    Succes = false,
                    Message = "Nom d'utilisateur ou mot de passe incorrect"
                };
            }

            // Obtenir les informations utilisateur depuis Active Directory
            var utilisateurAd = await ObtenirUtilisateurAsync(nomUtilisateur, domaine);
            if (utilisateurAd == null)
            {
                return new ResultatAuthentificationDTO
                {
                    Succes = false,
                    Message = "Impossible d'obtenir les informations utilisateur depuis Active Directory"
                };
            }

            // Synchroniser l'utilisateur avec le système local
            var utilisateurLocal = await SynchroniserUtilisateurAsync(utilisateurAd);

            // Générer le token JWT local
            var tokenJwt = await _serviceAuthentification.GenererTokenJwtAsync(utilisateurLocal);
            var refreshToken = await _serviceAuthentification.GenererRefreshTokenAsync(utilisateurLocal.Id);

            _logger.LogInformation("Authentification Active Directory réussie pour l'utilisateur {NomUtilisateur}", nomUtilisateur);

            return new ResultatAuthentificationDTO
            {
                Succes = true,
                Token = tokenJwt,
                RefreshToken = refreshToken,
                DateExpiration = DateTime.Now.AddHours(1),
                Utilisateur = utilisateurLocal,
                Message = "Authentification Active Directory réussie"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'authentification Active Directory pour l'utilisateur {NomUtilisateur}", nomUtilisateur);
            return new ResultatAuthentificationDTO
            {
                Succes = false,
                Message = "Erreur lors de l'authentification Active Directory"
            };
        }
    }

    /// <inheritdoc/>
    public async Task<ResultatAuthentificationDTO> AuthentifierAsync(DemandeAuthentificationActiveDirectoryDTO demande)
    {
        return await AuthentifierAsync(demande.NomUtilisateur, demande.MotDePasse, demande.Domaine);
    }

    /// <inheritdoc/>
    public async Task<bool> ValiderIdentifiantsAsync(string nomUtilisateur, string motDePasse, string? domaine = null)
    {
        try
        {
#if WINDOWS
            var domaineUtilise = domaine ?? _adConfig.Domaine;

            // Utiliser PrincipalContext pour valider les identifiants
            using var context = new PrincipalContext(ContextType.Domain, domaineUtilise);
            var estValide = context.ValidateCredentials(nomUtilisateur, motDePasse);

            _logger.LogInformation("Validation des identifiants pour {NomUtilisateur}@{Domaine}: {Resultat}",
                nomUtilisateur, domaineUtilise, estValide ? "Succès" : "Échec");

            return await Task.FromResult(estValide);
#else
            _logger.LogWarning("L'authentification Active Directory n'est pas supportée sur cette plateforme");
            return await Task.FromResult(false);
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la validation des identifiants pour {NomUtilisateur}", nomUtilisateur);
            return false;
        }
    }

    /// <inheritdoc/>
    public async Task<UtilisateurActiveDirectoryDTO?> ObtenirUtilisateurAsync(string nomUtilisateur, string? domaine = null)
    {
        try
        {
#if WINDOWS
            var domaineUtilise = domaine ?? _adConfig.Domaine;

            using var context = new PrincipalContext(ContextType.Domain, domaineUtilise);
            using var utilisateur = UserPrincipal.FindByIdentity(context, nomUtilisateur);

            if (utilisateur == null)
            {
                _logger.LogWarning("Utilisateur {NomUtilisateur} non trouvé dans Active Directory", nomUtilisateur);
                return null;
            }

            // Obtenir les groupes de l'utilisateur
            var groupes = await ObtenirGroupesUtilisateurAsync(nomUtilisateur, domaine);

            var utilisateurDto = new UtilisateurActiveDirectoryDTO
            {
                NomUtilisateur = utilisateur.SamAccountName ?? nomUtilisateur,
                NomComplet = utilisateur.DisplayName ?? string.Empty,
                Prenom = utilisateur.GivenName,
                Nom = utilisateur.Surname,
                Email = utilisateur.EmailAddress,
                TitrePoste = utilisateur.Description,
                DistinguishedName = utilisateur.DistinguishedName ?? string.Empty,
                Sid = utilisateur.Sid?.ToString(),
                EstActif = utilisateur.Enabled ?? true,
                DerniereConnexion = utilisateur.LastLogon,
                Groupes = groupes
            };

            return await Task.FromResult(utilisateurDto);
#else
            _logger.LogWarning("L'authentification Active Directory n'est pas supportée sur cette plateforme");
            return await Task.FromResult<UtilisateurActiveDirectoryDTO?>(null);
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des informations utilisateur {NomUtilisateur}", nomUtilisateur);
            return null;
        }
    }
}
