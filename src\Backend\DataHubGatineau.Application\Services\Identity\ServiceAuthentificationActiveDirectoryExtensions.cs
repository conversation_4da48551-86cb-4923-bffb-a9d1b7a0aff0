using AutoMapper;
using DataHubGatineau.Application.DTOs.Identity;
using DataHubGatineau.Application.Services.Identity.Interfaces;
using DataHubGatineau.Domain.Entites.Identity;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.DirectoryServices.AccountManagement;
using System.DirectoryServices.ActiveDirectory;

namespace DataHubGatineau.Application.Services.Identity;

/// <summary>
/// Extensions pour le service d'authentification Active Directory.
/// </summary>
public partial class ServiceAuthentificationActiveDirectory
{
    /// <inheritdoc/>
    public async Task<UtilisateurDTO> SynchroniserUtilisateurAsync(UtilisateurActiveDirectoryDTO utilisateurAd)
    {
        try
        {
            _logger.LogInformation("Synchronisation de l'utilisateur Active Directory {NomUtilisateur}", utilisateurAd.NomUtilisateur);

            // Rechercher l'utilisateur existant par nom d'utilisateur et fournisseur
            var utilisateurExistant = await _uniteDeTravail.Utilisateurs
                .ObtenirParNomUtilisateurAsync(utilisateurAd.NomUtilisateur);

            Utilisateur utilisateur;

            if (utilisateurExistant == null || utilisateurExistant.FournisseurIdentite != "ActiveDirectory")
            {
                // Créer un nouvel utilisateur
                utilisateur = new Utilisateur
                {
                    NomUtilisateur = utilisateurAd.NomUtilisateur,
                    Email = utilisateurAd.Email ?? $"{utilisateurAd.NomUtilisateur}@{_adConfig.Domaine}.local",
                    Prenom = utilisateurAd.Prenom ?? string.Empty,
                    Nom = utilisateurAd.Nom ?? string.Empty,
                    IdentifiantExterne = utilisateurAd.Sid ?? utilisateurAd.DistinguishedName,
                    FournisseurIdentite = "ActiveDirectory",
                    EstActif = utilisateurAd.EstActif,
                    DateCreation = DateTime.Now,
                    // Pas de mot de passe pour les utilisateurs Active Directory
                    MotDePasseHash = null,
                    MotDePasseSel = null
                };

                await _uniteDeTravail.Utilisateurs.AjouterAsync(utilisateur);
                _logger.LogInformation("Nouvel utilisateur Active Directory créé: {NomUtilisateur}", utilisateur.NomUtilisateur);
            }
            else
            {
                // Mettre à jour l'utilisateur existant
                utilisateur = utilisateurExistant;
                utilisateur.Email = utilisateurAd.Email ?? utilisateur.Email;
                utilisateur.Prenom = utilisateurAd.Prenom ?? utilisateur.Prenom;
                utilisateur.Nom = utilisateurAd.Nom ?? utilisateur.Nom;
                utilisateur.EstActif = utilisateurAd.EstActif;
                utilisateur.DerniereConnexion = DateTime.Now;

                await _uniteDeTravail.Utilisateurs.MettreAJourAsync(utilisateur);
                _logger.LogInformation("Utilisateur Active Directory mis à jour: {NomUtilisateur}", utilisateur.NomUtilisateur);
            }

            // Assigner les rôles basés sur les groupes Active Directory
            await AssignerRolesParGroupesAsync(utilisateur, utilisateurAd.Groupes);

            await _uniteDeTravail.EnregistrerChangementsAsync();

            return _mapper.Map<UtilisateurDTO>(utilisateur);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la synchronisation de l'utilisateur Active Directory {NomUtilisateur}", utilisateurAd.NomUtilisateur);
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<List<GroupeActiveDirectoryDTO>> ObtenirGroupesUtilisateurAsync(string nomUtilisateur, string? domaine = null)
    {
        try
        {
#if WINDOWS
            var groupes = new List<GroupeActiveDirectoryDTO>();
            var domaineUtilise = domaine ?? _adConfig.Domaine;

            using var context = new PrincipalContext(ContextType.Domain, domaineUtilise);
            using var utilisateur = UserPrincipal.FindByIdentity(context, nomUtilisateur);

            if (utilisateur != null)
            {
                var groupesPrincipaux = utilisateur.GetGroups();
                foreach (var groupe in groupesPrincipaux)
                {
                    groupes.Add(new GroupeActiveDirectoryDTO
                    {
                        Nom = groupe.Name ?? string.Empty,
                        Description = groupe.Description,
                        DistinguishedName = groupe.DistinguishedName ?? string.Empty,
                        Sid = groupe.Sid?.ToString()
                    });
                }
            }

            return await Task.FromResult(groupes);
#else
            _logger.LogWarning("L'authentification Active Directory n'est pas supportée sur cette plateforme");
            return await Task.FromResult(new List<GroupeActiveDirectoryDTO>());
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des groupes pour l'utilisateur {NomUtilisateur}", nomUtilisateur);
            return new List<GroupeActiveDirectoryDTO>();
        }
    }

    /// <inheritdoc/>
    public async Task<List<UtilisateurActiveDirectoryDTO>> RechercherUtilisateursAsync(string critereRecherche, int nombreMaxResultats = 50)
    {
        try
        {
#if WINDOWS
            var utilisateurs = new List<UtilisateurActiveDirectoryDTO>();

            using var context = new PrincipalContext(ContextType.Domain, _adConfig.Domaine);
            using var recherche = new UserPrincipal(context)
            {
                Name = $"*{critereRecherche}*"
            };

            using var resultats = new PrincipalSearcher(recherche);
            var compteur = 0;

            foreach (var resultat in resultats.FindAll())
            {
                if (compteur >= nombreMaxResultats) break;

                if (resultat is UserPrincipal utilisateur)
                {
                    utilisateurs.Add(new UtilisateurActiveDirectoryDTO
                    {
                        NomUtilisateur = utilisateur.SamAccountName ?? string.Empty,
                        NomComplet = utilisateur.DisplayName ?? string.Empty,
                        Prenom = utilisateur.GivenName,
                        Nom = utilisateur.Surname,
                        Email = utilisateur.EmailAddress,
                        DistinguishedName = utilisateur.DistinguishedName ?? string.Empty,
                        EstActif = utilisateur.Enabled ?? true
                    });
                    compteur++;
                }
            }

            return await Task.FromResult(utilisateurs);
#else
            _logger.LogWarning("L'authentification Active Directory n'est pas supportée sur cette plateforme");
            return await Task.FromResult(new List<UtilisateurActiveDirectoryDTO>());
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche d'utilisateurs avec le critère {Critere}", critereRecherche);
            return new List<UtilisateurActiveDirectoryDTO>();
        }
    }

    /// <inheritdoc/>
    public async Task<bool> TesterConnexionAsync()
    {
        try
        {
#if WINDOWS
            using var context = new PrincipalContext(ContextType.Domain, _adConfig.Domaine);
            // Tenter une simple validation pour tester la connexion
            var testConnexion = context.ConnectedServer != null;
            _logger.LogInformation("Test de connexion Active Directory réussi pour le domaine {Domaine}", _adConfig.Domaine);
            return await Task.FromResult(testConnexion);
#else
            _logger.LogWarning("L'authentification Active Directory n'est pas supportée sur cette plateforme");
            return await Task.FromResult(false);
#endif
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Échec du test de connexion Active Directory");
            return false;
        }
    }

    /// <inheritdoc/>
    public async Task<ResultatSynchronisationActiveDirectoryDTO> SynchroniserTousLesUtilisateursAsync(
        string? filtreUtilisateurs = null,
        string? filtreGroupes = null)
    {
        var resultat = new ResultatSynchronisationActiveDirectoryDTO();

        try
        {
            _logger.LogInformation("Début de la synchronisation complète Active Directory");

            // Pour l'instant, cette fonctionnalité est limitée pour éviter la surcharge
            resultat.Succes = false;
            resultat.Message = "Synchronisation complète non implémentée - utilisez la synchronisation individuelle";
            resultat.Avertissements.Add("La synchronisation complète peut être implémentée selon les besoins");

            _logger.LogWarning("Synchronisation complète Active Directory non implémentée");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la synchronisation complète Active Directory");
            resultat.Succes = false;
            resultat.Message = "Erreur lors de la synchronisation";
            resultat.Erreurs.Add(ex.Message);
        }

        return await Task.FromResult(resultat);
    }

    /// <inheritdoc/>
    public ActiveDirectoryConfigurationDTO ObtenirConfiguration()
    {
        return _adConfig;
    }

    /// <summary>
    /// Assigne les rôles à un utilisateur basé sur ses groupes Active Directory.
    /// </summary>
    /// <param name="utilisateur">Utilisateur à qui assigner les rôles.</param>
    /// <param name="groupesAd">Groupes Active Directory de l'utilisateur.</param>
    private async Task AssignerRolesParGroupesAsync(Utilisateur utilisateur, List<GroupeActiveDirectoryDTO> groupesAd)
    {
        try
        {
            // Mapping des groupes Active Directory vers les rôles du système
            var mappingGroupesRoles = new Dictionary<string, string>
            {
                ["Domain Admins"] = "AdministrateurSysteme",
                ["IT Admins"] = "AdministrateurSysteme",
                ["Data Stewards"] = "IntendantDonnees",
                ["Managers"] = "GestionnaireDonnees",
                ["Data Analysts"] = "AnalysteDonnees",
                ["Domain Users"] = "UtilisateurStandard",
                ["Employees"] = "UtilisateurStandard",
                ["Councillors"] = "ConseillerMunicipal",
                ["Mayor Office"] = "BureauMaire"
            };

            // Obtenir les rôles actuels de l'utilisateur
            var rolesActuels = await _uniteDeTravail.Utilisateurs.ObtenirRolesUtilisateurAsync(utilisateur.Id);
            var rolesAd = rolesActuels.Where(r => r.EstSysteme).ToList();

            // Déterminer les nouveaux rôles basés sur les groupes Active Directory
            var nouveauxRoles = new List<string>();
            foreach (var groupe in groupesAd)
            {
                if (mappingGroupesRoles.TryGetValue(groupe.Nom, out var nomRole))
                {
                    nouveauxRoles.Add(nomRole);
                }
            }

            // Ajouter un rôle par défaut si aucun rôle spécifique n'est trouvé
            if (!nouveauxRoles.Any())
            {
                nouveauxRoles.Add("UtilisateurStandard");
            }

            // Supprimer les anciens rôles Active Directory
            foreach (var role in rolesAd)
            {
                await _serviceUtilisateur.SupprimerRoleAsync(utilisateur.Id, role.Id);
            }

            // Ajouter les nouveaux rôles
            foreach (var nomRole in nouveauxRoles.Distinct())
            {
                var role = await _uniteDeTravail.Roles.ObtenirParNomAsync(nomRole);
                if (role != null)
                {
                    await _serviceUtilisateur.AjouterRoleAsync(utilisateur.Id, role.Id);
                    _logger.LogInformation("Rôle {Role} assigné à l'utilisateur {NomUtilisateur}", nomRole, utilisateur.NomUtilisateur);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'assignation des rôles pour l'utilisateur {NomUtilisateur}", utilisateur.NomUtilisateur);
        }
    }
}
