using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using TypeActifDonneesEntity = DataHubGatineau.Domain.Entites.TypeActifDonnees;

namespace DataHubGatineau.Application.Services;

/// <summary>
/// Implémentation du service des actifs de données.
/// </summary>
public class ServiceActifDonnees : IServiceActifDonnees
{
    private readonly IUniteDeTravail _uniteDeTravail;
    private readonly ILogger<ServiceActifDonnees> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ServiceActifDonnees"/>.
    /// </summary>
    /// <param name="uniteDeTravail">Unité de travail.</param>
    /// <param name="logger">Logger.</param>
    public ServiceActifDonnees(IUniteDeTravail uniteDeTravail, ILogger<ServiceActifDonnees> logger)
    {
        _uniteDeTravail = uniteDeTravail;
        _logger = logger;
    }

    /// <inheritdoc/>
    public async Task<ActifDonnees?> ObtenirParIdAsync(Guid id)
    {
        // Obtenir l'actif de données depuis la base de données avec les inclusions par défaut
        return await ObtenirParIdAsync(id, ActifDonneesInclude.Basic | ActifDonneesInclude.Type | ActifDonneesInclude.DomaineGouvernance);
    }

    /// <inheritdoc/>
    public async Task<ActifDonnees?> ObtenirParIdAsync(Guid id, ActifDonneesInclude includes)
    {
        _logger.LogInformation("Obtention de l'actif de données {Id} avec les inclusions: {Includes}", id, includes);

        // Obtenir l'actif de données depuis la base de données avec les inclusions spécifiées
        return await _uniteDeTravail.ActifsDonnees.ObtenirParIdAsync(id, includes);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirTousAsync()
    {
        // Obtenir les actifs de données depuis la base de données avec les inclusions par défaut
        return await ObtenirTousAsync(ActifDonneesInclude.Basic | ActifDonneesInclude.Type);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirTousAsync(ActifDonneesInclude includes)
    {
        _logger.LogInformation("Obtention de tous les actifs de données avec les inclusions: {Includes}", includes);

        // Obtenir les actifs de données depuis la base de données avec les inclusions spécifiées
        return await _uniteDeTravail.ActifsDonnees.ObtenirTousAsync(includes);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParTypeAsync(string type)
    {
        // Obtenir les actifs de données depuis la base de données
        return await _uniteDeTravail.ActifsDonnees.ObtenirParTypeAsync(type);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParClassificationAsync(ClassificationSensibilite classification)
    {
        // Obtenir les actifs de données depuis la base de données
        return await _uniteDeTravail.ActifsDonnees.ObtenirParClassificationAsync(classification);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParProprietaireAsync(string proprietaire)
    {
        // Obtenir les actifs de données depuis la base de données
        return await _uniteDeTravail.ActifsDonnees.ObtenirParProprietaireAsync(proprietaire);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParTermeGlossaireAsync(int termeId)
    {
        // Obtenir les actifs de données depuis la base de données
        // Note: Cette méthode n'est pas encore implémentée dans le dépôt
        // Pour l'instant, on récupère tous les actifs
        return await ObtenirTousAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParDomaineGouvernanceAsync(Guid domaineGouvernanceId)
    {
        return await _uniteDeTravail.ActifsDonnees.ObtenirParDomaineGouvernanceAsync(domaineGouvernanceId);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParConnexionSourceDonneesAsync(Guid connexionSourceDonneesId)
    {
        return await _uniteDeTravail.ActifsDonnees.ObtenirParConnexionSourceDonneesAsync(connexionSourceDonneesId);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParStatutAsync(string statut)
    {
        return await _uniteDeTravail.ActifsDonnees.ObtenirParStatutAsync(statut);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirLignageAsync(Guid actifId)
    {
        // Obtenir les actifs de données depuis la base de données
        // Note: Cette méthode n'est pas encore implémentée dans le dépôt
        // Pour l'instant, on récupère tous les actifs
        return await ObtenirTousAsync();
    }

    /// <inheritdoc/>
    public async Task<ActifDonnees> AjouterAsync(ActifDonnees actif)
    {
        // Vérifier que l'actif n'est pas null
        if (actif == null)
        {
            throw new ArgumentNullException(nameof(actif), "L'actif de données ne peut pas être null.");
        }

        // Définir les valeurs par défaut si nécessaire
        if (actif.Id == Guid.Empty)
        {
            actif.Id = Guid.NewGuid();
        }

        var maintenant = DateTime.UtcNow;
        actif.DateCreation = maintenant;
        actif.DateModification = maintenant;

        if (string.IsNullOrEmpty(actif.CreePar))
        {
            actif.CreePar = "Système";
        }

        if (string.IsNullOrEmpty(actif.ModifiePar))
        {
            actif.ModifiePar = "Système";
        }

        // Ajouter l'actif de données
        var result = await _uniteDeTravail.ActifsDonnees.AjouterAsync(actif);

        // Sauvegarder les changements dans la base de données
        await _uniteDeTravail.EnregistrerChangementsAsync();

        return result;
    }

    /// <inheritdoc/>
    public async Task<ActifDonnees> MettreAJourAsync(ActifDonnees actif)
    {
        // Vérifier que l'actif n'est pas null
        if (actif == null)
        {
            throw new ArgumentNullException(nameof(actif), "L'actif de données ne peut pas être null.");
        }

        // Vérifier que l'ID est valide
        if (actif.Id == Guid.Empty)
        {
            throw new ArgumentException("L'ID de l'actif de données ne peut pas être vide.", nameof(actif));
        }

        // Mettre à jour la date de modification
        actif.DateModification = DateTime.UtcNow;

        if (string.IsNullOrEmpty(actif.ModifiePar))
        {
            actif.ModifiePar = "Système";
        }

        // Mettre à jour l'actif de données
        var result = await _uniteDeTravail.ActifsDonnees.MettreAJourAsync(actif);

        // Sauvegarder les changements dans la base de données
        await _uniteDeTravail.EnregistrerChangementsAsync();

        return result;
    }

    /// <inheritdoc/>
    public async Task<bool> SupprimerAsync(Guid id)
    {
        // Vérifier que l'ID est valide
        if (id == Guid.Empty)
        {
            throw new ArgumentException("L'ID de l'actif de données ne peut pas être vide.", nameof(id));
        }

        // Supprimer l'actif de données
        var result = await _uniteDeTravail.ActifsDonnees.SupprimerAsync(id);

        // Sauvegarder les changements dans la base de données
        await _uniteDeTravail.EnregistrerChangementsAsync();

        return result;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TypeActifDonneesEntity>> ObtenirTypesActifDonneesAsync()
    {
        // Obtenir tous les types d'actifs de données
        var types = await _uniteDeTravail.TypesActifDonnees.ObtenirTousAsync();
        return types;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<FormatActifDonnees>> ObtenirFormatsActifDonneesAsync()
    {
        // Obtenir tous les formats d'actifs de données
        var formats = await _uniteDeTravail.FormatsActifDonnees.ObtenirTousAsync();
        return formats;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<SourceActifDonnees>> ObtenirSourcesActifDonneesAsync()
    {
        // Obtenir toutes les sources d'actifs de données
        var sources = await _uniteDeTravail.SourcesActifDonnees.ObtenirTousAsync();
        return sources;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<FrequenceMiseAJour>> ObtenirFrequencesMiseAJourAsync()
    {
        // Obtenir toutes les fréquences de mise à jour
        var frequences = await _uniteDeTravail.FrequencesMiseAJour.ObtenirTousAsync();
        return frequences;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<StatutActifDonnees>> ObtenirStatutsActifDonneesAsync()
    {
        // Obtenir tous les statuts d'actifs de données
        var statuts = await _uniteDeTravail.StatutsActifDonnees.ObtenirTousAsync();
        return statuts;
    }

    /// <inheritdoc/>
    public async Task<int> MettreAJourValeursParDefautAsync()
    {
        // Obtenir tous les actifs de données
        var actifs = await _uniteDeTravail.ActifsDonnees.ObtenirTousAsync();

        // Obtenir tous les types et sources disponibles
        var types = await ObtenirTypesActifDonneesAsync();
        var sources = await ObtenirSourcesActifDonneesAsync();

        // Obtener todos los tipos y fuentes disponibles
        _logger.LogInformation($"Types disponibles: {types.Count()}");
        foreach (var type in types)
        {
            _logger.LogInformation($"Type: {type.Id} - {type.Nom}");
        }

        _logger.LogInformation($"Sources disponibles: {sources.Count()}");
        foreach (var source in sources)
        {
            _logger.LogInformation($"Source: {source.Id} - {source.Nom}");
        }

        int compteurMisesAJour = 0;

        // Asignar tipos y fuentes variados a los activos
        var typesList = types.ToList();
        var sourcesList = sources.ToList();
        var random = new Random();

        foreach (var actif in actifs)
        {
            bool modifie = false;

            // Mettre à jour le type si nécessaire avec un type aléatoire
            if (actif.TypeActifDonneesId == null || actif.TypeActifDonneesId == Guid.Empty)
            {
                var randomTypeIndex = random.Next(typesList.Count);
                var selectedType = typesList[randomTypeIndex];
                actif.TypeActifDonneesId = selectedType.Id;
                _logger.LogInformation($"Actif {actif.Nom}: Type assigné = {selectedType.Nom}");
                modifie = true;
            }

            // Mettre à jour la source si nécessaire avec une source aléatoire
            if (actif.SourceActifDonneesId == null || actif.SourceActifDonneesId == Guid.Empty)
            {
                var randomSourceIndex = random.Next(sourcesList.Count);
                var selectedSource = sourcesList[randomSourceIndex];
                actif.SourceActifDonneesId = selectedSource.Id;
                _logger.LogInformation($"Actif {actif.Nom}: Source assignée = {selectedSource.Nom}");
                modifie = true;
            }

            // Mettre à jour l'actif si nécessaire
            if (modifie)
            {
                actif.DateModification = DateTime.UtcNow;
                actif.ModifiePar = "Système (mise à jour automatique)";
                await _uniteDeTravail.ActifsDonnees.MettreAJourAsync(actif);
                compteurMisesAJour++;
            }
        }

        return compteurMisesAJour;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> RechercherAsync(string terme)
    {
        _logger.LogInformation("Recherche d'actifs de données avec le terme: {Terme}", terme);

        if (string.IsNullOrWhiteSpace(terme))
        {
            return Enumerable.Empty<ActifDonnees>();
        }

        // Utiliser le dépôt pour effectuer la recherche
        // Si le dépôt n'implémente pas encore cette méthode, on peut utiliser une solution temporaire
        try
        {
            // Essayer d'utiliser la méthode du dépôt si elle existe
            return await _uniteDeTravail.ActifsDonnees.RechercherAsync(terme);
        }
        catch (NotImplementedException)
        {
            // Solution temporaire: récupérer tous les actifs et filtrer en mémoire
            _logger.LogWarning("La méthode RechercherAsync n'est pas implémentée dans le dépôt. Utilisation d'une solution temporaire.");

            var actifs = await _uniteDeTravail.ActifsDonnees.ObtenirTousAsync(ActifDonneesInclude.Basic);

            // Recherche dans plusieurs champs
            return actifs.Where(a =>
                (a.Nom != null && a.Nom.Contains(terme, StringComparison.OrdinalIgnoreCase)) ||
                (a.Description != null && a.Description.Contains(terme, StringComparison.OrdinalIgnoreCase)) ||
                (a.Proprietaire != null && a.Proprietaire.Contains(terme, StringComparison.OrdinalIgnoreCase)) ||
                (a.CheminAcces != null && a.CheminAcces.Contains(terme, StringComparison.OrdinalIgnoreCase))
            );
        }
    }

    /// <summary>
    /// Recherche des actifs de données par terme de recherche avec les inclusions spécifiées.
    /// </summary>
    /// <param name="terme">Terme de recherche.</param>
    /// <param name="includes">Propriétés de navigation à inclure.</param>
    /// <returns>Une collection d'actifs de données correspondant au terme de recherche.</returns>
    public async Task<IEnumerable<ActifDonnees>> RechercherAsync(string terme, ActifDonneesInclude includes)
    {
        _logger.LogInformation("Recherche d'actifs de données avec le terme: {Terme} et inclusions: {Includes}", terme, includes);

        if (string.IsNullOrWhiteSpace(terme))
        {
            return Enumerable.Empty<ActifDonnees>();
        }

        // Utiliser le dépôt pour effectuer la recherche avec inclusions
        try
        {
            // Essayer d'utiliser la méthode du dépôt si elle existe
            return await _uniteDeTravail.ActifsDonnees.RechercherAsync(terme, includes);
        }
        catch (NotImplementedException)
        {
            // Solution temporaire: récupérer tous les actifs avec inclusions et filtrer en mémoire
            _logger.LogWarning("La méthode RechercherAsync avec inclusions n'est pas implémentée dans le dépôt. Utilisation d'une solution temporaire.");

            var actifs = await _uniteDeTravail.ActifsDonnees.ObtenirTousAsync(includes);

            // Recherche dans plusieurs champs
            return actifs.Where(a =>
                (a.Nom != null && a.Nom.Contains(terme, StringComparison.OrdinalIgnoreCase)) ||
                (a.Description != null && a.Description.Contains(terme, StringComparison.OrdinalIgnoreCase)) ||
                (a.Proprietaire != null && a.Proprietaire.Contains(terme, StringComparison.OrdinalIgnoreCase)) ||
                (a.CheminAcces != null && a.CheminAcces.Contains(terme, StringComparison.OrdinalIgnoreCase))
            );
        }
    }
}
