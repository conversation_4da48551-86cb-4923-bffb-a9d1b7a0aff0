using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;
using System.Text.Json;

namespace DataHubGatineau.Application.Services;

/// <summary>
/// Service pour l'exécution des règles de qualité des données.
/// </summary>
public class ServiceExecutionRegleQualite : IServiceExecutionRegleQualite
{
    private readonly IUniteDeTravail _uniteDeTravail;
    private readonly ILogger<ServiceExecutionRegleQualite> _logger;
    private readonly string _connectionString;

    public ServiceExecutionRegleQualite(
        IUniteDeTravail uniteDeTravail,
        ILogger<ServiceExecutionRegleQualite> logger,
        IConfiguration configuration)
    {
        _uniteDeTravail = uniteDeTravail;
        _logger = logger;
        _connectionString = configuration.GetConnectionString("DefaultConnection") 
            ?? throw new ArgumentNullException(nameof(configuration));
    }

    /// <summary>
    /// Exécute une règle de qualité spécifique.
    /// </summary>
    public async Task<ResultatRegleQualite> ExecuterRegleAsync(Guid regleId)
    {
        _logger.LogInformation("Début de l'exécution de la règle de qualité {RegleId}", regleId);

        var regle = await _uniteDeTravail.ReglesQualite.ObtenirParIdAsync(regleId);
        if (regle == null)
        {
            throw new ArgumentException($"Règle de qualité avec l'ID {regleId} non trouvée");
        }

        var resultat = new ResultatRegleQualite
        {
            Id = Guid.NewGuid(),
            RegleQualiteId = regleId,
            DateExecution = DateTime.UtcNow,
            StatutExecution = "En cours",
            CreePar = "Système",
            ModifiePar = "Système",
            DateCreation = DateTime.UtcNow,
            DateModification = DateTime.UtcNow
        };

        try
        {
            // Exécuter la règle selon son type
            switch (regle.TypeRegle)
            {
                case "Complétude":
                    await ExecuterRegleCompletude(regle, resultat);
                    break;
                case "Unicité":
                    await ExecuterRegleUnicite(regle, resultat);
                    break;
                case "Validité":
                    await ExecuterRegleValidite(regle, resultat);
                    break;
                case "Cohérence":
                    await ExecuterRegleCoherence(regle, resultat);
                    break;
                case "Conformité":
                    await ExecuterRegleConformite(regle, resultat);
                    break;
                default:
                    await ExecuterReglePersonnalisee(regle, resultat);
                    break;
            }

            resultat.StatutExecution = "Terminé";
            _logger.LogInformation("Règle de qualité {RegleId} exécutée avec succès. Score: {Score}", 
                regleId, resultat.ValeurMesuree);
        }
        catch (Exception ex)
        {
            resultat.StatutExecution = "Erreur";
            resultat.MessageErreur = ex.Message;
            resultat.ValeurMesuree = 0;
            _logger.LogError(ex, "Erreur lors de l'exécution de la règle de qualité {RegleId}", regleId);
        }

        // Sauvegarder le résultat
        await _uniteDeTravail.ResultatsRegleQualite.AjouterAsync(resultat);
        await _uniteDeTravail.SauvegarderAsync();

        return resultat;
    }

    /// <summary>
    /// Exécute toutes les règles de qualité pour un actif de données.
    /// </summary>
    public async Task<IEnumerable<ResultatRegleQualite>> ExecuterToutesReglesAsync(Guid actifDonneesId)
    {
        _logger.LogInformation("Exécution de toutes les règles de qualité pour l'actif {ActifId}", actifDonneesId);

        var regles = await _uniteDeTravail.ReglesQualite.ObtenirParActifDonneesAsync(actifDonneesId);
        var resultats = new List<ResultatRegleQualite>();

        foreach (var regle in regles)
        {
            try
            {
                var resultat = await ExecuterRegleAsync(regle.Id);
                resultats.Add(resultat);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'exécution de la règle {RegleId} pour l'actif {ActifId}", 
                    regle.Id, actifDonneesId);
            }
        }

        return resultats;
    }

    /// <summary>
    /// Exécute une règle de complétude (vérification des valeurs nulles).
    /// </summary>
    private async Task ExecuterRegleCompletude(RegleQualite regle, ResultatRegleQualite resultat)
    {
        var actif = await _uniteDeTravail.ActifsDonnees.ObtenirParIdAsync(regle.ActifDonneesId);
        if (actif?.ConnexionSourceDonnees == null)
        {
            throw new InvalidOperationException("Connexion à la source de données non trouvée");
        }

        var parametres = JsonSerializer.Deserialize<Dictionary<string, object>>(regle.ParametresJson ?? "{}");
        var colonnes = parametres.GetValueOrDefault("colonnes", "").ToString();
        var table = parametres.GetValueOrDefault("table", actif.Nom).ToString();

        var requeteSQL = $@"
            SELECT 
                COUNT(*) as TotalLignes,
                COUNT({colonnes}) as LignesCompletes
            FROM {table}";

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(requeteSQL, connection);
        using var reader = await command.ExecuteReaderAsync();
        
        if (await reader.ReadAsync())
        {
            var totalLignes = reader.GetInt32("TotalLignes");
            var lignesCompletes = reader.GetInt32("LignesCompletes");
            
            resultat.ValeurMesuree = totalLignes > 0 ? (double)lignesCompletes / totalLignes * 100 : 0;
            resultat.NombreEnregistrements = totalLignes;
            resultat.NombreAnomalies = totalLignes - lignesCompletes;
        }
    }

    /// <summary>
    /// Exécute une règle d'unicité (vérification des doublons).
    /// </summary>
    private async Task ExecuterRegleUnicite(RegleQualite regle, ResultatRegleQualite resultat)
    {
        var actif = await _uniteDeTravail.ActifsDonnees.ObtenirParIdAsync(regle.ActifDonneesId);
        if (actif?.ConnexionSourceDonnees == null)
        {
            throw new InvalidOperationException("Connexion à la source de données non trouvée");
        }

        var parametres = JsonSerializer.Deserialize<Dictionary<string, object>>(regle.ParametresJson ?? "{}");
        var colonnes = parametres.GetValueOrDefault("colonnes", "").ToString();
        var table = parametres.GetValueOrDefault("table", actif.Nom).ToString();

        var requeteSQL = $@"
            SELECT 
                COUNT(*) as TotalLignes,
                COUNT(DISTINCT {colonnes}) as LignesUniques
            FROM {table}";

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(requeteSQL, connection);
        using var reader = await command.ExecuteReaderAsync();
        
        if (await reader.ReadAsync())
        {
            var totalLignes = reader.GetInt32("TotalLignes");
            var lignesUniques = reader.GetInt32("LignesUniques");
            
            resultat.ValeurMesuree = totalLignes > 0 ? (double)lignesUniques / totalLignes * 100 : 0;
            resultat.NombreEnregistrements = totalLignes;
            resultat.NombreAnomalies = totalLignes - lignesUniques;
        }
    }

    /// <summary>
    /// Exécute une règle de validité (vérification du format).
    /// </summary>
    private async Task ExecuterRegleValidite(RegleQualite regle, ResultatRegleQualite resultat)
    {
        var actif = await _uniteDeTravail.ActifsDonnees.ObtenirParIdAsync(regle.ActifDonneesId);
        if (actif?.ConnexionSourceDonnees == null)
        {
            throw new InvalidOperationException("Connexion à la source de données non trouvée");
        }

        var parametres = JsonSerializer.Deserialize<Dictionary<string, object>>(regle.ParametresJson ?? "{}");
        var colonne = parametres.GetValueOrDefault("colonne", "").ToString();
        var pattern = parametres.GetValueOrDefault("pattern", "").ToString();
        var table = parametres.GetValueOrDefault("table", actif.Nom).ToString();

        var requeteSQL = $@"
            SELECT 
                COUNT(*) as TotalLignes,
                SUM(CASE WHEN {colonne} LIKE '{pattern}' THEN 1 ELSE 0 END) as LignesValides
            FROM {table}
            WHERE {colonne} IS NOT NULL";

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(requeteSQL, connection);
        using var reader = await command.ExecuteReaderAsync();
        
        if (await reader.ReadAsync())
        {
            var totalLignes = reader.GetInt32("TotalLignes");
            var lignesValides = reader.GetInt32("LignesValides");
            
            resultat.ValeurMesuree = totalLignes > 0 ? (double)lignesValides / totalLignes * 100 : 0;
            resultat.NombreEnregistrements = totalLignes;
            resultat.NombreAnomalies = totalLignes - lignesValides;
        }
    }

    /// <summary>
    /// Exécute une règle de cohérence (vérification des relations).
    /// </summary>
    private async Task ExecuterRegleCoherence(RegleQualite regle, ResultatRegleQualite resultat)
    {
        // Implémentation pour les règles de cohérence
        var parametres = JsonSerializer.Deserialize<Dictionary<string, object>>(regle.ParametresJson ?? "{}");
        var requeteSQL = parametres.GetValueOrDefault("requete", "").ToString();

        if (string.IsNullOrEmpty(requeteSQL))
        {
            throw new InvalidOperationException("Requête SQL non définie pour la règle de cohérence");
        }

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(requeteSQL, connection);
        var result = await command.ExecuteScalarAsync();
        
        resultat.ValeurMesuree = Convert.ToDouble(result ?? 0);
    }

    /// <summary>
    /// Exécute une règle de conformité (vérification des standards).
    /// </summary>
    private async Task ExecuterRegleConformite(RegleQualite regle, ResultatRegleQualite resultat)
    {
        // Implémentation pour les règles de conformité
        var parametres = JsonSerializer.Deserialize<Dictionary<string, object>>(regle.ParametresJson ?? "{}");
        var requeteSQL = parametres.GetValueOrDefault("requete", "").ToString();

        if (string.IsNullOrEmpty(requeteSQL))
        {
            throw new InvalidOperationException("Requête SQL non définie pour la règle de conformité");
        }

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(requeteSQL, connection);
        var result = await command.ExecuteScalarAsync();
        
        resultat.ValeurMesuree = Convert.ToDouble(result ?? 0);
    }

    /// <summary>
    /// Exécute une règle personnalisée.
    /// </summary>
    private async Task ExecuterReglePersonnalisee(RegleQualite regle, ResultatRegleQualite resultat)
    {
        if (string.IsNullOrEmpty(regle.RequeteSQL))
        {
            throw new InvalidOperationException("Requête SQL non définie pour la règle personnalisée");
        }

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(regle.RequeteSQL, connection);
        var result = await command.ExecuteScalarAsync();
        
        resultat.ValeurMesuree = Convert.ToDouble(result ?? 0);
    }
}
