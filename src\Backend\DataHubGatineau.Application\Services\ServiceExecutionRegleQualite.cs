using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Data;
using System.Data.SqlClient;
using System.Text.Json;

namespace DataHubGatineau.Application.Services;

/// <summary>
/// Service pour l'exécution des règles de qualité des données.
/// </summary>
public class ServiceExecutionRegleQualite : IServiceExecutionRegleQualite
{
    private readonly IUniteDeTravail _uniteDeTravail;
    private readonly ILogger<ServiceExecutionRegleQualite> _logger;
    private readonly string _connectionString;

    public ServiceExecutionRegleQualite(
        IUniteDeTravail uniteDeTravail,
        ILogger<ServiceExecutionRegleQualite> logger,
        IConfiguration configuration)
    {
        _uniteDeTravail = uniteDeTravail;
        _logger = logger;
        _connectionString = configuration.GetConnectionString("DefaultConnection") 
            ?? throw new ArgumentNullException(nameof(configuration));
    }

    /// <summary>
    /// Exécute une règle de qualité spécifique.
    /// </summary>
    public async Task<ResultatRegleQualite> ExecuterRegleAsync(Guid regleId)
    {
        _logger.LogInformation("Début de l'exécution de la règle de qualité {RegleId}", regleId);

        var regle = await _uniteDeTravail.ReglesQualite.ObtenirParIdAsync(regleId);
        if (regle == null)
        {
            throw new ArgumentException($"Règle de qualité avec l'ID {regleId} non trouvée");
        }

        var resultat = new ResultatRegleQualite
        {
            Id = Guid.NewGuid(),
            RegleQualiteId = regleId,
            DateExecution = DateTime.UtcNow,
            Statut = StatutResultatRegleQualite.Succes, // Sera mis à jour selon le résultat
            Message = "Exécution en cours",
            CreePar = "Système",
            ModifiePar = "Système",
            DateCreation = DateTime.UtcNow,
            DateModification = DateTime.UtcNow
        };

        try
        {
            // Exécuter la règle selon son type
            switch (regle.Type)
            {
                case TypeRegleQualite.Completude:
                    await ExecuterRegleCompletude(regle, resultat);
                    break;
                case TypeRegleQualite.Unicite:
                    await ExecuterRegleUnicite(regle, resultat);
                    break;
                case TypeRegleQualite.Validite:
                    await ExecuterRegleValidite(regle, resultat);
                    break;
                case TypeRegleQualite.Coherence:
                    await ExecuterRegleCoherence(regle, resultat);
                    break;
                case TypeRegleQualite.Exactitude:
                    await ExecuterRegleExactitude(regle, resultat);
                    break;
                case TypeRegleQualite.Integrite:
                    await ExecuterRegleIntegrite(regle, resultat);
                    break;
                default:
                    await ExecuterReglePersonnalisee(regle, resultat);
                    break;
            }

            resultat.Statut = StatutResultatRegleQualite.Succes;
            resultat.Message = "Exécution terminée avec succès";
            _logger.LogInformation("Règle de qualité {RegleId} exécutée avec succès. Score: {Score}",
                regleId, resultat.ValeurMesuree);
        }
        catch (Exception ex)
        {
            resultat.Statut = StatutResultatRegleQualite.Erreur;
            resultat.Message = ex.Message;
            resultat.ValeurMesuree = 0;
            _logger.LogError(ex, "Erreur lors de l'exécution de la règle de qualité {RegleId}", regleId);
        }

        // Sauvegarder le résultat
        await _uniteDeTravail.ResultatsRegleQualite.AjouterAsync(resultat);
        await _uniteDeTravail.SauvegarderAsync();

        return resultat;
    }

    /// <summary>
    /// Exécute toutes les règles de qualité pour un actif de données.
    /// </summary>
    public async Task<IEnumerable<ResultatRegleQualite>> ExecuterToutesReglesAsync(Guid actifDonneesId)
    {
        _logger.LogInformation("Exécution de toutes les règles de qualité pour l'actif {ActifId}", actifDonneesId);

        var regles = await _uniteDeTravail.ReglesQualite.ObtenirParActifDonneesAsync(actifDonneesId);
        var resultats = new List<ResultatRegleQualite>();

        foreach (var regle in regles)
        {
            try
            {
                var resultat = await ExecuterRegleAsync(regle.Id);
                resultats.Add(resultat);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'exécution de la règle {RegleId} pour l'actif {ActifId}", 
                    regle.Id, actifDonneesId);
            }
        }

        return resultats;
    }

    /// <summary>
    /// Exécute une règle de complétude (vérification des valeurs nulles).
    /// </summary>
    private async Task ExecuterRegleCompletude(RegleQualite regle, ResultatRegleQualite resultat)
    {
        var actif = await _uniteDeTravail.ActifsDonnees.ObtenirParIdAsync(regle.ActifDonneesId);
        if (actif?.ConnexionSourceDonnees == null)
        {
            throw new InvalidOperationException("Connexion à la source de données non trouvée");
        }

        // Utiliser le champ spécifié dans la règle ou une configuration par défaut
        var colonnes = regle.Champ ?? "*";
        var table = actif.Nom;

        var requeteSQL = $@"
            SELECT
                COUNT(*) as TotalLignes,
                COUNT({colonnes}) as LignesCompletes
            FROM {table}";

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(requeteSQL, connection);
        using var reader = await command.ExecuteReaderAsync();
        
        if (await reader.ReadAsync())
        {
            var totalLignes = reader.GetInt32("TotalLignes");
            var lignesCompletes = reader.GetInt32("LignesCompletes");
            
            resultat.ValeurMesuree = totalLignes > 0 ? (double)lignesCompletes / totalLignes * 100 : 0;
            // Note: Les propriétés NombreEnregistrements et NombreAnomalies n'existent pas dans l'entité
            // Ces informations peuvent être stockées dans le Message si nécessaire
            resultat.Message = $"Total: {totalLignes}, Complètes: {lignesCompletes}, Anomalies: {totalLignes - lignesCompletes}";
        }
    }

    /// <summary>
    /// Exécute une règle d'unicité (vérification des doublons).
    /// </summary>
    private async Task ExecuterRegleUnicite(RegleQualite regle, ResultatRegleQualite resultat)
    {
        var actif = await _uniteDeTravail.ActifsDonnees.ObtenirParIdAsync(regle.ActifDonneesId);
        if (actif?.ConnexionSourceDonnees == null)
        {
            throw new InvalidOperationException("Connexion à la source de données non trouvée");
        }

        // Utiliser le champ spécifié dans la règle
        var colonnes = regle.Champ ?? "Id";
        var table = actif.Nom;

        var requeteSQL = $@"
            SELECT
                COUNT(*) as TotalLignes,
                COUNT(DISTINCT {colonnes}) as LignesUniques
            FROM {table}";

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(requeteSQL, connection);
        using var reader = await command.ExecuteReaderAsync();
        
        if (await reader.ReadAsync())
        {
            var totalLignes = reader.GetInt32("TotalLignes");
            var lignesUniques = reader.GetInt32("LignesUniques");
            
            resultat.ValeurMesuree = totalLignes > 0 ? (double)lignesUniques / totalLignes * 100 : 0;
            resultat.Message = $"Total: {totalLignes}, Uniques: {lignesUniques}, Doublons: {totalLignes - lignesUniques}";
        }
    }

    /// <summary>
    /// Exécute une règle de validité (vérification du format).
    /// </summary>
    private async Task ExecuterRegleValidite(RegleQualite regle, ResultatRegleQualite resultat)
    {
        var actif = await _uniteDeTravail.ActifsDonnees.ObtenirParIdAsync(regle.ActifDonneesId);
        if (actif?.ConnexionSourceDonnees == null)
        {
            throw new InvalidOperationException("Connexion à la source de données non trouvée");
        }

        // Utiliser le champ et l'expression de la règle
        var colonne = regle.Champ ?? "Id";
        var pattern = regle.Expression ?? "%"; // Pattern par défaut
        var table = actif.Nom;

        var requeteSQL = $@"
            SELECT
                COUNT(*) as TotalLignes,
                SUM(CASE WHEN {colonne} LIKE '{pattern}' THEN 1 ELSE 0 END) as LignesValides
            FROM {table}
            WHERE {colonne} IS NOT NULL";

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(requeteSQL, connection);
        using var reader = await command.ExecuteReaderAsync();
        
        if (await reader.ReadAsync())
        {
            var totalLignes = reader.GetInt32("TotalLignes");
            var lignesValides = reader.GetInt32("LignesValides");
            
            resultat.ValeurMesuree = totalLignes > 0 ? (double)lignesValides / totalLignes * 100 : 0;
            resultat.Message = $"Total: {totalLignes}, Valides: {lignesValides}, Invalides: {totalLignes - lignesValides}";
        }
    }

    /// <summary>
    /// Exécute une règle de cohérence (vérification des relations).
    /// </summary>
    private async Task ExecuterRegleCoherence(RegleQualite regle, ResultatRegleQualite resultat)
    {
        // Implémentation pour les règles de cohérence
        var requeteSQL = regle.Expression;

        if (string.IsNullOrEmpty(requeteSQL))
        {
            throw new InvalidOperationException("Expression SQL non définie pour la règle de cohérence");
        }

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var command = new SqlCommand(requeteSQL, connection);
        var result = await command.ExecuteScalarAsync();
        
        resultat.ValeurMesuree = Convert.ToDouble(result ?? 0);
    }

    /// <summary>
    /// Exécute une règle d'exactitude (vérification de la précision).
    /// </summary>
    private async Task ExecuterRegleExactitude(RegleQualite regle, ResultatRegleQualite resultat)
    {
        // Implémentation pour les règles d'exactitude
        var requeteSQL = regle.Expression;

        if (string.IsNullOrEmpty(requeteSQL))
        {
            throw new InvalidOperationException("Expression SQL non définie pour la règle d'exactitude");
        }

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        using var command = new SqlCommand(requeteSQL, connection);
        var result = await command.ExecuteScalarAsync();

        resultat.ValeurMesuree = Convert.ToDouble(result ?? 0);
        resultat.Message = $"Valeur mesurée: {resultat.ValeurMesuree}";
    }

    /// <summary>
    /// Exécute une règle d'intégrité (vérification des contraintes).
    /// </summary>
    private async Task ExecuterRegleIntegrite(RegleQualite regle, ResultatRegleQualite resultat)
    {
        // Implémentation pour les règles d'intégrité
        var requeteSQL = regle.Expression;

        if (string.IsNullOrEmpty(requeteSQL))
        {
            throw new InvalidOperationException("Expression SQL non définie pour la règle d'intégrité");
        }

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        using var command = new SqlCommand(requeteSQL, connection);
        var result = await command.ExecuteScalarAsync();

        resultat.ValeurMesuree = Convert.ToDouble(result ?? 0);
        resultat.Message = $"Valeur mesurée: {resultat.ValeurMesuree}";
    }

    /// <summary>
    /// Exécute une règle personnalisée.
    /// </summary>
    private async Task ExecuterReglePersonnalisee(RegleQualite regle, ResultatRegleQualite resultat)
    {
        if (string.IsNullOrEmpty(regle.Expression))
        {
            throw new InvalidOperationException("Expression SQL non définie pour la règle personnalisée");
        }

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        using var command = new SqlCommand(regle.Expression, connection);
        var result = await command.ExecuteScalarAsync();

        resultat.ValeurMesuree = Convert.ToDouble(result ?? 0);
        resultat.Message = $"Valeur mesurée: {resultat.ValeurMesuree}";
    }
}
