using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;

namespace DataHubGatineau.Application.Services;

/// <summary>
/// Implémentation du service des produits de données.
/// </summary>
public class ServiceProduitDonnees : IServiceProduitDonnees
{
    private readonly IUniteDeTravail _uniteDeTravail;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ServiceProduitDonnees"/>.
    /// </summary>
    /// <param name="uniteDeTravail">Unité de travail.</param>
    public ServiceProduitDonnees(IUniteDeTravail uniteDeTravail)
    {
        _uniteDeTravail = uniteDeTravail;
    }

    /// <inheritdoc/>
    public async Task<ProduitDonnees?> ObtenirParIdAsync(Guid id)
    {
        return await _uniteDeTravail.ProduitsDonnees.ObtenirParIdAsync(id);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ProduitDonnees>> ObtenirTousAsync()
    {
        return await _uniteDeTravail.ProduitsDonnees.ObtenirTousAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ProduitDonnees>> ObtenirParDomaineGouvernanceAsync(Guid domaineGouvernanceId)
    {
        return await _uniteDeTravail.ProduitsDonnees.ObtenirParDomaineGouvernanceAsync(domaineGouvernanceId);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ProduitDonnees>> ObtenirParProprietaireAsync(string proprietaire)
    {
        return await _uniteDeTravail.ProduitsDonnees.ObtenirParProprietaireAsync(proprietaire);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ProduitDonnees>> ObtenirParActifDonneesAsync(Guid actifDonneesId)
    {
        return await _uniteDeTravail.ProduitsDonnees.ObtenirParActifDonneesAsync(actifDonneesId);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ProduitDonnees>> RechercherAsync(string motCle)
    {
        return await _uniteDeTravail.ProduitsDonnees.RechercherAsync(motCle);
    }

    /// <inheritdoc/>
    public async Task<ProduitDonnees> AjouterAsync(ProduitDonnees produitDonnees)
    {
        // Vérifier que le produit de données n'est pas null
        if (produitDonnees == null)
        {
            throw new ArgumentNullException(nameof(produitDonnees), "Le produit de données ne peut pas être null.");
        }

        // Vérifier que le domaine de gouvernance existe
        var domaineGouvernance = await _uniteDeTravail.DomainesGouvernance.ObtenirParIdAsync(produitDonnees.DomaineGouvernanceId);
        if (domaineGouvernance == null)
        {
            throw new ArgumentException($"Le domaine de gouvernance avec l'ID {produitDonnees.DomaineGouvernanceId} n'existe pas.");
        }

        // Définir les valeurs par défaut si nécessaire
        if (produitDonnees.Id == Guid.Empty)
        {
            produitDonnees.Id = Guid.NewGuid();
        }

        var maintenant = DateTime.UtcNow;
        produitDonnees.DateCreation = maintenant;
        produitDonnees.DateModification = maintenant;

        if (string.IsNullOrEmpty(produitDonnees.CreePar))
        {
            produitDonnees.CreePar = "Système";
        }

        if (string.IsNullOrEmpty(produitDonnees.ModifiePar))
        {
            produitDonnees.ModifiePar = "Système";
        }

        // Ajouter le produit de données
        var produitAjoute = await _uniteDeTravail.ProduitsDonnees.AjouterAsync(produitDonnees);
        await _uniteDeTravail.EnregistrerChangementsAsync();
        return produitAjoute;
    }

    /// <inheritdoc/>
    public async Task<ProduitDonnees> MettreAJourAsync(ProduitDonnees produitDonnees)
    {
        // Vérifier que le produit de données n'est pas null
        if (produitDonnees == null)
        {
            throw new ArgumentNullException(nameof(produitDonnees), "Le produit de données ne peut pas être null.");
        }

        // Vérifier que le produit de données existe
        var produitExistant = await _uniteDeTravail.ProduitsDonnees.ObtenirParIdAsync(produitDonnees.Id);
        if (produitExistant == null)
        {
            throw new ArgumentException($"Le produit de données avec l'ID {produitDonnees.Id} n'existe pas.");
        }

        // Vérifier que le domaine de gouvernance existe
        var domaineGouvernance = await _uniteDeTravail.DomainesGouvernance.ObtenirParIdAsync(produitDonnees.DomaineGouvernanceId);
        if (domaineGouvernance == null)
        {
            throw new ArgumentException($"Le domaine de gouvernance avec l'ID {produitDonnees.DomaineGouvernanceId} n'existe pas.");
        }

        // Mettre à jour les propriétés
        produitDonnees.DateCreation = produitExistant.DateCreation;
        produitDonnees.DateModification = DateTime.UtcNow;
        produitDonnees.CreePar = produitExistant.CreePar;

        if (string.IsNullOrEmpty(produitDonnees.ModifiePar))
        {
            produitDonnees.ModifiePar = "Système";
        }

        // Mettre à jour le produit de données
        var produitMisAJour = await _uniteDeTravail.ProduitsDonnees.MettreAJourAsync(produitDonnees);
        await _uniteDeTravail.EnregistrerChangementsAsync();
        return produitMisAJour;
    }

    /// <inheritdoc/>
    public async Task<bool> SupprimerAsync(Guid id)
    {
        // Vérifier que le produit de données existe
        var produitExistant = await _uniteDeTravail.ProduitsDonnees.ObtenirParIdAsync(id);
        if (produitExistant == null)
        {
            return false;
        }

        // Supprimer le produit de données
        await _uniteDeTravail.ProduitsDonnees.SupprimerAsync(id);
        await _uniteDeTravail.EnregistrerChangementsAsync();
        return true;
    }

    /// <inheritdoc/>
    public async Task<bool> AjouterActifDonneesAsync(Guid produitDonneesId, Guid actifDonneesId)
    {
        // Vérifier que le produit de données existe et charger ses actifs
        var produit = await _uniteDeTravail.ProduitsDonnees.ObtenirParIdAvecActifsAsync(produitDonneesId);
        if (produit == null)
        {
            return false;
        }

        // Vérifier que l'actif de données existe
        var actif = await _uniteDeTravail.ActifsDonnees.ObtenirParIdAsync(actifDonneesId);
        if (actif == null)
        {
            return false;
        }

        // Vérifier si l'actif est déjà dans le produit
        if (produit.ActifsDonnees.Any(a => a.Id == actifDonneesId))
        {
            return true; // L'actif est déjà dans le produit
        }

        // Ajouter l'actif au produit
        produit.ActifsDonnees.Add(actif);
        await _uniteDeTravail.EnregistrerChangementsAsync();
        return true;
    }

    /// <inheritdoc/>
    public async Task<bool> SupprimerActifDonneesAsync(Guid produitDonneesId, Guid actifDonneesId)
    {
        // Vérifier que le produit de données existe et charger ses actifs
        var produit = await _uniteDeTravail.ProduitsDonnees.ObtenirParIdAvecActifsAsync(produitDonneesId);
        if (produit == null)
        {
            return false;
        }

        // Vérifier que l'actif de données existe
        var actif = await _uniteDeTravail.ActifsDonnees.ObtenirParIdAsync(actifDonneesId);
        if (actif == null)
        {
            return false;
        }

        // Vérifier si l'actif est dans le produit
        var actifDansProduit = produit.ActifsDonnees.FirstOrDefault(a => a.Id == actifDonneesId);
        if (actifDansProduit == null)
        {
            return true; // L'actif n'est pas dans le produit
        }

        // Supprimer l'actif du produit
        produit.ActifsDonnees.Remove(actifDansProduit);
        await _uniteDeTravail.EnregistrerChangementsAsync();
        return true;
    }
}
