using DataHubGatineau.Domain.Enums;
using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Domain.Entites;

/// <summary>
/// Représente un actif de données dans le système.
/// </summary>
public class ActifDonnees : EntiteBase
{
    /// <summary>
    /// Obtient ou définit le nom de l'actif de données.
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Nom { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit la description de l'actif de données.
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant du type de l'actif de données.
    /// </summary>
    public Guid? TypeActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit le type de l'actif de données.
    /// </summary>
    public virtual TypeActifDonnees? TypeActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant du format de l'actif de données.
    /// </summary>
    public Guid? FormatActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit le format de l'actif de données.
    /// </summary>
    public virtual FormatActifDonnees? FormatActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de la source de l'actif de données.
    /// </summary>
    public Guid? SourceActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit la source de l'actif de données.
    /// </summary>
    public virtual SourceActifDonnees? SourceActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit le propriétaire de l'actif de données.
    /// </summary>
    [StringLength(100)]
    public string? Proprietaire { get; set; }

    /// <summary>
    /// Obtient ou définit la classification de sensibilité de l'actif de données.
    /// </summary>
    public ClassificationSensibilite ClassificationSensibilite { get; set; }

    /// <summary>
    /// Obtient ou définit la date de dernière mise à jour de l'actif de données.
    /// </summary>
    public DateTime? DateDerniereMiseAJour { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de la fréquence de mise à jour de l'actif de données.
    /// </summary>
    public Guid? FrequenceMiseAJourId { get; set; }

    /// <summary>
    /// Obtient ou définit la fréquence de mise à jour de l'actif de données.
    /// </summary>
    public virtual FrequenceMiseAJour? FrequenceMiseAJour { get; set; }

    /// <summary>
    /// Obtient ou définit le chemin d'accès à l'actif de données.
    /// </summary>
    [StringLength(255)]
    public string? CheminAcces { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant du domaine de gouvernance.
    /// </summary>
    public Guid? DomaineGouvernanceId { get; set; }

    /// <summary>
    /// Obtient ou définit le domaine de gouvernance.
    /// </summary>
    public virtual DomaineGouvernance? DomaineGouvernance { get; set; }

    /// <summary>
    /// Obtient ou définit si cet actif est un élément critique.
    /// </summary>
    public bool EstElementCritique { get; set; } = false;

    /// <summary>
    /// Obtient ou définit l'identifiant de la connexion à la source de données.
    /// </summary>
    public Guid? ConnexionSourceDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit la connexion à la source de données.
    /// </summary>
    public virtual ConnexionSourceDonnees? ConnexionSourceDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant du statut de l'actif de données.
    /// </summary>
    public Guid? StatutActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit le statut de l'actif de données.
    /// </summary>
    public virtual StatutActifDonnees? StatutActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit les métadonnées associées à cet actif de données.
    /// </summary>
    public virtual ICollection<Metadonnee> Metadonnees { get; set; } = new List<Metadonnee>();

    /// <summary>
    /// Obtient ou définit les règles de qualité associées à cet actif de données.
    /// </summary>
    public virtual ICollection<RegleQualite> ReglesQualite { get; set; } = new List<RegleQualite>();

    /// <summary>
    /// Obtient ou définit les termes du glossaire associés à cet actif de données.
    /// </summary>
    public virtual ICollection<TermeGlossaire> TermesGlossaire { get; set; } = new List<TermeGlossaire>();

    /// <summary>
    /// Obtient ou définit les actifs de données sources (lignage entrant).
    /// </summary>
    public virtual ICollection<ActifDonnees> ElementsSources { get; set; } = new List<ActifDonnees>();

    /// <summary>
    /// Obtient ou définit les actifs de données cibles (lignage sortant).
    /// </summary>
    public virtual ICollection<ActifDonnees> ElementsCibles { get; set; } = new List<ActifDonnees>();

    /// <summary>
    /// Obtient ou définit les produits de données auxquels cet actif appartient.
    /// </summary>
    public virtual ICollection<ProduitDonnees> ProduitsDonnees { get; set; } = new List<ProduitDonnees>();

    /// <summary>
    /// Obtient ou définit les workflows d'approbation associés à cet actif.
    /// </summary>
    public virtual ICollection<WorkflowApprobation> WorkflowsApprobation { get; set; } = new List<WorkflowApprobation>();

    /// <summary>
    /// Obtient ou définit les politiques qui s'appliquent à cet actif.
    /// </summary>
    public virtual ICollection<Politique> Politiques { get; set; } = new List<Politique>();
}
