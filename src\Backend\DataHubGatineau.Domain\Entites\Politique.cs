using System.ComponentModel.DataAnnotations;
using DataHubGatineau.Domain.Enums;

namespace DataHubGatineau.Domain.Entites;

/// <summary>
/// Représente une politique ou un standard dans le système de gouvernance des données.
/// </summary>
public class Politique : EntiteBase
{
    /// <summary>
    /// Obtient ou définit le code de référence unique de la politique.
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit le titre de la politique.
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Titre { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit la description courte de la politique.
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Obtient ou définit le contenu complet de la politique.
    /// </summary>
    [Required]
    public string Contenu { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit le statut de la politique.
    /// </summary>
    [Required]
    public StatutPolitique Statut { get; set; } = StatutPolitique.Brouillon;

    /// <summary>
    /// Obtient ou définit le niveau d'application de la politique.
    /// </summary>
    [Required]
    public NiveauApplicationPolitique NiveauApplication { get; set; } = NiveauApplicationPolitique.Organisation;

    /// <summary>
    /// Obtient ou définit la catégorie de la politique.
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Categorie { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit les mots-clés associés à la politique.
    /// </summary>
    [StringLength(500)]
    public string? MotsCles { get; set; }

    /// <summary>
    /// Obtient ou définit la version de la politique.
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// Obtient ou définit l'identifiant de la politique parente (pour les versions).
    /// </summary>
    public Guid? PolitiqueParenteId { get; set; }

    /// <summary>
    /// Obtient ou définit la politique parente.
    /// </summary>
    public virtual Politique? PolitiqueParente { get; set; }

    /// <summary>
    /// Obtient ou définit les versions enfants de cette politique.
    /// </summary>
    public virtual ICollection<Politique> VersionsEnfants { get; set; } = new List<Politique>();

    /// <summary>
    /// Obtient ou définit la date d'entrée en vigueur de la politique.
    /// </summary>
    public DateTime? DateEntreeVigueur { get; set; }

    /// <summary>
    /// Obtient ou définit la date d'expiration de la politique.
    /// </summary>
    public DateTime? DateExpiration { get; set; }

    /// <summary>
    /// Obtient ou définit la date de la prochaine révision.
    /// </summary>
    public DateTime? DateProchaineRevision { get; set; }

    /// <summary>
    /// Obtient ou définit le propriétaire de la politique.
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Proprietaire { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit l'approbateur de la politique.
    /// </summary>
    [StringLength(100)]
    public string? Approbateur { get; set; }

    /// <summary>
    /// Obtient ou définit la date d'approbation.
    /// </summary>
    public DateTime? DateApprobation { get; set; }

    /// <summary>
    /// Obtient ou définit si la politique est active.
    /// </summary>
    public bool EstActive { get; set; } = true;

    /// <summary>
    /// Obtient ou définit le type de politique.
    /// </summary>
    [Required]
    public TypePolitique Type { get; set; } = TypePolitique.Gouvernance;

    /// <summary>
    /// Obtient ou définit l'identifiant de l'entité d'application (pour les niveaux spécifiques).
    /// </summary>
    public int? EntiteApplicationId { get; set; }

    /// <summary>
    /// Obtient ou définit le nom de l'entité d'application.
    /// </summary>
    [StringLength(200)]
    public string? EntiteApplicationNom { get; set; }

    /// <summary>
    /// Obtient ou définit les références en format JSON.
    /// </summary>
    public string? ReferencesJson { get; set; }

    /// <summary>
    /// Obtient ou définit les pièces jointes en format JSON.
    /// </summary>
    public string? PiecesJointesJson { get; set; }

    /// <summary>
    /// Obtient ou définit l'historique des modifications en format JSON.
    /// </summary>
    public string? HistoriqueModificationsJson { get; set; }

    /// <summary>
    /// Obtient ou définit les actifs de données auxquels cette politique s'applique.
    /// </summary>
    public virtual ICollection<ActifDonnees> ActifsDonnees { get; set; } = new List<ActifDonnees>();

    /// <summary>
    /// Obtient ou définit les termes du glossaire associés à cette politique.
    /// </summary>
    public virtual ICollection<TermeGlossairePolitique> TermesGlossaire { get; set; } = new List<TermeGlossairePolitique>();
}

/// <summary>
/// Énumération des statuts possibles pour une politique.
/// </summary>
public enum StatutPolitique
{
    /// <summary>
    /// Politique en cours de rédaction.
    /// </summary>
    Brouillon,

    /// <summary>
    /// Politique en cours de révision.
    /// </summary>
    EnRevision,

    /// <summary>
    /// Politique en attente d'approbation.
    /// </summary>
    EnAttenteApprobation,

    /// <summary>
    /// Politique approuvée et active.
    /// </summary>
    Active,

    /// <summary>
    /// Politique suspendue temporairement.
    /// </summary>
    Suspendue,

    /// <summary>
    /// Politique archivée (remplacée par une nouvelle version).
    /// </summary>
    Archivee,

    /// <summary>
    /// Politique obsolète (plus applicable).
    /// </summary>
    Obsolete
}

/// <summary>
/// Énumération des types de politiques disponibles dans le système.
/// </summary>
public enum TypePolitique
{
    /// <summary>
    /// Politique de gouvernance des données.
    /// </summary>
    Gouvernance,

    /// <summary>
    /// Politique de qualité des données.
    /// </summary>
    Qualite,

    /// <summary>
    /// Politique de sécurité des données.
    /// </summary>
    Securite,

    /// <summary>
    /// Politique de confidentialité des données.
    /// </summary>
    Confidentialite,

    /// <summary>
    /// Politique de conservation des données.
    /// </summary>
    Conservation,

    /// <summary>
    /// Politique d'accès aux données.
    /// </summary>
    Acces,

    /// <summary>
    /// Politique de partage des données.
    /// </summary>
    Partage,

    /// <summary>
    /// Standard technique.
    /// </summary>
    StandardTechnique,

    /// <summary>
    /// Standard métier.
    /// </summary>
    StandardMetier,

    /// <summary>
    /// Autre type de politique.
    /// </summary>
    Autre
}

/// <summary>
/// Énumération des niveaux d'application possibles pour une politique.
/// </summary>
public enum NiveauApplicationPolitique
{
    /// <summary>
    /// Politique applicable à toute l'organisation.
    /// </summary>
    Organisation,

    /// <summary>
    /// Politique applicable à un département spécifique.
    /// </summary>
    Departement,

    /// <summary>
    /// Politique applicable à un projet spécifique.
    /// </summary>
    Projet,

    /// <summary>
    /// Politique applicable à un domaine de données spécifique.
    /// </summary>
    Domaine,

    /// <summary>
    /// Politique applicable à un actif de données spécifique.
    /// </summary>
    Actif,

    /// <summary>
    /// Politique applicable à un système spécifique.
    /// </summary>
    Systeme
}
