using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;

namespace DataHubGatineau.Domain.Interfaces;

/// <summary>
/// Options de chargement pour les actifs de données.
/// </summary>
[Flags]
public enum ActifDonneesInclude
{
    /// <summary>
    /// Aucune inclusion.
    /// </summary>
    None = 0,

    /// <summary>
    /// Inclure le type d'actif de données.
    /// </summary>
    Type = 1 << 0,

    /// <summary>
    /// Inclure le format d'actif de données.
    /// </summary>
    Format = 1 << 1,

    /// <summary>
    /// Inclure la source d'actif de données.
    /// </summary>
    Source = 1 << 2,

    /// <summary>
    /// Inclure la fréquence de mise à jour.
    /// </summary>
    FrequenceMiseAJour = 1 << 3,

    /// <summary>
    /// Inclure le statut d'actif de données.
    /// </summary>
    Statut = 1 << 4,

    /// <summary>
    /// Inclure le domaine de gouvernance.
    /// </summary>
    DomaineGouvernance = 1 << 5,

    /// <summary>
    /// Inclure la connexion à la source de données.
    /// </summary>
    ConnexionSourceDonnees = 1 << 6,

    /// <summary>
    /// Inclure les métadonnées.
    /// </summary>
    Metadonnees = 1 << 7,

    /// <summary>
    /// Inclure les règles de qualité.
    /// </summary>
    ReglesQualite = 1 << 8,

    /// <summary>
    /// Inclure les termes du glossaire.
    /// </summary>
    TermesGlossaire = 1 << 9,

    /// <summary>
    /// Inclure les produits de données.
    /// </summary>
    ProduitsDonnees = 1 << 10,

    /// <summary>
    /// Inclure les politiques.
    /// </summary>
    Politiques = 1 << 11,

    /// <summary>
    /// Inclure toutes les propriétés de navigation.
    /// </summary>
    All = Type | Format | Source | FrequenceMiseAJour | Statut | DomaineGouvernance |
          ConnexionSourceDonnees | Metadonnees | ReglesQualite | TermesGlossaire | ProduitsDonnees | Politiques,

    /// <summary>
    /// Inclure les propriétés de base (type, format, source, fréquence, statut).
    /// </summary>
    Basic = Type | Format | Source | FrequenceMiseAJour | Statut
}

/// <summary>
/// Interface pour le dépôt des actifs de données.
/// </summary>
public interface IDepotActifDonnees : IDepotBase<ActifDonnees>
{
    /// <summary>
    /// Obtient un actif de données par son identifiant avec les inclusions spécifiées.
    /// </summary>
    /// <param name="id">Identifiant de l'actif de données.</param>
    /// <param name="includes">Propriétés de navigation à inclure.</param>
    /// <returns>L'actif de données correspondant à l'identifiant spécifié.</returns>
    Task<ActifDonnees?> ObtenirParIdAsync(Guid id, ActifDonneesInclude includes);

    /// <summary>
    /// Obtient tous les actifs de données avec les inclusions spécifiées.
    /// </summary>
    /// <param name="includes">Propriétés de navigation à inclure.</param>
    /// <returns>Une collection de tous les actifs de données.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirTousAsync(ActifDonneesInclude includes);

    /// <summary>
    /// Obtient les actifs de données par type.
    /// </summary>
    /// <param name="type">Type d'actif de données.</param>
    /// <param name="includes">Propriétés de navigation à inclure.</param>
    /// <returns>Une collection d'actifs de données du type spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParTypeAsync(string type, ActifDonneesInclude includes = ActifDonneesInclude.Basic);

    /// <summary>
    /// Obtient les actifs de données par classification de sensibilité.
    /// </summary>
    /// <param name="classification">Classification de sensibilité.</param>
    /// <returns>Une collection d'actifs de données avec la classification spécifiée.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParClassificationAsync(ClassificationSensibilite classification);

    /// <summary>
    /// Obtient les actifs de données par propriétaire.
    /// </summary>
    /// <param name="proprietaire">Propriétaire des actifs de données.</param>
    /// <returns>Une collection d'actifs de données appartenant au propriétaire spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParProprietaireAsync(string proprietaire);

    /// <summary>
    /// Obtient les actifs de données par terme du glossaire.
    /// </summary>
    /// <param name="termeId">Identifiant du terme du glossaire.</param>
    /// <returns>Une collection d'actifs de données associés au terme spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParTermeGlossaireAsync(int termeId);

    /// <summary>
    /// Obtient les actifs de données par domaine de gouvernance.
    /// </summary>
    /// <param name="domaineGouvernanceId">Identifiant du domaine de gouvernance.</param>
    /// <returns>Une collection d'actifs de données associés au domaine de gouvernance spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParDomaineGouvernanceAsync(Guid domaineGouvernanceId);

    /// <summary>
    /// Obtient les actifs de données par connexion à la source de données.
    /// </summary>
    /// <param name="connexionSourceDonneesId">Identifiant de la connexion à la source de données.</param>
    /// <returns>Une collection d'actifs de données associés à la connexion spécifiée.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParConnexionSourceDonneesAsync(Guid connexionSourceDonneesId);

    /// <summary>
    /// Obtient les actifs de données par statut.
    /// </summary>
    /// <param name="statut">Statut des actifs de données.</param>
    /// <returns>Une collection d'actifs de données avec le statut spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParStatutAsync(string statut);

    /// <summary>
    /// Obtient le lignage des données pour un actif de données.
    /// </summary>
    /// <param name="actifId">Identifiant de l'actif de données.</param>
    /// <returns>Une collection d'actifs de données représentant le lignage.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirLignageAsync(Guid actifId);

    /// <summary>
    /// Recherche des actifs de données par terme de recherche.
    /// </summary>
    /// <param name="terme">Terme de recherche.</param>
    /// <param name="includes">Propriétés de navigation à inclure.</param>
    /// <returns>Une collection d'actifs de données correspondant au terme de recherche.</returns>
    Task<IEnumerable<ActifDonnees>> RechercherAsync(string terme, ActifDonneesInclude includes = ActifDonneesInclude.Basic);

    /// <summary>
    /// Obtient un actif de données par son nom.
    /// </summary>
    /// <param name="nom">Nom de l'actif de données.</param>
    /// <returns>L'actif de données correspondant au nom spécifié ou null si non trouvé.</returns>
    Task<ActifDonnees?> ObtenirParNomAsync(string nom);
}
