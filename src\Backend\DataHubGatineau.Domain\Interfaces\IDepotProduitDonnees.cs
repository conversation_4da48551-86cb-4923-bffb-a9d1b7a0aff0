using DataHubGatineau.Domain.Entites;

namespace DataHubGatineau.Domain.Interfaces;

/// <summary>
/// Interface pour le dépôt des produits de données.
/// </summary>
public interface IDepotProduitDonnees : IDepotBase<ProduitDonnees>
{
    /// <summary>
    /// Obtient les produits de données par domaine de gouvernance.
    /// </summary>
    /// <param name="domaineGouvernanceId">Identifiant du domaine de gouvernance.</param>
    /// <returns>Une collection de produits de données.</returns>
    Task<IEnumerable<ProduitDonnees>> ObtenirParDomaineGouvernanceAsync(Guid domaineGouvernanceId);

    /// <summary>
    /// Obtient les produits de données par propriétaire.
    /// </summary>
    /// <param name="proprietaire">Propriétaire des produits de données.</param>
    /// <returns>Une collection de produits de données.</returns>
    Task<IEnumerable<ProduitDonnees>> ObtenirParProprietaireAsync(string proprietaire);

    /// <summary>
    /// Obtient les produits de données contenant un actif de données spécifique.
    /// </summary>
    /// <param name="actifDonneesId">Identifiant de l'actif de données.</param>
    /// <returns>Une collection de produits de données.</returns>
    Task<IEnumerable<ProduitDonnees>> ObtenirParActifDonneesAsync(Guid actifDonneesId);

    /// <summary>
    /// Recherche des produits de données par mot-clé.
    /// </summary>
    /// <param name="motCle">Mot-clé à rechercher.</param>
    /// <returns>Une collection de produits de données.</returns>
    Task<IEnumerable<ProduitDonnees>> RechercherAsync(string motCle);

    /// <summary>
    /// Obtient un produit de données par son identifiant avec ses actifs de données associés.
    /// </summary>
    /// <param name="id">Identifiant du produit de données.</param>
    /// <returns>Le produit de données avec ses actifs associés si trouvé, sinon null.</returns>
    Task<ProduitDonnees?> ObtenirParIdAvecActifsAsync(Guid id);
}
