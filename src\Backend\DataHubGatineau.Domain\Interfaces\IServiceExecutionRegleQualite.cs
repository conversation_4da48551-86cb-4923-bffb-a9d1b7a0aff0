using DataHubGatineau.Domain.Entites;

namespace DataHubGatineau.Domain.Interfaces;

/// <summary>
/// Interface pour le service d'exécution des règles de qualité.
/// </summary>
public interface IServiceExecutionRegleQualite
{
    /// <summary>
    /// Exécute une règle de qualité spécifique.
    /// </summary>
    /// <param name="regleId">Identifiant de la règle à exécuter.</param>
    /// <returns>Le résultat de l'exécution de la règle.</returns>
    Task<ResultatRegleQualite> ExecuterRegleAsync(Guid regleId);

    /// <summary>
    /// Exécute toutes les règles de qualité pour un actif de données.
    /// </summary>
    /// <param name="actifDonneesId">Identifiant de l'actif de données.</param>
    /// <returns>Les résultats de l'exécution de toutes les règles.</returns>
    Task<IEnumerable<ResultatRegleQualite>> ExecuterToutesReglesAsync(Guid actifDonneesId);
}
