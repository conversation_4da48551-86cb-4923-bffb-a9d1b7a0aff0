using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;

namespace DataHubGatineau.Domain.Interfaces.Repositories;

/// <summary>
/// Interface du dépôt pour les politiques.
/// </summary>
public interface IDepotPolitique : IDepotBase<Politique>
{
    /// <summary>
    /// Obtient toutes les politiques avec les entités liées.
    /// </summary>
    /// <returns>Collection de politiques avec les entités liées.</returns>
    Task<IEnumerable<Politique>> ObtenirTousAvecEntitesLieesAsync();

    /// <summary>
    /// Obtient une politique par identifiant avec les entités liées.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <returns>Politique avec les entités liées si trouvée.</returns>
    Task<Politique?> ObtenirParIdAvecEntitesLieesAsync(Guid id);

    /// <summary>
    /// Obtient une politique par son code.
    /// </summary>
    /// <param name="code">Code de la politique.</param>
    /// <returns>Politique si trouvée.</returns>
    Task<Politique?> ObtenirParCodeAsync(string code);

    /// <summary>
    /// Obtient les politiques par catégorie.
    /// </summary>
    /// <param name="categorie">Catégorie des politiques.</param>
    /// <returns>Collection de politiques de la catégorie spécifiée.</returns>
    Task<IEnumerable<Politique>> ObtenirParCategorieAsync(string categorie);

    /// <summary>
    /// Obtient les politiques par statut.
    /// </summary>
    /// <param name="statut">Statut des politiques.</param>
    /// <returns>Collection de politiques du statut spécifié.</returns>
    Task<IEnumerable<Politique>> ObtenirParStatutAsync(StatutPolitique statut);

    /// <summary>
    /// Obtient les politiques par niveau d'application.
    /// </summary>
    /// <param name="niveauApplication">Niveau d'application.</param>
    /// <returns>Collection de politiques du niveau spécifié.</returns>
    Task<IEnumerable<Politique>> ObtenirParNiveauApplicationAsync(NiveauApplicationPolitique niveauApplication);

    /// <summary>
    /// Obtient les politiques actives.
    /// </summary>
    /// <returns>Collection de politiques actives.</returns>
    Task<IEnumerable<Politique>> ObtenirPolitiquesActivesAsync();

    /// <summary>
    /// Obtient les politiques en vigueur à une date donnée.
    /// </summary>
    /// <param name="date">Date de référence.</param>
    /// <returns>Collection de politiques en vigueur.</returns>
    Task<IEnumerable<Politique>> ObtenirEnVigueurAsync(DateTime? date = null);

    /// <summary>
    /// Obtient les politiques qui expirent dans une période donnée.
    /// </summary>
    /// <param name="joursAvantExpiration">Nombre de jours avant expiration.</param>
    /// <returns>Collection de politiques qui expirent bientôt.</returns>
    Task<IEnumerable<Politique>> ObtenirExpirantBientotAsync(int joursAvantExpiration = 30);

    /// <summary>
    /// Obtient les politiques nécessitant une révision.
    /// </summary>
    /// <returns>Collection de politiques à réviser.</returns>
    Task<IEnumerable<Politique>> ObtenirAReviserAsync();

    /// <summary>
    /// Obtient les politiques parentes (sans parent).
    /// </summary>
    /// <returns>Collection de politiques parentes.</returns>
    Task<IEnumerable<Politique>> ObtenirPolitiquesParentesAsync();

    /// <summary>
    /// Obtient les politiques enfants d'une politique parente.
    /// </summary>
    /// <param name="politiqueParenteId">Identifiant de la politique parente.</param>
    /// <returns>Collection de politiques enfants.</returns>
    Task<IEnumerable<Politique>> ObtenirPolitiquesEnfantsAsync(Guid politiqueParenteId);

    /// <summary>
    /// Recherche des politiques par mots-clés.
    /// </summary>
    /// <param name="motsCles">Mots-clés de recherche.</param>
    /// <returns>Collection de politiques correspondantes.</returns>
    Task<IEnumerable<Politique>> RechercherParMotsClesAsync(string motsCles);

    /// <summary>
    /// Vérifie si un code de politique existe déjà.
    /// </summary>
    /// <param name="code">Code à vérifier.</param>
    /// <param name="excludeId">Identifiant à exclure de la vérification (pour les mises à jour).</param>
    /// <returns>True si le code existe, false sinon.</returns>
    Task<bool> CodeExisteAsync(string code, Guid? excludeId = null);

    /// <summary>
    /// Obtient les statistiques des politiques.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    Task<Dictionary<string, int>> ObtenirStatistiquesAsync();

    /// <summary>
    /// Active ou désactive une politique.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <param name="estActive">État d'activation.</param>
    /// <returns>True si la mise à jour a réussi.</returns>
    Task<bool> ChangerEtatActivationAsync(Guid id, bool estActive);

    /// <summary>
    /// Met à jour le statut d'une politique.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <param name="nouveauStatut">Nouveau statut.</param>
    /// <returns>True si la mise à jour a réussi.</returns>
    Task<bool> MettreAJourStatutAsync(Guid id, StatutPolitique nouveauStatut);

    /// <summary>
    /// Associe des actifs de données à une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <param name="actifsIds">Liste des identifiants des actifs à associer.</param>
    /// <returns>True si l'association a réussi.</returns>
    Task<bool> AssocierActifsAsync(Guid politiqueId, IEnumerable<Guid> actifsIds);

    /// <summary>
    /// Dissocie un actif de données d'une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <param name="actifId">Identifiant de l'actif à dissocier.</param>
    /// <returns>True si la dissociation a réussi.</returns>
    Task<bool> DissocierActifAsync(Guid politiqueId, Guid actifId);

    /// <summary>
    /// Obtient les actifs de données associés à une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>Collection des actifs associés ou null si la politique n'existe pas.</returns>
    Task<IEnumerable<ActifDonnees>?> ObtenirActifsAssociesAsync(Guid politiqueId);
}
