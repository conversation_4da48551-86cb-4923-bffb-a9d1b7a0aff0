﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataHubGatineau.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AjouterRelationsPolitiquesActifs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "PolitiquesActifs",
                schema: "Gouvernance",
                columns: table => new
                {
                    ActifsDonneesId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PolitiquesId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PolitiquesActifs", x => new { x.ActifsDonneesId, x.PolitiquesId });
                    table.ForeignKey(
                        name: "FK_PolitiquesActifs_ActifsDonnees_ActifsDonneesId",
                        column: x => x.ActifsDonneesId,
                        principalSchema: "Metadonnees",
                        principalTable: "ActifsDonnees",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PolitiquesActifs_Politiques_PolitiquesId",
                        column: x => x.PolitiquesId,
                        principalSchema: "Gouvernance",
                        principalTable: "Politiques",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PolitiquesActifs_PolitiquesId",
                schema: "Gouvernance",
                table: "PolitiquesActifs",
                column: "PolitiquesId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PolitiquesActifs",
                schema: "Gouvernance");
        }
    }
}
