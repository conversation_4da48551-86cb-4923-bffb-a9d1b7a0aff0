﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataHubGatineau.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AgregarCamposPolitique : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "EntiteApplicationId",
                schema: "Gouvernance",
                table: "Politiques",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EntiteApplicationNom",
                schema: "Gouvernance",
                table: "Politiques",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "HistoriqueModificationsJson",
                schema: "Gouvernance",
                table: "Politiques",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PiecesJointes<PERSON>son",
                schema: "Gouvernance",
                table: "Politiques",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "References<PERSON>son",
                schema: "Gouvernance",
                table: "Politiques",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Type",
                schema: "Gouvernance",
                table: "Politiques",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EntiteApplicationId",
                schema: "Gouvernance",
                table: "Politiques");

            migrationBuilder.DropColumn(
                name: "EntiteApplicationNom",
                schema: "Gouvernance",
                table: "Politiques");

            migrationBuilder.DropColumn(
                name: "HistoriqueModificationsJson",
                schema: "Gouvernance",
                table: "Politiques");

            migrationBuilder.DropColumn(
                name: "PiecesJointesJson",
                schema: "Gouvernance",
                table: "Politiques");

            migrationBuilder.DropColumn(
                name: "ReferencesJson",
                schema: "Gouvernance",
                table: "Politiques");

            migrationBuilder.DropColumn(
                name: "Type",
                schema: "Gouvernance",
                table: "Politiques");
        }
    }
}
