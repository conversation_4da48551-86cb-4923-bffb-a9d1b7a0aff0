using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Entites.Identity;
using DataHubGatineau.Domain.Entities.Identity;
using DataHubGatineau.Domain.Entities.Metadata;
using DataHubGatineau.Infrastructure.Persistence.Configurations;
using Microsoft.EntityFrameworkCore;

namespace DataHubGatineau.Infrastructure.Persistence;

/// <summary>
/// Contexte de base de données pour l'application Centre de Données Gatineau.
/// </summary>
public class CentreDonneesDbContext : DbContext
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="CentreDonneesDbContext"/>.
    /// </summary>
    /// <param name="options">Options de configuration du contexte.</param>
    public CentreDonneesDbContext(DbContextOptions<CentreDonneesDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Obtient ou définit les actifs de données.
    /// </summary>
    public DbSet<ActifDonnees> ActifsDonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les métadonnées.
    /// </summary>
    public DbSet<Metadonnee> Metadonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les règles de qualité.
    /// </summary>
    public DbSet<RegleQualite> ReglesQualite { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les résultats de règles de qualité.
    /// </summary>
    public DbSet<ResultatRegleQualite> ResultatsRegleQualite { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les termes du glossaire.
    /// </summary>
    public DbSet<TermeGlossaire> TermesGlossaire { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les relations de lignage entre actifs de données.
    /// </summary>
    public DbSet<LignageDonnees> LignageDonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les domaines de gouvernance.
    /// </summary>
    public DbSet<DomaineGouvernance> DomainesGouvernance { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les domaines d'affaires.
    /// </summary>
    public DbSet<DomaineAffaires> DomainesAffaires { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les politiques d'accès.
    /// </summary>
    public DbSet<PolitiqueAcces> PolitiquesAcces { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les types d'actifs de données.
    /// </summary>
    public DbSet<TypeActifDonnees> TypesActifDonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les catégories de types d'actifs de données.
    /// </summary>
    public DbSet<CategorieTypeActifDonnees> CategoriesTypesActifDonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les formats d'actifs de données.
    /// </summary>
    public DbSet<FormatActifDonnees> FormatsActifDonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les sources d'actifs de données.
    /// </summary>
    public DbSet<SourceActifDonnees> SourcesActifDonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les fréquences de mise à jour.
    /// </summary>
    public DbSet<FrequenceMiseAJour> FrequencesMiseAJour { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les statuts d'actifs de données.
    /// </summary>
    public DbSet<StatutActifDonnees> StatutsActifDonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les types de métadonnées.
    /// </summary>
    public DbSet<TypeMetadonnee> TypesMetadonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les catégories de métadonnées.
    /// </summary>
    public DbSet<CategorieMetadonnee> CategoriesMetadonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les schémas de métadonnées.
    /// </summary>
    public DbSet<SchemaMetadonnees> SchemasMetadonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les définitions de métadonnées.
    /// </summary>
    public DbSet<DefinitionMetadonnee> DefinitionsMetadonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les produits de données.
    /// </summary>
    public DbSet<ProduitDonnees> ProduitsDonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les connexions aux sources de données.
    /// </summary>
    public DbSet<ConnexionSourceDonnees> ConnexionsSourceDonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les configurations de scan.
    /// </summary>
    public DbSet<ConfigurationScan> ConfigurationsScans { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les historiques de scan.
    /// </summary>
    public DbSet<HistoriqueScan> HistoriquesScans { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les workflows d'approbation.
    /// </summary>
    public DbSet<WorkflowApprobation> WorkflowsApprobation { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les étapes de workflow d'approbation.
    /// </summary>
    public DbSet<EtapeWorkflowApprobation> EtapesWorkflowApprobation { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les relations de lineage intelligent.
    /// </summary>
    public DbSet<RelationLineage> RelationsLineage { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les graphes de lineage.
    /// </summary>
    public DbSet<GrapheLineage> GraphesLineage { get; set; } = null!;

    // Entités d'identité et de gestion des accès

    /// <summary>
    /// Obtient ou définit les utilisateurs.
    /// </summary>
    public DbSet<Utilisateur> Utilisateurs { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les groupes.
    /// </summary>
    public DbSet<Groupe> Groupes { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les rôles.
    /// </summary>
    public DbSet<Role> Roles { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les permissions.
    /// </summary>
    public DbSet<Permission> Permissions { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les relations utilisateur-rôle.
    /// </summary>
    public DbSet<UtilisateurRole> UtilisateursRoles { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les relations utilisateur-groupe.
    /// </summary>
    public DbSet<UtilisateurGroupe> UtilisateursGroupes { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les relations groupe-rôle.
    /// </summary>
    public DbSet<GroupeRole> GroupesRoles { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les relations rôle-permission.
    /// </summary>
    public DbSet<RolePermission> RolesPermissions { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les demandes d'accès.
    /// </summary>
    public DbSet<DemandeAcces> DemandesAcces { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit le journal d'accès.
    /// </summary>
    public DbSet<JournalAcces> JournalAcces { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les jetons de rafraîchissement JWT.
    /// </summary>
    public DbSet<JetonRafraichissementJWT> JetonsRafraichissementJWT { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les utilisateurs JWT.
    /// </summary>
    public DbSet<UtilisateurJWT> UtilisateursJWT { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les rôles JWT.
    /// </summary>
    public DbSet<RoleJWT> RolesJWT { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les relations utilisateur-rôle JWT.
    /// </summary>
    public DbSet<UtilisateurRoleJWT> UtilisateursRolesJWT { get; set; } = null!;

    // Nouvelles entités pour les intégrations du glossaire

    /// <summary>
    /// Obtient ou définit les politiques générales.
    /// </summary>
    public DbSet<Politique> Politiques { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les associations entre termes du glossaire et métadonnées.
    /// </summary>
    public DbSet<TermeGlossaireMetadonnee> TermesGlossaireMetadonnees { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les associations entre termes du glossaire et règles de qualité.
    /// </summary>
    public DbSet<TermeGlossaireRegleQualite> TermesGlossaireReglesQualite { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit les associations entre termes du glossaire et politiques.
    /// </summary>
    public DbSet<TermeGlossairePolitique> TermesGlossairePolitiques { get; set; } = null!;

    /// <summary>
    /// Configure le modèle de données.
    /// </summary>
    /// <param name="modelBuilder">Constructeur de modèle.</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configuration des entités
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(CentreDonneesDbContext).Assembly);

        // Configuration explicite pour les nouvelles entités (selon Context7)
        modelBuilder.ApplyConfiguration(new SchemaMetadonneesConfiguration());
        modelBuilder.ApplyConfiguration(new DefinitionMetadonneeConfiguration());

        // Configuration des nouvelles entités d'intégration du glossaire
        modelBuilder.ApplyConfiguration(new PolitiqueConfiguration());
        modelBuilder.ApplyConfiguration(new TermeGlossaireMetadonneeConfiguration());
        modelBuilder.ApplyConfiguration(new TermeGlossaireRegleQualiteConfiguration());
        modelBuilder.ApplyConfiguration(new TermeGlossairePolitiqueConfiguration());

        // Configuration des schémas
        modelBuilder.Entity<ActifDonnees>().ToTable("ActifsDonnees", "Metadonnees");
        modelBuilder.Entity<Metadonnee>().ToTable("Metadonnees", "Metadonnees");
        modelBuilder.Entity<RegleQualite>().ToTable("ReglesQualite", "QualiteDonnees");
        modelBuilder.Entity<ResultatRegleQualite>().ToTable("ResultatsRegleQualite", "QualiteDonnees");
        modelBuilder.Entity<TermeGlossaire>().ToTable("TermesGlossaire", "Gouvernance");
        modelBuilder.Entity<DomaineGouvernance>().ToTable("DomainesGouvernance", "Gouvernance");
        modelBuilder.Entity<DomaineAffaires>().ToTable("DomainesAffaires", "Gouvernance");

        // Configuration du mapping pour DomaineAffaires
        modelBuilder.Entity<DomaineAffaires>(entity =>
        {
            entity.Property(e => e.CreePar)
                .HasColumnName("UtilisateurCreation")
                .HasMaxLength(100)
                .IsRequired(false)
                .HasDefaultValue("System");

            entity.Property(e => e.ModifiePar)
                .HasColumnName("UtilisateurModification")
                .HasMaxLength(100)
                .IsRequired(false)
                .HasDefaultValue("System");

            entity.Property(e => e.DateModification)
                .HasDefaultValueSql("GETDATE()");
        });
        modelBuilder.Entity<PolitiqueAcces>().ToTable("PolitiquesAcces", "Gouvernance");
        modelBuilder.Entity<TypeActifDonnees>().ToTable("TypesActifDonnees", "Metadonnees");
        modelBuilder.Entity<CategorieTypeActifDonnees>().ToTable("CategoriesTypesActifDonnees", "Metadonnees");
        modelBuilder.Entity<FormatActifDonnees>().ToTable("FormatsActifDonnees", "Metadonnees");
        modelBuilder.Entity<SourceActifDonnees>().ToTable("SourcesActifDonnees", "Metadonnees");
        modelBuilder.Entity<FrequenceMiseAJour>().ToTable("FrequencesMiseAJour", "Metadonnees");
        modelBuilder.Entity<StatutActifDonnees>().ToTable("StatutsActifDonnees", "Metadonnees");
        modelBuilder.Entity<TypeMetadonnee>().ToTable("TypesMetadonnees", "Metadonnees");
        modelBuilder.Entity<CategorieMetadonnee>().ToTable("CategoriesMetadonnees", "Metadonnees");
        modelBuilder.Entity<ProduitDonnees>().ToTable("ProduitsDonnees", "Gouvernance");
        modelBuilder.Entity<ConnexionSourceDonnees>().ToTable("ConnexionsSourceDonnees", "Integration");
        modelBuilder.Entity<ConfigurationScan>().ToTable("ConfigurationsScans", "Integration");
        modelBuilder.Entity<HistoriqueScan>().ToTable("HistoriquesScans", "Integration");
        modelBuilder.Entity<WorkflowApprobation>().ToTable("WorkflowsApprobation", "Workflow");
        modelBuilder.Entity<EtapeWorkflowApprobation>().ToTable("EtapesWorkflowApprobation", "Workflow");

        // Configuration des tables de lineage intelligent
        modelBuilder.Entity<RelationLineage>().ToTable("RelationsLineage", "Lineage");
        modelBuilder.Entity<GrapheLineage>().ToTable("GraphesLineage", "Lineage");

        // Configuration des schémas pour les entités d'identité et de gestion des accès
        modelBuilder.Entity<Utilisateur>().ToTable("Utilisateurs", "Identite");
        modelBuilder.Entity<Groupe>().ToTable("Groupes", "Identite");
        modelBuilder.Entity<Role>().ToTable("Roles", "Identite");
        modelBuilder.Entity<Permission>().ToTable("Permissions", "Identite");
        modelBuilder.Entity<UtilisateurRole>().ToTable("UtilisateursRoles", "Identite");
        modelBuilder.Entity<UtilisateurGroupe>().ToTable("UtilisateursGroupes", "Identite");
        modelBuilder.Entity<GroupeRole>().ToTable("GroupesRoles", "Identite");
        modelBuilder.Entity<RolePermission>().ToTable("RolesPermissions", "Identite");
        modelBuilder.Entity<DemandeAcces>().ToTable("DemandesAcces", "Identite");
        modelBuilder.Entity<JournalAcces>().ToTable("JournalAcces", "Identite");
        // Configuration des tables JWT
        modelBuilder.Entity<UtilisateurJWT>().ToTable("UtilisateursJWT", "Identite");
        modelBuilder.Entity<RoleJWT>().ToTable("RolesJWT", "Identite");
        modelBuilder.Entity<UtilisateurRoleJWT>().ToTable("UtilisateursRolesJWT", "Identite");
        modelBuilder.Entity<JetonRafraichissementJWT>().ToTable("JetonsRafraichissementJWT", "Identite");

        // Configuration des clés composites
        modelBuilder.Entity<UtilisateurRole>()
            .HasKey(ur => new { ur.UtilisateurId, ur.RoleId });

        modelBuilder.Entity<UtilisateurRoleJWT>()
            .HasKey(ur => new { ur.UtilisateurId, ur.RoleId });

        // Configuration des relations
        modelBuilder.Entity<UtilisateurRole>()
            .HasOne(ur => ur.Utilisateur)
            .WithMany(u => u.UtilisateurRoles)
            .HasForeignKey(ur => ur.UtilisateurId);

        modelBuilder.Entity<UtilisateurRoleJWT>()
            .HasOne(ur => ur.Utilisateur)
            .WithMany(u => u.Roles)
            .HasForeignKey(ur => ur.UtilisateurId);

        modelBuilder.Entity<UtilisateurRoleJWT>()
            .HasOne(ur => ur.Role)
            .WithMany(r => r.Utilisateurs)
            .HasForeignKey(ur => ur.RoleId);

        modelBuilder.Entity<JetonRafraichissementJWT>()
            .HasOne(jr => jr.Utilisateur)
            .WithMany(u => u.JetonsRafraichissement)
            .HasForeignKey(jr => jr.UtilisateurId);

        // Configuration des relations pour les nouvelles entités
        modelBuilder.Entity<ActifDonnees>()
            .HasOne(a => a.DomaineGouvernance)
            .WithMany(d => d.ActifsDonnees)
            .HasForeignKey(a => a.DomaineGouvernanceId)
            .IsRequired(false);

        modelBuilder.Entity<TermeGlossaire>()
            .HasOne(t => t.DomaineAffaires)
            .WithMany(d => d.TermesGlossaire)
            .HasForeignKey(t => t.DomaineAffairesId)
            .IsRequired(false);

        modelBuilder.Entity<ActifDonnees>()
            .HasOne(a => a.TypeActifDonnees)
            .WithMany(t => t.ActifsDonnees)
            .HasForeignKey(a => a.TypeActifDonneesId)
            .IsRequired(false);

        modelBuilder.Entity<ActifDonnees>()
            .HasOne(a => a.FormatActifDonnees)
            .WithMany(f => f.ActifsDonnees)
            .HasForeignKey(a => a.FormatActifDonneesId)
            .IsRequired(false);

        modelBuilder.Entity<ActifDonnees>()
            .HasOne(a => a.SourceActifDonnees)
            .WithMany(s => s.ActifsDonnees)
            .HasForeignKey(a => a.SourceActifDonneesId)
            .IsRequired(false);

        modelBuilder.Entity<ActifDonnees>()
            .HasOne(a => a.FrequenceMiseAJour)
            .WithMany(f => f.ActifsDonnees)
            .HasForeignKey(a => a.FrequenceMiseAJourId)
            .IsRequired(false);

        modelBuilder.Entity<ActifDonnees>()
            .HasOne(a => a.StatutActifDonnees)
            .WithMany(s => s.ActifsDonnees)
            .HasForeignKey(a => a.StatutActifDonneesId)
            .IsRequired(false);

        modelBuilder.Entity<ActifDonnees>()
            .HasOne(a => a.ConnexionSourceDonnees)
            .WithMany(c => c.ActifsDonnees)
            .HasForeignKey(a => a.ConnexionSourceDonneesId)
            .IsRequired(false);

        // Configuration des relations pour les métadonnées
        modelBuilder.Entity<Metadonnee>()
            .HasOne(m => m.TypeMetadonnee)
            .WithMany(t => t.Metadonnees)
            .HasForeignKey(m => m.TypeId)
            .IsRequired(false);

        modelBuilder.Entity<Metadonnee>()
            .HasOne(m => m.CategorieMetadonnee)
            .WithMany(c => c.Metadonnees)
            .HasForeignKey(m => m.CategorieId)
            .IsRequired(false);

        modelBuilder.Entity<CategorieMetadonnee>()
            .HasOne(c => c.TypeMetadonnee)
            .WithMany(t => t.Categories)
            .HasForeignKey(c => c.TypeMetadonneeId);

        modelBuilder.Entity<ConfigurationScan>()
            .HasOne(c => c.ConnexionSourceDonnees)
            .WithMany()
            .HasForeignKey(c => c.ConnexionSourceDonneesId);

        modelBuilder.Entity<HistoriqueScan>()
            .HasOne(h => h.ConfigurationScan)
            .WithMany(c => c.HistoriqueScans)
            .HasForeignKey(h => h.ConfigurationScanId);

        modelBuilder.Entity<WorkflowApprobation>()
            .HasOne(w => w.ActifDonnees)
            .WithMany(a => a.WorkflowsApprobation)
            .HasForeignKey(w => w.ActifDonneesId);

        modelBuilder.Entity<WorkflowApprobation>()
            .HasOne(w => w.Initiateur)
            .WithMany()
            .HasForeignKey(w => w.InitiateurId);

        modelBuilder.Entity<EtapeWorkflowApprobation>()
            .HasOne(e => e.WorkflowApprobation)
            .WithMany(w => w.Etapes)
            .HasForeignKey(e => e.WorkflowApprobationId)
            .OnDelete(DeleteBehavior.NoAction);

        modelBuilder.Entity<EtapeWorkflowApprobation>()
            .HasOne(e => e.Approbateur)
            .WithMany()
            .HasForeignKey(e => e.ApprobateurId)
            .OnDelete(DeleteBehavior.NoAction);

        modelBuilder.Entity<ProduitDonnees>()
            .HasOne(p => p.DomaineGouvernance)
            .WithMany()
            .HasForeignKey(p => p.DomaineGouvernanceId);

        modelBuilder.Entity<TermeGlossaire>()
            .HasOne(t => t.DomaineGouvernance)
            .WithMany(d => d.TermesGlossaire)
            .HasForeignKey(t => t.DomaineGouvernanceId)
            .IsRequired(false);

        modelBuilder.Entity<TermeGlossaire>()
            .HasOne(t => t.PolitiqueAcces)
            .WithMany(p => p.TermesGlossaire)
            .HasForeignKey(t => t.PolitiqueAccesId)
            .IsRequired(false);

        // Configuración de la relación many-to-many entre ActifDonnees y TermeGlossaire
        modelBuilder.Entity<ActifDonnees>()
            .HasMany(a => a.TermesGlossaire)
            .WithMany(t => t.ActifsDonnees)
            .UsingEntity<Dictionary<string, object>>(
                "ActifsTermes",
                j => j.HasOne<TermeGlossaire>()
                    .WithMany()
                    .HasForeignKey("TermesGlossaireId")
                    .OnDelete(DeleteBehavior.Cascade),
                j => j.HasOne<ActifDonnees>()
                    .WithMany()
                    .HasForeignKey("ActifsDonneesId")
                    .OnDelete(DeleteBehavior.Cascade),
                j =>
                {
                    j.HasKey("ActifsDonneesId", "TermesGlossaireId");
                    j.ToTable("ActifsTermes", "Gouvernance");
                });

        // Configuración de la relación many-to-many entre ActifDonnees y ProduitDonnees
        modelBuilder.Entity<ActifDonnees>()
            .HasMany(a => a.ProduitsDonnees)
            .WithMany(p => p.ActifsDonnees)
            .UsingEntity<Dictionary<string, object>>(
                "ActifDonneesProduitDonnees",
                j => j.HasOne<ProduitDonnees>()
                    .WithMany()
                    .HasForeignKey("ProduitsDonneesId")
                    .OnDelete(DeleteBehavior.Cascade),
                j => j.HasOne<ActifDonnees>()
                    .WithMany()
                    .HasForeignKey("ActifsDonneesId")
                    .OnDelete(DeleteBehavior.Cascade),
                j =>
                {
                    j.HasKey("ActifsDonneesId", "ProduitsDonneesId");
                    j.ToTable("ActifDonneesProduitDonnees");
                });

        // Configuración de la relación many-to-many entre ActifDonnees y Politique
        modelBuilder.Entity<ActifDonnees>()
            .HasMany(a => a.Politiques)
            .WithMany(p => p.ActifsDonnees)
            .UsingEntity<Dictionary<string, object>>(
                "PolitiquesActifs",
                j => j.HasOne<Politique>()
                    .WithMany()
                    .HasForeignKey("PolitiquesId")
                    .OnDelete(DeleteBehavior.Cascade),
                j => j.HasOne<ActifDonnees>()
                    .WithMany()
                    .HasForeignKey("ActifsDonneesId")
                    .OnDelete(DeleteBehavior.Cascade),
                j =>
                {
                    j.HasKey("ActifsDonneesId", "PolitiquesId");
                    j.ToTable("PolitiquesActifs", "Gouvernance");
                });

        // Configuración de la relación many-to-many entre ActifDonnees y sí mismo para el lignage
        modelBuilder.Entity<ActifDonnees>()
            .HasMany(a => a.ElementsSources)
            .WithMany(a => a.ElementsCibles)
            .UsingEntity<LignageDonnees>(
                l => l.HasOne(ld => ld.ElementsCible)
                    .WithMany()
                    .HasForeignKey(ld => ld.ElementsCiblesId)
                    .OnDelete(DeleteBehavior.NoAction),
                r => r.HasOne(ld => ld.ElementsSource)
                    .WithMany()
                    .HasForeignKey(ld => ld.ElementsSourcesId)
                    .OnDelete(DeleteBehavior.NoAction),
                j =>
                {
                    j.HasKey(ld => ld.Id);
                    j.ToTable("LignageDonnees", "Metadonnees");
                    j.Property(ld => ld.TypeRelation).HasMaxLength(50).IsRequired();
                });

        // Configuration des relations pour le lineage intelligent
        modelBuilder.Entity<RelationLineage>()
            .HasOne(r => r.ActifSource)
            .WithMany()
            .HasForeignKey(r => r.ActifSourceId)
            .OnDelete(DeleteBehavior.NoAction);

        modelBuilder.Entity<RelationLineage>()
            .HasOne(r => r.ActifCible)
            .WithMany()
            .HasForeignKey(r => r.ActifCibleId)
            .OnDelete(DeleteBehavior.NoAction);

        modelBuilder.Entity<GrapheLineage>()
            .HasOne(g => g.ActifRacine)
            .WithMany()
            .HasForeignKey(g => g.ActifRacineId)
            .OnDelete(DeleteBehavior.NoAction);

        // Configuration des propriétés pour RelationLineage
        modelBuilder.Entity<RelationLineage>()
            .Property(r => r.TypeRelation)
            .HasMaxLength(50)
            .IsRequired();

        modelBuilder.Entity<RelationLineage>()
            .Property(r => r.Description)
            .HasMaxLength(500);

        modelBuilder.Entity<RelationLineage>()
            .Property(r => r.MethodeDetection)
            .HasMaxLength(100);

        modelBuilder.Entity<RelationLineage>()
            .Property(r => r.ValidePar)
            .HasMaxLength(100);

        // Configuration des propriétés pour GrapheLineage
        modelBuilder.Entity<GrapheLineage>()
            .Property(g => g.Nom)
            .HasMaxLength(200)
            .IsRequired();

        modelBuilder.Entity<GrapheLineage>()
            .Property(g => g.Description)
            .HasMaxLength(500);

        modelBuilder.Entity<GrapheLineage>()
            .Property(g => g.TypeGraphe)
            .HasMaxLength(20)
            .IsRequired();

        modelBuilder.Entity<GrapheLineage>()
            .Property(g => g.DonneesGraphe)
            .IsRequired();
    }
}
