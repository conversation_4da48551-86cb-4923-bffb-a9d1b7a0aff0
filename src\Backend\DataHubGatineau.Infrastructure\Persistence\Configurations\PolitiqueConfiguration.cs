using DataHubGatineau.Domain.Entites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataHubGatineau.Infrastructure.Persistence.Configurations;

/// <summary>
/// Configuration de l'entité Politique pour Entity Framework Core.
/// </summary>
public class PolitiqueConfiguration : IEntityTypeConfiguration<Politique>
{
    /// <summary>
    /// Configure l'entité Politique.
    /// </summary>
    /// <param name="builder">Constructeur de type d'entité.</param>
    public void Configure(EntityTypeBuilder<Politique> builder)
    {
        // Configuration de la table
        builder.ToTable("Politiques", "Gouvernance");

        // Configuration de la clé primaire
        builder.HasKey(p => p.Id);

        // Configuration des propriétés
        builder.Property(p => p.Code)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(p => p.Titre)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(p => p.Description)
            .HasMaxLength(500);

        builder.Property(p => p.Contenu)
            .IsRequired();

        builder.Property(p => p.Statut)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(p => p.NiveauApplication)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(p => p.Categorie)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(p => p.MotsCles)
            .HasMaxLength(500);

        builder.Property(p => p.Version)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue("1.0");

        builder.Property(p => p.Proprietaire)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(p => p.Approbateur)
            .HasMaxLength(100);

        builder.Property(p => p.EstActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Configuration des relations
        builder.HasOne(p => p.PolitiqueParente)
            .WithMany(p => p.VersionsEnfants)
            .HasForeignKey(p => p.PolitiqueParenteId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        // Configuration des index
        builder.HasIndex(p => p.Code)
            .IsUnique()
            .HasDatabaseName("UX_Politiques_Code");

        builder.HasIndex(p => p.Statut)
            .HasDatabaseName("IX_Politiques_Statut");

        builder.HasIndex(p => p.NiveauApplication)
            .HasDatabaseName("IX_Politiques_NiveauApplication");

        builder.HasIndex(p => p.Categorie)
            .HasDatabaseName("IX_Politiques_Categorie");

        builder.HasIndex(p => p.EstActive)
            .HasDatabaseName("IX_Politiques_EstActive");

        builder.HasIndex(p => p.DateEntreeVigueur)
            .HasDatabaseName("IX_Politiques_DateEntreeVigueur");

        // Configuration de la relation many-to-many avec ActifDonnees
        builder.HasMany(p => p.ActifsDonnees)
            .WithMany(a => a.Politiques)
            .UsingEntity(j => j.ToTable("PolitiquesActifs", "Gouvernance"));

        builder.HasIndex(p => p.DateExpiration)
            .HasDatabaseName("IX_Politiques_DateExpiration");

        builder.HasIndex(p => p.PolitiqueParenteId)
            .HasDatabaseName("IX_Politiques_PolitiqueParenteId");
    }
}
