using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;
using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using TypeActifDonneesEntity = DataHubGatineau.Domain.Entites.TypeActifDonnees;

namespace DataHubGatineau.Infrastructure.Repositories;

/// <summary>
/// Implémentation du dépôt pour les actifs de données.
/// </summary>
public class DepotActifDonnees : DepotBase<ActifDonnees>, IDepotActifDonnees
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DepotActifDonnees"/>.
    /// </summary>
    /// <param name="context">Contexte de base de données.</param>
    public DepotActifDonnees(CentreDonneesDbContext context) : base(context)
    {
    }

    /// <inheritdoc/>
    public override async Task<ActifDonnees?> ObtenirParIdAsync(Guid id)
    {
        return await ObtenirParIdAsync(id, ActifDonneesInclude.Basic | ActifDonneesInclude.Type | ActifDonneesInclude.Format | ActifDonneesInclude.Source | ActifDonneesInclude.DomaineGouvernance | ActifDonneesInclude.ConnexionSourceDonnees);
    }

    /// <inheritdoc/>
    public async Task<ActifDonnees?> ObtenirParIdAsync(Guid id, ActifDonneesInclude includes)
    {
        Console.WriteLine($"ObtenirParIdAsync avec includes: {includes}");

        var query = _dbSet.AsQueryable();

        // Appliquer les inclusions en fonction des options
        query = AppliquerInclusions(query, includes);

        var result = await query.FirstOrDefaultAsync(a => a.Id == id);

        // Debug: Vérifier si les propriétés de navigation sont chargées
        if (result != null)
        {
            Console.WriteLine($"Actif chargé: {result.Nom}, Type: {result.TypeActifDonnees?.Nom ?? "NULL"}, Format: {result.FormatActifDonnees?.Nom ?? "NULL"}, Source: {result.SourceActifDonnees?.Nom ?? "NULL"}");
            Console.WriteLine($"TypeActifDonneesId: {result.TypeActifDonneesId}");

            // Vérifier si le type existe dans la base de données
            if (result.TypeActifDonneesId.HasValue)
            {
                var typeExists = await _context.Set<TypeActifDonneesEntity>()
                    .AnyAsync(t => t.Id == result.TypeActifDonneesId.Value);
                Console.WriteLine($"Type existe dans la BD: {typeExists}");

                if (typeExists)
                {
                    var type = await _context.Set<TypeActifDonneesEntity>()
                        .FirstOrDefaultAsync(t => t.Id == result.TypeActifDonneesId.Value);
                    Console.WriteLine($"Type trouvé: {type?.Nom} (ID: {type?.Id})");
                }
            }
        }

        return result;
    }

    /// <inheritdoc/>
    public override async Task<IEnumerable<ActifDonnees>> ObtenirTousAsync()
    {
        return await ObtenirTousAsync(ActifDonneesInclude.Basic | ActifDonneesInclude.Type);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirTousAsync(ActifDonneesInclude includes)
    {
        Console.WriteLine($"ObtenirTousAsync avec includes: {includes}");

        var query = _dbSet.AsQueryable();

        // Appliquer les inclusions en fonction des options
        query = AppliquerInclusions(query, includes);

        return await query.ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParTypeAsync(string type)
    {
        return await ObtenirParTypeAsync(type, ActifDonneesInclude.Basic);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParTypeAsync(string type, ActifDonneesInclude includes)
    {
        Console.WriteLine($"ObtenirParTypeAsync avec includes: {includes}");

        var query = _dbSet.AsQueryable();

        // Appliquer les inclusions en fonction des options
        query = AppliquerInclusions(query, includes);

        return await query
            .Where(a => a.TypeActifDonnees != null && a.TypeActifDonnees.Nom == type)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParClassificationAsync(ClassificationSensibilite classification)
    {
        return await _dbSet
            .Where(a => a.ClassificationSensibilite == classification)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParProprietaireAsync(string proprietaire)
    {
        return await _dbSet
            .Where(a => a.Proprietaire == proprietaire)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParTermeGlossaireAsync(int termeId)
    {
        // Note: Cette méthode nécessite une relation entre ActifDonnees et TermeGlossaire
        // qui n'est pas encore implémentée. Pour l'instant, nous retournons une liste vide.
        return Enumerable.Empty<ActifDonnees>();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParDomaineGouvernanceAsync(Guid domaineGouvernanceId)
    {
        return await _dbSet
            .Where(a => a.DomaineGouvernanceId == domaineGouvernanceId)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParConnexionSourceDonneesAsync(Guid connexionSourceDonneesId)
    {
        return await _dbSet
            .Where(a => a.ConnexionSourceDonneesId == connexionSourceDonneesId)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParStatutAsync(string statut)
    {
        return await _dbSet
            .Include(a => a.StatutActifDonnees)
            .Where(a => a.StatutActifDonnees != null && a.StatutActifDonnees.Nom == statut)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirLignageAsync(Guid actifId)
    {
        // Note: Cette méthode nécessite une relation de lignage entre les actifs de données
        // qui n'est pas encore implémentée. Pour l'instant, nous retournons une liste vide.
        return Enumerable.Empty<ActifDonnees>();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> RechercherAsync(string terme, ActifDonneesInclude includes = ActifDonneesInclude.Basic)
    {
        Console.WriteLine($"RechercherAsync avec terme: {terme} et includes: {includes}");

        if (string.IsNullOrWhiteSpace(terme))
        {
            return Enumerable.Empty<ActifDonnees>();
        }

        var query = _dbSet.AsQueryable();

        // Appliquer les inclusions en fonction des options
        query = AppliquerInclusions(query, includes);

        // Recherche optimisée avec EF Core
        return await query
            .Where(a =>
                a.Nom.Contains(terme) ||
                (a.Description != null && a.Description.Contains(terme)) ||
                (a.Proprietaire != null && a.Proprietaire.Contains(terme)) ||
                (a.CheminAcces != null && a.CheminAcces.Contains(terme))
            )
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<ActifDonnees?> ObtenirParNomAsync(string nom)
    {
        return await _dbSet
            .FirstOrDefaultAsync(a => a.Nom == nom);
    }

    /// <summary>
    /// Applique les inclusions spécifiées à la requête.
    /// </summary>
    /// <param name="query">Requête à laquelle appliquer les inclusions.</param>
    /// <param name="includes">Options d'inclusion.</param>
    /// <returns>Requête avec les inclusions appliquées.</returns>
    private IQueryable<ActifDonnees> AppliquerInclusions(IQueryable<ActifDonnees> query, ActifDonneesInclude includes)
    {
        // Appliquer les inclusions en fonction des options
        if (includes.HasFlag(ActifDonneesInclude.Type))
        {
            query = query.Include(a => a.TypeActifDonnees);
        }

        if (includes.HasFlag(ActifDonneesInclude.Format))
        {
            query = query.Include(a => a.FormatActifDonnees);
        }

        if (includes.HasFlag(ActifDonneesInclude.Source))
        {
            query = query.Include(a => a.SourceActifDonnees);
        }

        if (includes.HasFlag(ActifDonneesInclude.FrequenceMiseAJour))
        {
            query = query.Include(a => a.FrequenceMiseAJour);
        }

        if (includes.HasFlag(ActifDonneesInclude.Statut))
        {
            query = query.Include(a => a.StatutActifDonnees);
        }

        if (includes.HasFlag(ActifDonneesInclude.DomaineGouvernance))
        {
            query = query.Include(a => a.DomaineGouvernance);
        }

        if (includes.HasFlag(ActifDonneesInclude.ConnexionSourceDonnees))
        {
            query = query.Include(a => a.ConnexionSourceDonnees);
        }

        // Inclure les métadonnées si demandé
        if (includes.HasFlag(ActifDonneesInclude.Metadonnees))
        {
            query = query.Include(a => a.Metadonnees);
        }

        // Inclure les règles de qualité si demandé
        if (includes.HasFlag(ActifDonneesInclude.ReglesQualite))
        {
            query = query.Include(a => a.ReglesQualite);
        }

        // Inclure les termes du glossaire si demandé
        if (includes.HasFlag(ActifDonneesInclude.TermesGlossaire))
        {
            query = query.Include(a => a.TermesGlossaire);
        }

        // Inclure les produits de données si demandé
        if (includes.HasFlag(ActifDonneesInclude.ProduitsDonnees))
        {
            query = query.Include(a => a.ProduitsDonnees);
        }

        // Inclure les politiques si demandé
        if (includes.HasFlag(ActifDonneesInclude.Politiques))
        {
            query = query.Include(a => a.Politiques);
        }

        return query;
    }
}
