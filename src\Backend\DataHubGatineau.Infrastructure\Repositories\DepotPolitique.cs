using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;
using DataHubGatineau.Domain.Interfaces.Repositories;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace DataHubGatineau.Infrastructure.Repositories;

/// <summary>
/// Implémentation du dépôt pour les politiques.
/// </summary>
public class DepotPolitique : DepotBase<Politique>, IDepotPolitique
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DepotPolitique"/>.
    /// </summary>
    /// <param name="context">Contexte de base de données.</param>
    public DepotPolitique(CentreDonneesDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient toutes les politiques avec les entités liées.
    /// </summary>
    /// <returns>Collection de politiques avec les entités liées.</returns>
    public async Task<IEnumerable<Politique>> ObtenirTousAvecEntitesLieesAsync()
    {
        return await _context.Politiques
            .Include(p => p.PolitiqueParente)
            .Include(p => p.VersionsEnfants)
            .Include(p => p.TermesGlossaire)
                .ThenInclude(atg => atg.TermeGlossaire)
            .Include(p => p.ActifsDonnees)
                .ThenInclude(a => a.TypeActifDonnees)
            .OrderBy(p => p.Categorie)
            .ThenBy(p => p.Titre)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient une politique par identifiant avec les entités liées.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <returns>Politique avec les entités liées si trouvée.</returns>
    public async Task<Politique?> ObtenirParIdAvecEntitesLieesAsync(Guid id)
    {
        return await _context.Politiques
            .Include(p => p.PolitiqueParente)
            .Include(p => p.VersionsEnfants)
            .Include(p => p.TermesGlossaire)
                .ThenInclude(atg => atg.TermeGlossaire)
            .Include(p => p.ActifsDonnees)
                .ThenInclude(a => a.TypeActifDonnees)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    /// <summary>
    /// Obtient une politique par son code.
    /// </summary>
    /// <param name="code">Code de la politique.</param>
    /// <returns>Politique si trouvée.</returns>
    public async Task<Politique?> ObtenirParCodeAsync(string code)
    {
        return await _context.Politiques
            .FirstOrDefaultAsync(p => p.Code == code);
    }

    /// <summary>
    /// Obtient les politiques par catégorie.
    /// </summary>
    /// <param name="categorie">Catégorie des politiques.</param>
    /// <returns>Collection de politiques de la catégorie spécifiée.</returns>
    public async Task<IEnumerable<Politique>> ObtenirParCategorieAsync(string categorie)
    {
        return await _context.Politiques
            .Where(p => p.Categorie == categorie)
            .OrderBy(p => p.Titre)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les politiques par statut.
    /// </summary>
    /// <param name="statut">Statut des politiques.</param>
    /// <returns>Collection de politiques du statut spécifié.</returns>
    public async Task<IEnumerable<Politique>> ObtenirParStatutAsync(StatutPolitique statut)
    {
        return await _context.Politiques
            .Where(p => p.Statut == statut)
            .OrderBy(p => p.DateEntreeVigueur)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les politiques par niveau d'application.
    /// </summary>
    /// <param name="niveauApplication">Niveau d'application.</param>
    /// <returns>Collection de politiques du niveau spécifié.</returns>
    public async Task<IEnumerable<Politique>> ObtenirParNiveauApplicationAsync(NiveauApplicationPolitique niveauApplication)
    {
        return await _context.Politiques
            .Where(p => p.NiveauApplication == niveauApplication)
            .OrderBy(p => p.Titre)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les politiques actives.
    /// </summary>
    /// <returns>Collection de politiques actives.</returns>
    public async Task<IEnumerable<Politique>> ObtenirPolitiquesActivesAsync()
    {
        return await _context.Politiques
            .Where(p => p.EstActive)
            .OrderBy(p => p.Categorie)
            .ThenBy(p => p.Titre)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les politiques en vigueur à une date donnée.
    /// </summary>
    /// <param name="date">Date de référence.</param>
    /// <returns>Collection de politiques en vigueur.</returns>
    public async Task<IEnumerable<Politique>> ObtenirEnVigueurAsync(DateTime? date = null)
    {
        var dateRef = date ?? DateTime.UtcNow;

        return await _context.Politiques
            .Where(p => p.EstActive &&
                       (!p.DateEntreeVigueur.HasValue || p.DateEntreeVigueur <= dateRef) &&
                       (!p.DateExpiration.HasValue || p.DateExpiration > dateRef))
            .OrderBy(p => p.Categorie)
            .ThenBy(p => p.Titre)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les politiques qui expirent dans une période donnée.
    /// </summary>
    /// <param name="joursAvantExpiration">Nombre de jours avant expiration.</param>
    /// <returns>Collection de politiques qui expirent bientôt.</returns>
    public async Task<IEnumerable<Politique>> ObtenirExpirantBientotAsync(int joursAvantExpiration = 30)
    {
        var dateLimite = DateTime.UtcNow.AddDays(joursAvantExpiration);

        return await _context.Politiques
            .Where(p => p.EstActive &&
                       p.DateExpiration.HasValue &&
                       p.DateExpiration <= dateLimite &&
                       p.DateExpiration > DateTime.UtcNow)
            .OrderBy(p => p.DateExpiration)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les politiques nécessitant une révision.
    /// </summary>
    /// <returns>Collection de politiques à réviser.</returns>
    public async Task<IEnumerable<Politique>> ObtenirAReviserAsync()
    {
        var dateActuelle = DateTime.UtcNow;

        return await _context.Politiques
            .Where(p => p.EstActive &&
                       p.DateProchaineRevision.HasValue &&
                       p.DateProchaineRevision <= dateActuelle)
            .OrderBy(p => p.DateProchaineRevision)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les politiques parentes (sans parent).
    /// </summary>
    /// <returns>Collection de politiques parentes.</returns>
    public async Task<IEnumerable<Politique>> ObtenirPolitiquesParentesAsync()
    {
        return await _context.Politiques
            .Include(p => p.VersionsEnfants)
            .Where(p => p.PolitiqueParenteId == null)
            .OrderBy(p => p.Categorie)
            .ThenBy(p => p.Titre)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les politiques enfants d'une politique parente.
    /// </summary>
    /// <param name="politiqueParenteId">Identifiant de la politique parente.</param>
    /// <returns>Collection de politiques enfants.</returns>
    public async Task<IEnumerable<Politique>> ObtenirPolitiquesEnfantsAsync(Guid politiqueParenteId)
    {
        return await _context.Politiques
            .Where(p => p.PolitiqueParenteId == politiqueParenteId)
            .OrderBy(p => p.Titre)
            .ToListAsync();
    }

    /// <summary>
    /// Recherche des politiques par mots-clés.
    /// </summary>
    /// <param name="motsCles">Mots-clés de recherche.</param>
    /// <returns>Collection de politiques correspondantes.</returns>
    public async Task<IEnumerable<Politique>> RechercherParMotsClesAsync(string motsCles)
    {
        var termes = motsCles.ToLower().Split(' ', StringSplitOptions.RemoveEmptyEntries);

        return await _context.Politiques
            .Where(p => termes.Any(terme => 
                p.Titre.ToLower().Contains(terme) ||
                p.Description!.ToLower().Contains(terme) ||
                p.MotsCles!.ToLower().Contains(terme) ||
                p.Contenu.ToLower().Contains(terme)))
            .OrderBy(p => p.Titre)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si un code de politique existe déjà.
    /// </summary>
    /// <param name="code">Code à vérifier.</param>
    /// <param name="excludeId">Identifiant à exclure de la vérification (pour les mises à jour).</param>
    /// <returns>True si le code existe, false sinon.</returns>
    public async Task<bool> CodeExisteAsync(string code, Guid? excludeId = null)
    {
        var query = _context.Politiques.Where(p => p.Code == code);
        
        if (excludeId.HasValue)
        {
            query = query.Where(p => p.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    /// <summary>
    /// Obtient les statistiques des politiques.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    public async Task<Dictionary<string, int>> ObtenirStatistiquesAsync()
    {
        var stats = new Dictionary<string, int>();

        stats["Total"] = await _context.Politiques.CountAsync();
        stats["Actives"] = await _context.Politiques.CountAsync(p => p.EstActive);
        stats["Brouillon"] = await _context.Politiques.CountAsync(p => p.Statut == StatutPolitique.Brouillon);
        stats["EnRevision"] = await _context.Politiques.CountAsync(p => p.Statut == StatutPolitique.EnRevision);
        stats["EnAttenteApprobation"] = await _context.Politiques.CountAsync(p => p.Statut == StatutPolitique.EnAttenteApprobation);
        stats["Actives"] = await _context.Politiques.CountAsync(p => p.Statut == StatutPolitique.Active);
        stats["Archivees"] = await _context.Politiques.CountAsync(p => p.Statut == StatutPolitique.Archivee);

        var dateActuelle = DateTime.UtcNow;
        stats["EnVigueur"] = await _context.Politiques.CountAsync(p => 
            p.EstActive &&
            (!p.DateEntreeVigueur.HasValue || p.DateEntreeVigueur <= dateActuelle) &&
            (!p.DateExpiration.HasValue || p.DateExpiration > dateActuelle));

        var dateLimite = dateActuelle.AddDays(30);
        stats["ExpirantBientot"] = await _context.Politiques.CountAsync(p => 
            p.EstActive &&
            p.DateExpiration.HasValue &&
            p.DateExpiration <= dateLimite &&
            p.DateExpiration > dateActuelle);

        stats["AReviser"] = await _context.Politiques.CountAsync(p => 
            p.EstActive &&
            p.DateProchaineRevision.HasValue &&
            p.DateProchaineRevision <= dateActuelle);

        return stats;
    }

    /// <summary>
    /// Active ou désactive une politique.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <param name="estActive">État d'activation.</param>
    /// <returns>True si la mise à jour a réussi.</returns>
    public async Task<bool> ChangerEtatActivationAsync(Guid id, bool estActive)
    {
        var politique = await _context.Politiques.FindAsync(id);
        if (politique == null) return false;

        politique.EstActive = estActive;
        politique.DateModification = DateTime.UtcNow;
        
        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// Met à jour le statut d'une politique.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <param name="nouveauStatut">Nouveau statut.</param>
    /// <returns>True si la mise à jour a réussi.</returns>
    public async Task<bool> MettreAJourStatutAsync(Guid id, StatutPolitique nouveauStatut)
    {
        var politique = await _context.Politiques.FindAsync(id);
        if (politique == null) return false;

        politique.Statut = nouveauStatut;
        politique.DateModification = DateTime.UtcNow;

        // Si la politique est approuvée, mettre à jour la date d'approbation
        if (nouveauStatut == StatutPolitique.Active)
        {
            politique.DateApprobation = DateTime.UtcNow;
        }

        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// Associe des actifs de données à une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <param name="actifsIds">Liste des identifiants des actifs à associer.</param>
    /// <returns>True si l'association a réussi.</returns>
    public async Task<bool> AssocierActifsAsync(Guid politiqueId, IEnumerable<Guid> actifsIds)
    {
        var politique = await _context.Politiques
            .Include(p => p.ActifsDonnees)
            .FirstOrDefaultAsync(p => p.Id == politiqueId);

        if (politique == null) return false;

        var actifs = await _context.ActifsDonnees
            .Where(a => actifsIds.Contains(a.Id))
            .ToListAsync();

        foreach (var actif in actifs)
        {
            if (!politique.ActifsDonnees.Any(a => a.Id == actif.Id))
            {
                politique.ActifsDonnees.Add(actif);
            }
        }

        politique.DateModification = DateTime.UtcNow;
        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// Dissocie un actif de données d'une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <param name="actifId">Identifiant de l'actif à dissocier.</param>
    /// <returns>True si la dissociation a réussi.</returns>
    public async Task<bool> DissocierActifAsync(Guid politiqueId, Guid actifId)
    {
        var politique = await _context.Politiques
            .Include(p => p.ActifsDonnees)
            .FirstOrDefaultAsync(p => p.Id == politiqueId);

        if (politique == null) return false;

        var actif = politique.ActifsDonnees.FirstOrDefault(a => a.Id == actifId);
        if (actif == null) return false;

        politique.ActifsDonnees.Remove(actif);
        politique.DateModification = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// Obtient les actifs de données associés à une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>Collection des actifs associés ou null si la politique n'existe pas.</returns>
    public async Task<IEnumerable<ActifDonnees>?> ObtenirActifsAssociesAsync(Guid politiqueId)
    {
        var politique = await _context.Politiques
            .Include(p => p.ActifsDonnees)
                .ThenInclude(a => a.TypeActifDonnees)
            .Include(p => p.ActifsDonnees)
                .ThenInclude(a => a.ConnexionSourceDonnees)
            .FirstOrDefaultAsync(p => p.Id == politiqueId);

        return politique?.ActifsDonnees;
    }
}
