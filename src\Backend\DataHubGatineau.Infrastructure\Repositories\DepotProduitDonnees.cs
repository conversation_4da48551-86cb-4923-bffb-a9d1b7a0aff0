using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace DataHubGatineau.Infrastructure.Repositories;

/// <summary>
/// Implémentation du dépôt pour les produits de données.
/// </summary>
public class DepotProduitDonnees : DepotBase<ProduitDonnees>, IDepotProduitDonnees
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DepotProduitDonnees"/>.
    /// </summary>
    /// <param name="context">Contexte de base de données.</param>
    public DepotProduitDonnees(CentreDonneesDbContext context) : base(context)
    {
    }

    /// <inheritdoc/>
    public override async Task<IEnumerable<ProduitDonnees>> ObtenirTousAsync()
    {
        return await _dbSet
            .Include(p => p.DomaineGouvernance)
            .Include(p => p.ActifsDonnees)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public override async Task<ProduitDonnees?> ObtenirParIdAsync(Guid id)
    {
        return await _dbSet
            .Include(p => p.DomaineGouvernance)
            .Include(p => p.ActifsDonnees)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    /// <inheritdoc/>
    public async Task<ProduitDonnees?> ObtenirParIdAvecActifsAsync(Guid id)
    {
        return await _dbSet
            .Include(p => p.DomaineGouvernance)
            .Include(p => p.ActifsDonnees)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ProduitDonnees>> ObtenirParDomaineGouvernanceAsync(Guid domaineGouvernanceId)
    {
        return await _dbSet
            .Include(p => p.DomaineGouvernance)
            .Include(p => p.ActifsDonnees)
            .Where(p => p.DomaineGouvernanceId == domaineGouvernanceId)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ProduitDonnees>> ObtenirParProprietaireAsync(string proprietaire)
    {
        return await _dbSet
            .Include(p => p.DomaineGouvernance)
            .Include(p => p.ActifsDonnees)
            .Where(p => p.Proprietaire == proprietaire)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ProduitDonnees>> ObtenirParActifDonneesAsync(Guid actifDonneesId)
    {
        return await _dbSet
            .Include(p => p.DomaineGouvernance)
            .Include(p => p.ActifsDonnees)
            .Where(p => p.ActifsDonnees.Any(a => a.Id == actifDonneesId))
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ProduitDonnees>> RechercherAsync(string motCle)
    {
        return await _dbSet
            .Include(p => p.DomaineGouvernance)
            .Include(p => p.ActifsDonnees)
            .Where(p => p.Nom.Contains(motCle) || p.Description!.Contains(motCle))
            .ToListAsync();
    }
}
