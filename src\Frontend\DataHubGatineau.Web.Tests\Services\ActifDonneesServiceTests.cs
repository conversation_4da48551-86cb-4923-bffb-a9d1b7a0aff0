using DataHubGatineau.Web.Models.DataAsset;
using DataHubGatineau.Web.Services.Implementations;
using DataHubGatineau.Web.Services.Interfaces;
using Moq;
using System.Net;
using System.Text.Json;
using Xunit;

namespace DataHubGatineau.Web.Tests.Services;

public class ActifDonneesServiceTests
{
    private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
    private readonly HttpClient _httpClient;
    private readonly ActifDonneesService _service;
    private readonly JsonSerializerOptions _jsonOptions;

    public ActifDonneesServiceTests()
    {
        _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_httpMessageHandlerMock.Object)
        {
            BaseAddress = new Uri("http://localhost:5187/")
        };
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        
        _service = new ActifDonneesService(_httpClient, _jsonOptions);
    }

    [Fact]
    public async Task ObtenirTousAsync_ShouldReturnActifs_WhenApiReturnsData()
    {
        // Arrange
        var expectedActifs = new List<ActifDonnees>
        {
            new() { Id = Guid.NewGuid(), Nom = "Test Actif 1" },
            new() { Id = Guid.NewGuid(), Nom = "Test Actif 2" }
        };

        var jsonResponse = JsonSerializer.Serialize(expectedActifs, _jsonOptions);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jsonResponse, System.Text.Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.ObtenirTousAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.Equal("Test Actif 1", result.First().Nom);
    }

    [Fact]
    public async Task ObtenirParIdAsync_ShouldReturnActif_WhenActifExists()
    {
        // Arrange
        var actifId = Guid.NewGuid();
        var expectedActif = new ActifDonnees { Id = actifId, Nom = "Test Actif" };

        var jsonResponse = JsonSerializer.Serialize(expectedActif, _jsonOptions);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jsonResponse, System.Text.Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.ObtenirParIdAsync(actifId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(actifId, result.Id);
        Assert.Equal("Test Actif", result.Nom);
    }

    [Fact]
    public async Task ObtenirParIdAsync_ShouldReturnNull_WhenActifNotFound()
    {
        // Arrange
        var actifId = Guid.NewGuid();
        var httpResponse = new HttpResponseMessage(HttpStatusCode.NotFound);

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.ObtenirParIdAsync(actifId);

        // Assert
        Assert.Null(result);
    }
}
