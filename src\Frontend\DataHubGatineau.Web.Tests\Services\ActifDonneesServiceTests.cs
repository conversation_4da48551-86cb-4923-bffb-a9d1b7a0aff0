using DataHubGatineau.Web.Models;
using DataHubGatineau.Web.Services.Implementations;
using DataHubGatineau.Web.Services.Interfaces;
using Moq;
using Moq.Protected;
using System.Net;
using System.Text.Json;
using Xunit;

namespace DataHubGatineau.Web.Tests.Services;

public class ActifDonneesServiceTests
{
    private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
    private readonly HttpClient _httpClient;
    private readonly Mock<ICacheService> _cacheServiceMock;
    private readonly ActifDonneesService _service;

    public ActifDonneesServiceTests()
    {
        _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        _httpClient = new HttpClient(_httpMessageHandlerMock.Object)
        {
            BaseAddress = new Uri("http://localhost:5187/")
        };

        _cacheServiceMock = new Mock<ICacheService>();

        _service = new ActifDonneesService(_httpClient, _cacheServiceMock.Object);
    }

    [Fact]
    public async Task ObtenirTousAsync_ShouldReturnActifs_WhenApiReturnsData()
    {
        // Arrange
        var expectedActifs = new List<ActifDonnees>
        {
            new() { Id = Guid.NewGuid(), Nom = "Test Actif 1" },
            new() { Id = Guid.NewGuid(), Nom = "Test Actif 2" }
        };

        // Setup cache service to return null (cache miss)
        _cacheServiceMock
            .Setup(x => x.GetOrCreateAsync<IEnumerable<ActifDonnees>>(
                It.IsAny<string>(),
                It.IsAny<Func<Task<IEnumerable<ActifDonnees>>>>(),
                It.IsAny<TimeSpan>()))
            .Returns<string, Func<Task<IEnumerable<ActifDonnees>>>, TimeSpan>(
                async (key, factory, expiration) => await factory());

        var jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        var jsonResponse = JsonSerializer.Serialize(expectedActifs, jsonOptions);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jsonResponse, System.Text.Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.ObtenirTousAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.Equal("Test Actif 1", result.First().Nom);
    }

    [Fact]
    public async Task ObtenirParIdAsync_ShouldReturnActif_WhenActifExists()
    {
        // Arrange
        var actifId = Guid.NewGuid();
        var expectedActif = new ActifDonnees { Id = actifId, Nom = "Test Actif" };

        // Setup cache service to return null (cache miss)
        _cacheServiceMock
            .Setup(x => x.GetOrCreateAsync<ActifDonnees?>(
                It.IsAny<string>(),
                It.IsAny<Func<Task<ActifDonnees?>>>(),
                It.IsAny<TimeSpan>()))
            .Returns<string, Func<Task<ActifDonnees?>>, TimeSpan>(
                async (key, factory, expiration) => await factory());

        var jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        var jsonResponse = JsonSerializer.Serialize(expectedActif, jsonOptions);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jsonResponse, System.Text.Encoding.UTF8, "application/json")
        };

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.ObtenirParIdAsync(actifId);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(actifId, result.Id);
        Assert.Equal("Test Actif", result.Nom);
    }

    [Fact]
    public async Task ObtenirParIdAsync_ShouldReturnFallbackActif_WhenActifNotFound()
    {
        // Arrange
        var actifId = Guid.NewGuid();

        // Setup cache service to return null (cache miss)
        _cacheServiceMock
            .Setup(x => x.GetOrCreateAsync<ActifDonnees?>(
                It.IsAny<string>(),
                It.IsAny<Func<Task<ActifDonnees?>>>(),
                It.IsAny<TimeSpan>()))
            .Returns<string, Func<Task<ActifDonnees?>>, TimeSpan>(
                async (key, factory, expiration) => await factory());

        _cacheServiceMock
            .Setup(x => x.TryGetValue<IEnumerable<ActifDonnees>>(It.IsAny<string>(), out It.Ref<IEnumerable<ActifDonnees>>.IsAny))
            .Returns(false);

        var httpResponse = new HttpResponseMessage(HttpStatusCode.NotFound);

        _httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(httpResponse);

        // Act
        var result = await _service.ObtenirParIdAsync(actifId);

        // Assert
        Assert.NotNull(result); // Le service retourne un actif factice en cas d'erreur
        Assert.Equal(actifId, result.Id);
        Assert.Contains("temporaire", result.Nom.ToLower());
    }
}
