@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services.Interfaces
@inject ITypeActifDonneesService TypeActifDonneesService
@inject ISourceActifDonneesService SourceActifDonneesService
@inject IDomaineGouvernanceService DomaineGouvernanceService
@inject IConnexionSourceDonneesService ConnexionSourceDonneesService

<div class="card shadow-sm border-0 rounded-4 mb-4">
    <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0 fw-bold">
            <i class="bi bi-funnel-fill text-primary me-2"></i>
            Filtres avancés
        </h5>
        <button class="btn btn-sm btn-outline-secondary rounded-pill" @onclick="ReinitialiserFiltres">
            <i class="bi bi-arrow-counterclockwise me-1"></i> Réinitialiser
        </button>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">
                        <i class="bi bi-tag text-primary me-2"></i>
                        Type d'actif
                    </label>
                    <select class="form-select" @bind="TypeId">
                        <option value="">Tous les types</option>
                        @foreach (var type in _types)
                        {
                            <option value="@type.Id">@type.Nom</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">
                        <i class="bi bi-database text-primary me-2"></i>
                        Source
                    </label>
                    <select class="form-select" @bind="SourceId">
                        <option value="">Toutes les sources</option>
                        @foreach (var source in _sources)
                        {
                            <option value="@source.Id">@source.Nom</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">
                        <i class="bi bi-diagram-3 text-primary me-2"></i>
                        Domaine de gouvernance
                    </label>
                    <select class="form-select" @bind="DomaineId">
                        <option value="">Tous les domaines</option>
                        @foreach (var domaine in _domaines)
                        {
                            <option value="@domaine.Id">@domaine.Nom</option>
                        }
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">
                        <i class="bi bi-server text-primary me-2"></i>
                        Base de données
                    </label>
                    <select class="form-select" @bind="ConnexionId">
                        <option value="">Toutes les bases de données</option>
                        @foreach (var connexion in _connexions)
                        {
                            <option value="@connexion.Id">@connexion.BaseDonnees (@connexion.Serveur)</option>
                        }
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">
                        <i class="bi bi-exclamation-triangle text-primary me-2"></i>
                        Actifs critiques
                    </label>
                    <select class="form-select" @bind="EstCritique">
                        <option value="">Tous</option>
                        <option value="true">Oui</option>
                        <option value="false">Non</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">
                        <i class="bi bi-shield-lock text-primary me-2"></i>
                        Classification
                    </label>
                    <select class="form-select" @bind="Classification">
                        <option value="">Toutes</option>
                        <option value="Public">Public</option>
                        <option value="Interne">Interne</option>
                        <option value="Confidentiel">Confidentiel</option>
                        <option value="Critique">Critique</option>
                        <option value="Restreint">Restreint</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <div class="mb-3 w-100">
                    <button class="btn btn-primary w-100" @onclick="AppliquerFiltres">
                        <i class="bi bi-funnel-fill me-1"></i> Appliquer les filtres
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public Guid? TypeId { get; set; }

    [Parameter]
    public EventCallback<Guid?> TypeIdChanged { get; set; }

    [Parameter]
    public Guid? SourceId { get; set; }

    [Parameter]
    public EventCallback<Guid?> SourceIdChanged { get; set; }

    [Parameter]
    public Guid? DomaineId { get; set; }

    [Parameter]
    public EventCallback<Guid?> DomaineIdChanged { get; set; }

    [Parameter]
    public string? EstCritique { get; set; }

    [Parameter]
    public EventCallback<string?> EstCritiqueChanged { get; set; }

    [Parameter]
    public string? Classification { get; set; }

    [Parameter]
    public EventCallback<string?> ClassificationChanged { get; set; }

    [Parameter]
    public Guid? ConnexionId { get; set; }

    [Parameter]
    public EventCallback<Guid?> ConnexionIdChanged { get; set; }

    [Parameter]
    public EventCallback OnFiltresAppliques { get; set; }

    private List<TypeActifDonneesItem> _types = new List<TypeActifDonneesItem>();
    private List<SourceActifDonneesItem> _sources = new List<SourceActifDonneesItem>();
    private List<DomaineGouvernance> _domaines = new List<DomaineGouvernance>();
    private List<ConnexionSourceDonnees> _connexions = new List<ConnexionSourceDonnees>();

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    private async Task ChargerDonnees()
    {
        try
        {
            var typesTask = TypeActifDonneesService.ObtenirTousAsync();
            var sourcesTask = SourceActifDonneesService.ObtenirTousAsync();
            var domainesTask = DomaineGouvernanceService.ObtenirTousAsync();
            var connexionsTask = ConnexionSourceDonneesService.ObtenirTousAsync();

            await Task.WhenAll(typesTask, sourcesTask, domainesTask, connexionsTask);

            _types = typesTask.Result.ToList();
            _sources = sourcesTask.Result.ToList();
            _domaines = domainesTask.Result.ToList();
            _connexions = connexionsTask.Result.ToList();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement des données de filtrage: {ex.Message}");
        }
    }

    private async Task AppliquerFiltres()
    {
        await TypeIdChanged.InvokeAsync(TypeId);
        await SourceIdChanged.InvokeAsync(SourceId);
        await DomaineIdChanged.InvokeAsync(DomaineId);
        await EstCritiqueChanged.InvokeAsync(EstCritique);
        await ClassificationChanged.InvokeAsync(Classification);
        await ConnexionIdChanged.InvokeAsync(ConnexionId);
        await OnFiltresAppliques.InvokeAsync();
    }

    private async Task ReinitialiserFiltres()
    {
        TypeId = null;
        SourceId = null;
        DomaineId = null;
        EstCritique = null;
        Classification = null;
        ConnexionId = null;

        await TypeIdChanged.InvokeAsync(null);
        await SourceIdChanged.InvokeAsync(null);
        await DomaineIdChanged.InvokeAsync(null);
        await EstCritiqueChanged.InvokeAsync(null);
        await ClassificationChanged.InvokeAsync(null);
        await ConnexionIdChanged.InvokeAsync(null);
        await OnFiltresAppliques.InvokeAsync();
    }
}
