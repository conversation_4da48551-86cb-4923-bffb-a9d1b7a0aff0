@using DataHubGatineau.Web.Services.Interfaces

@if (AfficherModal && Utilisateur != null)
{
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-circle"></i> Détails de l'Utilisateur
                    </h5>
                    <button type="button" class="btn-close" @onclick="FermerModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- En-tête utilisateur -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="avatar-circle-large">
                                        @Utilisateur.Prenom.FirstOrDefault()@Utilisateur.Nom.FirstOrDefault()
                                    </div>
                                </div>
                                <div class="col">
                                    <h4 class="mb-1">@Utilisateur.NomComplet</h4>
                                    <p class="text-muted mb-1">@Utilisateur.NomUtilisateur</p>
                                    <p class="mb-0">
                                        <span class="badge bg-@(Utilisateur.EstActif ? "success" : "secondary") me-2">
                                            @(Utilisateur.EstActif ? "Actif" : "Inactif")
                                        </span>
                                        <span class="badge bg-info">@Utilisateur.NiveauPrincipal</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations personnelles -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-person"></i> Informations Personnelles</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">Prénom:</label>
                                        <span class="info-value">@Utilisateur.Prenom</span>
                                    </div>
                                    <div class="info-item">
                                        <label class="info-label">Nom:</label>
                                        <span class="info-value">@Utilisateur.Nom</span>
                                    </div>
                                    <div class="info-item">
                                        <label class="info-label">Nom d'utilisateur:</label>
                                        <span class="info-value">@Utilisateur.NomUtilisateur</span>
                                    </div>
                                    @if (!string.IsNullOrEmpty(Utilisateur.Telephone))
                                    {
                                        <div class="info-item">
                                            <label class="info-label">Téléphone:</label>
                                            <span class="info-value">
                                                <i class="bi bi-telephone me-1"></i>@Utilisateur.Telephone
                                                @if (!string.IsNullOrEmpty(Utilisateur.Poste))
                                                {
                                                    <span class="text-muted"> poste @Utilisateur.Poste</span>
                                                }
                                            </span>
                                        </div>
                                    }
                                    else if (!string.IsNullOrEmpty(Utilisateur.Poste))
                                    {
                                        <div class="info-item">
                                            <label class="info-label">Poste:</label>
                                            <span class="info-value">@Utilisateur.Poste</span>
                                        </div>
                                    }
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <label class="info-label">Courriel:</label>
                                        <span class="info-value">
                                            <a href="mailto:@Utilisateur.Courriel">@Utilisateur.Courriel</a>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <label class="info-label">Date de création:</label>
                                        <span class="info-value">@Utilisateur.DateCreation.ToString("dd/MM/yyyy à HH:mm")</span>
                                    </div>
                                    <div class="info-item">
                                        <label class="info-label">Dernière connexion:</label>
                                        <span class="info-value">
                                            @if (Utilisateur.DerniereConnexion.HasValue)
                                            {
                                                @Utilisateur.DerniereConnexion.Value.ToString("dd/MM/yyyy à HH:mm")
                                            }
                                            else
                                            {
                                                <span class="text-muted">Jamais connecté</span>
                                            }
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rôles et permissions -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-shield-check"></i> Rôles et Permissions</h6>
                        </div>
                        <div class="card-body">
                            @if (Utilisateur.Roles.Any())
                            {
                                <div class="row">
                                    @foreach (var role in Utilisateur.Roles.OrderBy(r => r.Niveau))
                                    {
                                        <div class="col-md-6 mb-3">
                                            <div class="role-card">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge <EMAIL> me-2">@role.Nom</span>
                                                    @if (role.EstSysteme)
                                                    {
                                                        <small class="text-warning">
                                                            <i class="bi bi-lock-fill" title="Rôle système"></i>
                                                        </small>
                                                    }
                                                </div>
                                                <small class="text-muted d-block mt-1">@role.Description</small>
                                                <small class="text-info">Niveau: @role.Niveau</small>
                                            </div>
                                        </div>
                                    }
                                </div>

                                <!-- Résumé des permissions par niveau -->
                                <div class="mt-3">
                                    <h6 class="text-muted">Résumé des Permissions:</h6>
                                    @{
                                        var rolesParNiveau = Utilisateur.Roles.GroupBy(r => r.Niveau);
                                    }
                                    @foreach (var groupe in rolesParNiveau)
                                    {
                                        <div class="permission-level mb-2">
                                            <strong>@groupe.Key:</strong>
                                            @foreach (var role in groupe)
                                            {
                                                <span class="badge <EMAIL> me-1">@role.Nom</span>
                                            }
                                        </div>
                                    }
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    Aucun rôle assigné à cet utilisateur.
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Statistiques d'activité -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-activity"></i> Activité</h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <h4 class="text-primary">@CalculerJoursDepuisCreation()</h4>
                                        <small class="text-muted">Jours depuis création</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <h4 class="text-info">@Utilisateur.Roles.Count</h4>
                                        <small class="text-muted">Rôles assignés</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <h4 class="text-@(Utilisateur.DerniereConnexion.HasValue ? "success" : "warning")">
                                            @(Utilisateur.DerniereConnexion.HasValue ? CalculerJoursDepuisDerniereConnexion() : "N/A")
                                        </h4>
                                        <small class="text-muted">Jours depuis dernière connexion</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions rapides -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-lightning"></i> Actions Rapides</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-outline-primary btn-sm" @onclick="() => OnModifierUtilisateur.InvokeAsync(Utilisateur)">
                                    <i class="bi bi-pencil"></i> Modifier
                                </button>
                                <button class="btn btn-outline-@(Utilisateur.EstActif ? "warning" : "success") btn-sm" @onclick="() => OnBasculerActivation.InvokeAsync(Utilisateur)">
                                    <i class="bi bi-@(Utilisateur.EstActif ? "pause" : "play")"></i> 
                                    @(Utilisateur.EstActif ? "Désactiver" : "Activer")
                                </button>
                                <button class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-envelope"></i> Envoyer courriel
                                </button>
                                <button class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-key"></i> Réinitialiser mot de passe
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="FermerModal">
                        Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

<style>
    .avatar-circle-large {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: #007bff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 24px;
    }

    .info-item {
        margin-bottom: 0.75rem;
    }

    .info-label {
        font-weight: 600;
        color: #6c757d;
        display: inline-block;
        min-width: 120px;
    }

    .info-value {
        color: #212529;
    }

    .role-card {
        padding: 0.75rem;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        background-color: #f8f9fa;
    }

    .permission-level {
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-item h4 {
        margin-bottom: 0.25rem;
    }
</style>

@code {
    [Parameter] public bool AfficherModal { get; set; }
    [Parameter] public UtilisateurAvecRoles? Utilisateur { get; set; }
    [Parameter] public EventCallback OnModalFermee { get; set; }
    [Parameter] public EventCallback<UtilisateurAvecRoles> OnModifierUtilisateur { get; set; }
    [Parameter] public EventCallback<UtilisateurAvecRoles> OnBasculerActivation { get; set; }

    private void FermerModal()
    {
        AfficherModal = false;
        OnModalFermee.InvokeAsync();
    }

    private int CalculerJoursDepuisCreation()
    {
        if (Utilisateur == null) return 0;
        return (DateTime.Now - Utilisateur.DateCreation).Days;
    }

    private string CalculerJoursDepuisDerniereConnexion()
    {
        if (Utilisateur?.DerniereConnexion == null) return "N/A";
        var jours = (DateTime.Now - Utilisateur.DerniereConnexion.Value).Days;
        return jours == 0 ? "Aujourd'hui" : $"{jours}";
    }
}
