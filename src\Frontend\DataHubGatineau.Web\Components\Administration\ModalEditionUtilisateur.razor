@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Models.Auditoria
@using Microsoft.AspNetCore.Components.Forms
@inject IServiceGestionUtilisateurs ServiceGestionUtilisateurs
@inject IServiceAudit ServiceAudit
@inject IJSRuntime JSRuntime

<div class="modal fade" id="modalEditionUtilisateur" tabindex="-1" aria-labelledby="modalEditionUtilisateurLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalEditionUtilisateurLabel">
                    <i class="fas fa-user-edit me-2"></i>Modifier l'utilisateur
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fermer"></button>
            </div>
            
            <EditForm Model="DemandeModification" OnValidSubmit="ModifierUtilisateur">
                <DataAnnotationsValidator />
                
                <div class="modal-body">
                    @if (!string.IsNullOrEmpty(MessageErreur))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>@MessageErreur
                        </div>
                    }

                    @if (UtilisateurSelectionne != null)
                    {
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom d'utilisateur</label>
                                    <input type="text" class="form-control" value="@UtilisateurSelectionne.NomUtilisateur" readonly />
                                    <small class="form-text text-muted">Le nom d'utilisateur ne peut pas être modifié</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Courriel *</label>
                                    <InputText class="form-control" @bind-Value="DemandeModification.Courriel" />
                                    <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => DemandeModification.Courriel)" />
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Prénom *</label>
                                    <InputText class="form-control" @bind-Value="DemandeModification.Prenom" />
                                    <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => DemandeModification.Prenom)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom *</label>
                                    <InputText class="form-control" @bind-Value="DemandeModification.Nom" />
                                    <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => DemandeModification.Nom)" />
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Téléphone</label>
                                    <InputText class="form-control" @bind-Value="DemandeModification.Telephone" placeholder="(*************" />
                                    <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => DemandeModification.Telephone)" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Poste</label>
                                    <InputText class="form-control" @bind-Value="DemandeModification.Poste" placeholder="1234" />
                                    <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => DemandeModification.Poste)" />
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Statut</label>
                                    <div class="form-check form-switch">
                                        <InputCheckbox class="form-check-input" @bind-Value="EstActifLocal" />
                                        <label class="form-check-label">
                                            @(EstActifLocal ? "Actif" : "Inactif")
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Rôles</label>
                            @if (RolesDisponibles != null && RolesDisponibles.Any())
                            {
                                <div class="row">
                                    @foreach (var role in RolesDisponibles)
                                    {
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       checked="@(DemandeModification.IdsRoles?.Contains(role.Id) == true)"
                                                       @onchange="@((ChangeEventArgs e) => GererSelectionRole(role.Id, (bool)e.Value!))" />
                                                <label class="form-check-label">
                                                    @role.Nom
                                                    @if (!string.IsNullOrEmpty(role.Description))
                                                    {
                                                        <small class="text-muted d-block">@role.Description</small>
                                                    }
                                                </label>
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                            else
                            {
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>Aucun rôle disponible
                                </div>
                            }
                        </div>
                    }
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Annuler
                    </button>
                    <button type="submit" class="btn btn-primary" disabled="@EstEnCoursDeModification">
                        @if (EstEnCoursDeModification)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        }
                        else
                        {
                            <i class="fas fa-save me-2"></i>
                        }
                        Enregistrer les modifications
                    </button>
                </div>
            </EditForm>
        </div>
    </div>
</div>

@code {
    [Parameter] public EventCallback OnUtilisateurModifie { get; set; }
    
    private UtilisateurAvecRoles? UtilisateurSelectionne { get; set; }
    private DemandeMiseAJourUtilisateur DemandeModification { get; set; } = new();
    private List<InfoRole> RolesDisponibles { get; set; } = new();
    private bool EstEnCoursDeModification { get; set; }
    private string MessageErreur { get; set; } = string.Empty;

    private bool EstActifLocal
    {
        get => DemandeModification.EstActif ?? true;
        set => DemandeModification.EstActif = value;
    }

    protected override async Task OnInitializedAsync()
    {
        await ChargerRolesDisponibles();
    }

    public async Task OuvrirModal(UtilisateurAvecRoles utilisateur)
    {
        UtilisateurSelectionne = utilisateur;
        DemandeModification = new DemandeMiseAJourUtilisateur
        {
            Courriel = utilisateur.Courriel,
            Prenom = utilisateur.Prenom,
            Nom = utilisateur.Nom,
            Telephone = utilisateur.Telephone,
            Poste = utilisateur.Poste,
            EstActif = utilisateur.EstActif,
            IdsRoles = utilisateur.Roles.Select(r => r.Id).ToList()
        };
        
        MessageErreur = string.Empty;
        StateHasChanged();
        
        var modal = await JSRuntime.InvokeAsync<object>("bootstrap.Modal.getOrCreateInstance",
            await JSRuntime.InvokeAsync<object>("document.getElementById", "modalEditionUtilisateur"));
        await JSRuntime.InvokeVoidAsync("eval", "arguments[0].show()", modal);
    }

    private async Task ChargerRolesDisponibles()
    {
        try
        {
            RolesDisponibles = await ServiceGestionUtilisateurs.ObtenirTousRolesAsync();
        }
        catch (Exception ex)
        {
            MessageErreur = $"Erreur lors du chargement des rôles : {ex.Message}";
        }
    }

    private void GererSelectionRole(Guid roleId, bool estSelectionne)
    {
        DemandeModification.IdsRoles ??= new List<Guid>();
        
        if (estSelectionne)
        {
            if (!DemandeModification.IdsRoles.Contains(roleId))
            {
                DemandeModification.IdsRoles.Add(roleId);
            }
        }
        else
        {
            DemandeModification.IdsRoles.Remove(roleId);
        }
    }

    private async Task ModifierUtilisateur()
    {
        if (UtilisateurSelectionne == null) return;

        EstEnCoursDeModification = true;
        MessageErreur = string.Empty;
        StateHasChanged();

        try
        {
            // Sauvegarder les valeurs avant modification pour l'audit
            var valeursAvant = new
            {
                UtilisateurSelectionne.Courriel,
                UtilisateurSelectionne.Prenom,
                UtilisateurSelectionne.Nom,
                UtilisateurSelectionne.Telephone,
                UtilisateurSelectionne.Poste,
                UtilisateurSelectionne.EstActif,
                Roles = UtilisateurSelectionne.Roles?.Select(r => r.Nom).ToList()
            };

            var utilisateurModifie = await ServiceGestionUtilisateurs.MettreAJourUtilisateurAsync(UtilisateurSelectionne.Id, DemandeModification);

            if (utilisateurModifie != null)
            {
                // Enregistrer l'audit de modification
                var valeursApres = new
                {
                    DemandeModification.Courriel,
                    DemandeModification.Prenom,
                    DemandeModification.Nom,
                    DemandeModification.Telephone,
                    DemandeModification.Poste,
                    DemandeModification.EstActif,
                    Roles = DemandeModification.RolesSelectionnes
                };

                await ServiceAudit.EnregistrerModificationAsync(
                    "Utilisateur",
                    UtilisateurSelectionne.Id,
                    $"{UtilisateurSelectionne.Prenom} {UtilisateurSelectionne.Nom}",
                    valeursAvant,
                    valeursApres,
                    "Modification des informations utilisateur via interface d'administration",
                    CategorieAudit.GestionUtilisateurs,
                    ClassificationSensibilite.Confidentiel);

                var modal = await JSRuntime.InvokeAsync<object>("bootstrap.Modal.getInstance",
                    await JSRuntime.InvokeAsync<object>("document.getElementById", "modalEditionUtilisateur"));
                await JSRuntime.InvokeVoidAsync("eval", "arguments[0].hide()", modal);

                await OnUtilisateurModifie.InvokeAsync();

                await JSRuntime.InvokeVoidAsync("afficherNotificationSucces", "Utilisateur modifié avec succès");
            }
            else
            {
                MessageErreur = "Une erreur est survenue lors de la modification";

                // Enregistrer l'échec dans l'audit
                await ServiceAudit.EnregistrerActionAsync(
                    "Échec modification utilisateur",
                    "Utilisateur",
                    UtilisateurSelectionne.Id,
                    $"{UtilisateurSelectionne.Prenom} {UtilisateurSelectionne.Nom}",
                    TypeActionAudit.Modification,
                    NiveauGraviteAudit.Erreur,
                    CategorieAudit.GestionUtilisateurs,
                    commentaire: "Échec de la modification utilisateur",
                    classification: ClassificationSensibilite.Confidentiel);
            }
        }
        catch (Exception ex)
        {
            MessageErreur = $"Erreur lors de la modification : {ex.Message}";

            // Enregistrer l'erreur dans l'audit
            await ServiceAudit.EnregistrerActionAsync(
                "Erreur modification utilisateur",
                "Utilisateur",
                UtilisateurSelectionne?.Id,
                UtilisateurSelectionne != null ? $"{UtilisateurSelectionne.Prenom} {UtilisateurSelectionne.Nom}" : null,
                TypeActionAudit.Modification,
                NiveauGraviteAudit.Erreur,
                CategorieAudit.GestionUtilisateurs,
                commentaire: $"Exception lors de la modification: {ex.Message}",
                classification: ClassificationSensibilite.Confidentiel);
        }
        finally
        {
            EstEnCoursDeModification = false;
            StateHasChanged();
        }
    }
}
