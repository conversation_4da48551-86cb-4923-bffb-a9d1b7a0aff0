@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services.Interfaces
@inject IWorkflowApprobationService WorkflowApprobationService
@inject IExceptionHandlingService ExceptionHandlingService

<div class="row">
    <div class="col-md-12">
        <h5>Workflow d'approbation</h5>

        @if (ActifDonnees.EstElementCritique)
        {
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i> Cet actif de données est marqué comme élément critique et nécessite une approbation avant publication.
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    Statut actuel
                </div>
                <div class="card-body">
                    <h6 class="card-title">
                        <span class="badge @GetStatusBadgeClass(ActifDonnees.StatutActifDonnees?.Nom ?? "")">@(ActifDonnees.StatutActifDonnees?.Nom ?? "")</span>
                    </h6>
                    <p class="card-text">
                        <small class="text-muted">Dernière modification: @ActifDonnees.DateModification.ToString("dd/MM/yyyy HH:mm")</small>
                    </p>

                    @if (ActifDonnees.Id != Guid.Empty)
                    {
                        @if (!WorkflowsActifs.Any())
                        {
                            <button class="btn btn-primary" @onclick="SoumettreApprobation">
                                <i class="bi bi-send"></i> Soumettre pour approbation
                            </button>
                        }
                        else
                        {
                            <button class="btn btn-success" @onclick="SoumettreApprobation">
                                <i class="bi bi-send"></i> Resoumettre pour approbation
                            </button>
                        }
                    }
                </div>
            </div>

            @if (WorkflowsActifs.Any())
            {
                <h6>Workflows d'approbation actifs</h6>
                <div class="table-responsive mb-4">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>Date de soumission</th>
                                <th>Statut</th>
                                <th>Initiateur</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var workflow in WorkflowsActifs)
                            {
                                <tr>
                                    <td>@workflow.DateSoumission?.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(workflow.Statut)">
                                            @workflow.Statut
                                        </span>
                                    </td>
                                    <td>@workflow.Initiateur?.NomComplet</td>
                                    <td>
                                        <button class="btn btn-sm @(SelectedWorkflow?.Id == workflow.Id ? "btn-primary" : "btn-outline-primary")"
                                                @onclick="() => AfficherDetailsWorkflow(workflow.Id)">
                                            <i class="bi bi-@(SelectedWorkflow?.Id == workflow.Id ? "eye-fill" : "eye")"></i>
                                            @(SelectedWorkflow?.Id == workflow.Id ? "Sélectionné" : "Voir détails")
                                        </button>
                                        <button class="btn btn-sm btn-outline-info ms-1" @onclick="() => AfficherModalDetails(workflow.Id)">
                                            <i class="bi bi-info-circle"></i> Info
                                        </button>
                                        @if (workflow.Statut == "En attente")
                                        {
                                            <button class="btn btn-sm btn-outline-danger ms-1" @onclick="() => AnnulerWorkflow(workflow.Id)">
                                                <i class="bi bi-x-circle"></i> Annuler
                                            </button>
                                        }
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }

            @if (SelectedWorkflow != null)
            {
                <div class="card mb-3">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="bi bi-diagram-3"></i> Workflow d'approbation
                                <small class="text-muted ms-2">(ID: @SelectedWorkflow.Id.ToString().Substring(0, 8)...)</small>
                            </h6>
                            <div class="progress" style="width: 200px;">
                                @{
                                    var totalEtapes = SelectedWorkflow.Etapes.Count();
                                    var etapesApprouvees = SelectedWorkflow.Etapes.Count(e => e.Statut == "Approuvé");
                                    var progressPercentage = totalEtapes > 0 ? (etapesApprouvees * 100) / totalEtapes : 0;
                                    var workflowComplete = etapesApprouvees == totalEtapes && totalEtapes > 0;
                                }
                                <div class="progress-bar bg-success" role="progressbar"
                                     style="width: @(progressPercentage)%"
                                     aria-valuenow="@progressPercentage" aria-valuemin="0" aria-valuemax="100">
                                    @etapesApprouvees/@totalEtapes
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Statut:</strong>
                                <span class="badge @GetWorkflowStatusBadgeClass(SelectedWorkflow.Statut)">
                                    @SelectedWorkflow.Statut
                                </span>
                            </div>
                            <div class="col-md-4">
                                <strong>Initié par:</strong> @(SelectedWorkflow.Initiateur?.NomComplet ?? "Inconnu")
                            </div>
                            <div class="col-md-4">
                                <strong>Date de création:</strong> @SelectedWorkflow.DateCreation.ToString("dd/MM/yyyy HH:mm")
                            </div>
                        </div>
                    </div>
                </div>

                <h6>Étapes d'approbation</h6>

                @if (workflowComplete)
                {
                    <div class="alert alert-success d-flex align-items-center mb-3">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <div>
                            <strong>Workflow complètement approuvé!</strong><br>
                            <small>Toutes les étapes ont été approuvées avec succès. L'actif de données est maintenant validé.</small>
                        </div>
                    </div>
                }

                <div class="timeline">
                    @foreach (var etape in SelectedWorkflow.Etapes.OrderBy(e => e.Ordre))
                    {
                        <div class="timeline-item">
                            <div class="timeline-marker @GetEtapeMarkerClass(etape.Statut)"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">@etape.Nom</h6>
                                <p>
                                    <span class="badge @GetStatusBadgeClass(etape.Statut)">@etape.Statut</span>
                                    @if (etape.DateApprobation.HasValue)
                                    {
                                        <small class="text-muted ms-2">@etape.DateApprobation?.ToString("dd/MM/yyyy HH:mm")</small>
                                    }
                                </p>
                                <p class="text-muted small">
                                    <i class="bi bi-person"></i> Approbateur: @(etape.Approbateur?.NomComplet ?? "Non assigné")
                                </p>
                                @if (!string.IsNullOrEmpty(etape.Commentaires))
                                {
                                    <div class="alert alert-light py-2 mt-2">
                                        <small>
                                            <i class="bi bi-chat-text"></i>
                                            <strong>Commentaire:</strong> @etape.Commentaires
                                        </small>
                                    </div>
                                }

                                @if (etape.Statut == "En attente" && UtilisateurEstApprobateur(etape))
                                {
                                    <div class="mt-2">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" placeholder="Commentaire (optionnel)"
                                                   @bind="@CommentairesEtapes[etape.Id]" />
                                            <button class="btn btn-success" @onclick="() => ApprouverEtape(etape.Id)">
                                                <i class="bi bi-check-circle"></i> Approuver
                                            </button>
                                            <button class="btn btn-danger" @onclick="() => RejeterEtape(etape.Id)">
                                                <i class="bi bi-x-circle"></i> Rejeter
                                            </button>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            }
            else if (!WorkflowsActifs.Any() && ActifDonnees.Id != Guid.Empty)
            {
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Aucun workflow d'approbation n'est actuellement actif pour cet actif de données.
                </div>
            }
        }
        else
        {
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> Cet actif de données n'est pas marqué comme élément critique et ne nécessite pas d'approbation avant publication.
            </div>

            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="marquerElementCritique"
                           @bind="ActifDonnees.EstElementCritique" @bind:after="NotifyChange" />
                    <label class="form-check-label" for="marquerElementCritique">
                        Marquer comme élément critique
                    </label>
                </div>
                <small class="text-muted">
                    Les éléments critiques nécessitent une approbation avant publication.
                </small>
            </div>
        }
    </div>
</div>

@if (ShowAnnulationModal)
{
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Annuler le workflow d'approbation</h5>
                    <button type="button" class="btn-close" @onclick="() => ShowAnnulationModal = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir annuler ce workflow d'approbation ?</p>
                    <div class="mb-3">
                        <label for="commentaireAnnulation" class="form-label">Commentaire*</label>
                        <textarea class="form-control @(string.IsNullOrEmpty(CommentaireAnnulation) ? "is-invalid" : "")"
                                  id="commentaireAnnulation" rows="3" @bind="CommentaireAnnulation"></textarea>
                        @if (string.IsNullOrEmpty(CommentaireAnnulation))
                        {
                            <div class="invalid-feedback">
                                Un commentaire est obligatoire pour expliquer l'annulation.
                            </div>
                        }
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="() => ShowAnnulationModal = false">Annuler</button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmerAnnulationWorkflow"
                            disabled="@string.IsNullOrEmpty(CommentaireAnnulation)">
                        Confirmer l'annulation
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@if (ShowDetailsModal && WorkflowDetails != null)
{
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-diagram-3"></i> Détails du Workflow d'Approbation
                    </h5>
                    <button type="button" class="btn-close" @onclick="() => ShowDetailsModal = false" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Informations générales -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-info-circle"></i> Informations Générales</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>ID du Workflow:</strong><br>
                                    <code>@WorkflowDetails.Id</code>
                                </div>
                                <div class="col-md-6">
                                    <strong>Statut:</strong><br>
                                    <span class="badge @GetWorkflowStatusBadgeClass(WorkflowDetails.Statut)">
                                        @WorkflowDetails.Statut
                                    </span>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <strong>Initié par:</strong><br>
                                    @(WorkflowDetails.Initiateur?.NomComplet ?? "Inconnu")
                                </div>
                                <div class="col-md-6">
                                    <strong>Date de soumission:</strong><br>
                                    @(WorkflowDetails.DateSoumission?.ToString("dd/MM/yyyy HH:mm") ?? "Non définie")
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Progression -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-bar-chart"></i> Progression</h6>
                        </div>
                        <div class="card-body">
                            @{
                                var totalEtapesModal = WorkflowDetails.Etapes.Count();
                                var etapesApprouveesModal = WorkflowDetails.Etapes.Count(e => e.Statut == "Approuvé");
                                var progressPercentageModal = totalEtapesModal > 0 ? (etapesApprouveesModal * 100) / totalEtapesModal : 0;
                            }
                            <div class="progress mb-2" style="height: 25px;">
                                <div class="progress-bar bg-success" role="progressbar"
                                     style="width: @(progressPercentageModal)%"
                                     aria-valuenow="@progressPercentageModal" aria-valuemin="0" aria-valuemax="100">
                                    @etapesApprouveesModal/@totalEtapesModal étapes approuvées (@progressPercentageModal%)
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Historique des étapes -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-clock-history"></i> Historique des Étapes</h6>
                        </div>
                        <div class="card-body">
                            @foreach (var etape in WorkflowDetails.Etapes.OrderBy(e => e.Ordre))
                            {
                                <div class="border rounded p-3 mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">@etape.Nom</h6>
                                        <span class="badge @GetStatusBadgeClass(etape.Statut)">@etape.Statut</span>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <i class="bi bi-person"></i> <strong>Approbateur:</strong><br>
                                                @(etape.Approbateur?.NomComplet ?? "Non assigné")
                                            </small>
                                        </div>
                                        <div class="col-md-6">
                                            @if (etape.DateApprobation.HasValue)
                                            {
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar-check"></i> <strong>Date d'action:</strong><br>
                                                    @etape.DateApprobation?.ToString("dd/MM/yyyy HH:mm")
                                                </small>
                                            }
                                        </div>
                                    </div>
                                    @if (!string.IsNullOrEmpty(etape.Commentaires))
                                    {
                                        <div class="mt-2">
                                            <small class="text-muted"><strong>Commentaire:</strong></small>
                                            <div class="alert alert-light py-2 mt-1">
                                                <small>@etape.Commentaires</small>
                                            </div>
                                        </div>
                                    }
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="() => ShowDetailsModal = false">Fermer</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

<style>
    .timeline {
        position: relative;
        padding-left: 30px;
        margin-bottom: 20px;
    }

    .timeline-item {
        position: relative;
        padding-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -30px;
        width: 15px;
        height: 15px;
        border-radius: 50%;
    }

    .timeline-item:not(:last-child):after {
        content: '';
        position: absolute;
        left: -23px;
        top: 15px;
        height: calc(100% - 15px);
        width: 2px;
        background-color: #dee2e6;
    }

    .timeline-content {
        padding-bottom: 10px;
    }

    .timeline-title {
        margin-bottom: 5px;
    }
</style>

@code {
    [Parameter]
    public ActifDonnees ActifDonnees { get; set; } = new ActifDonnees();

    [Parameter]
    public EventCallback<ActifDonnees> OnChange { get; set; }

    private List<WorkflowApprobation> WorkflowsActifs { get; set; } = new List<WorkflowApprobation>();
    private WorkflowApprobation? SelectedWorkflow { get; set; }
    private bool ShowAnnulationModal { get; set; } = false;
    private bool ShowDetailsModal { get; set; } = false;
    private WorkflowApprobation? WorkflowDetails { get; set; }
    private Guid WorkflowIdAnnulation { get; set; }
    private string CommentaireAnnulation { get; set; } = string.Empty;
    private Dictionary<Guid, string> CommentairesEtapes { get; set; } = new Dictionary<Guid, string>();

    // Utilisateur actuel simulé (à remplacer par l'utilisateur réel)
    private Guid UtilisateurActuelId { get; set; } = Guid.Empty;
    private string UtilisateurActuelNom { get; set; } = "Utilisateur Actuel";

    protected override async Task OnInitializedAsync()
    {
        Console.WriteLine("=== BLAZOR FUNCIONA: ApprobationTab OnInitializedAsync ===");
        Console.WriteLine($"=== ActifDonnees.Id: {ActifDonnees?.Id} ===");

        // Obtenir un utilisateur valide pour les tests
        await ObtenirUtilisateurActuel();

        if (ActifDonnees.Id != Guid.Empty)
        {
            await LoadWorkflows();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ActifDonnees.Id != Guid.Empty)
        {
            await LoadWorkflows();
        }
    }

    private async Task LoadWorkflows()
    {
        try
        {
            var workflows = await WorkflowApprobationService.ObtenirParActifDonneesAsync(ActifDonnees.Id);
            WorkflowsActifs = workflows.Where(w => w.Statut != "Terminé" && w.Statut != "Annulé").ToList();

            // Initialiser les commentaires pour chaque étape
            CommentairesEtapes.Clear();
            foreach (var workflow in WorkflowsActifs)
            {
                foreach (var etape in workflow.Etapes)
                {
                    if (!CommentairesEtapes.ContainsKey(etape.Id))
                    {
                        CommentairesEtapes[etape.Id] = string.Empty;
                    }
                }
            }

            if (WorkflowsActifs.Any())
            {
                SelectedWorkflow = WorkflowsActifs.OrderByDescending(w => w.DateSoumission).First();
            }
            else
            {
                SelectedWorkflow = null;
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement des workflows d'approbation");
        }
    }

    private async Task SoumettreApprobation()
    {
        // Log directo a la consola que SIEMPRE funciona
        Console.WriteLine("=== BOUTON CLIQUÉ: SoumettreApprobation (ApprobationTab) ===");

        try
        {
            Console.WriteLine("1. Entrée dans la méthode SoumettreApprobation (ApprobationTab)");
            Console.WriteLine($"2. ActifDonnees.Id: {ActifDonnees.Id}");
            Console.WriteLine($"3. UtilisateurActuelId: {UtilisateurActuelId}");

            await WorkflowApprobationService.CreerWorkflowAsync(ActifDonnees.Id, UtilisateurActuelId);
            // Trouver le statut "En attente d'approbation"
            var statuts = await WorkflowApprobationService.ObtenirStatutsAsync();
            var statutEnAttente = statuts.FirstOrDefault(s => s.Nom == "En attente d'approbation");
            if (statutEnAttente != null)
            {
                ActifDonnees.StatutActifDonneesId = statutEnAttente.Id;
                ActifDonnees.StatutActifDonnees = statutEnAttente;
            }
            await NotifyChange();
            await LoadWorkflows();
            await ExceptionHandlingService.HandleSuccessAsync("L'actif de données a été soumis pour approbation.");

            Console.WriteLine("4. Fin de SoumettreApprobation (ApprobationTab) - SUCCESS");
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de la soumission pour approbation");
        }
    }

    private async Task AfficherDetailsWorkflow(Guid workflowId)
    {
        var previousSelected = SelectedWorkflow?.Id;
        SelectedWorkflow = WorkflowsActifs.FirstOrDefault(w => w.Id == workflowId);

        if (SelectedWorkflow != null)
        {
            Console.WriteLine($"Affichage des détails du workflow {workflowId}");

            // Notification visuelle du changement
            if (previousSelected != workflowId)
            {
                await ExceptionHandlingService.HandleSuccessAsync($"Workflow sélectionné: {SelectedWorkflow.Id.ToString().Substring(0, 8)}...");
            }

            StateHasChanged(); // Forcer le rafraîchissement de l'interface
        }
    }

    private void AnnulerWorkflow(Guid workflowId)
    {
        WorkflowIdAnnulation = workflowId;
        CommentaireAnnulation = string.Empty;
        ShowAnnulationModal = true;
    }

    private void AfficherModalDetails(Guid workflowId)
    {
        WorkflowDetails = WorkflowsActifs.FirstOrDefault(w => w.Id == workflowId);
        if (WorkflowDetails != null)
        {
            ShowDetailsModal = true;
            Console.WriteLine($"Ouverture du modal de détails pour le workflow {workflowId}");
        }
    }

    private async Task ConfirmerAnnulationWorkflow()
    {
        if (string.IsNullOrEmpty(CommentaireAnnulation))
            return;

        try
        {
            var resultat = await WorkflowApprobationService.AnnulerWorkflowAsync(WorkflowIdAnnulation, UtilisateurActuelId, CommentaireAnnulation);
            if (resultat)
            {
                await ExceptionHandlingService.HandleSuccessAsync("Le workflow d'approbation a été annulé.");
                ShowAnnulationModal = false;
                await LoadWorkflows();

                // Si c'était le dernier workflow actif, remettre l'actif en brouillon
                if (!WorkflowsActifs.Any())
                {
                    // Trouver le statut "Brouillon"
                    var statuts = await WorkflowApprobationService.ObtenirStatutsAsync();
                    var statutBrouillon = statuts.FirstOrDefault(s => s.Nom == "Brouillon");
                    if (statutBrouillon != null)
                    {
                        ActifDonnees.StatutActifDonneesId = statutBrouillon.Id;
                        ActifDonnees.StatutActifDonnees = statutBrouillon;
                    }
                    await NotifyChange();
                }
            }
            else
            {
                await ExceptionHandlingService.HandleErrorAsync("Impossible d'annuler le workflow d'approbation.");
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de l'annulation du workflow");
        }
    }

    private async Task ApprouverEtape(Guid etapeId)
    {
        try
        {
            // Trouver le workflow qui contient cette étape
            var workflow = WorkflowsActifs.FirstOrDefault(w => w.Etapes.Any(e => e.Id == etapeId));
            if (workflow == null)
            {
                await ExceptionHandlingService.HandleErrorAsync("Workflow non trouvé pour cette étape.");
                return;
            }

            // Obtenir le commentaire spécifique à cette étape
            var commentaire = CommentairesEtapes.TryGetValue(etapeId, out var comment) ? comment : string.Empty;

            var resultat = await WorkflowApprobationService.ApprouverEtapeAsync(workflow.Id, UtilisateurActuelId, commentaire);
            if (resultat)
            {
                await ExceptionHandlingService.HandleSuccessAsync("L'étape a été approuvée.");
                // Effacer le commentaire de cette étape
                CommentairesEtapes[etapeId] = string.Empty;
                await LoadWorkflows();

                // Vérifier si toutes les étapes sont approuvées
                if (SelectedWorkflow != null && SelectedWorkflow.Etapes.All(e => e.Statut == "Approuvé"))
                {
                    // Trouver le statut "Approuvé"
                    var statuts = await WorkflowApprobationService.ObtenirStatutsAsync();
                    var statutApprouve = statuts.FirstOrDefault(s => s.Nom == "Approuvé");
                    if (statutApprouve != null)
                    {
                        ActifDonnees.StatutActifDonneesId = statutApprouve.Id;
                        ActifDonnees.StatutActifDonnees = statutApprouve;
                    }
                    await NotifyChange();
                }
            }
            else
            {
                await ExceptionHandlingService.HandleErrorAsync("Impossible d'approuver l'étape.");
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de l'approbation de l'étape");
        }
    }

    private async Task RejeterEtape(Guid etapeId)
    {
        // Obtenir el comentario específico de esta etapa
        var commentaire = CommentairesEtapes.TryGetValue(etapeId, out var comment) ? comment : string.Empty;

        if (string.IsNullOrEmpty(commentaire))
        {
            await ExceptionHandlingService.HandleErrorAsync("Un commentaire est obligatoire pour rejeter une étape.");
            return;
        }

        try
        {
            // Trouver le workflow qui contient cette étape
            var workflow = WorkflowsActifs.FirstOrDefault(w => w.Etapes.Any(e => e.Id == etapeId));
            if (workflow == null)
            {
                await ExceptionHandlingService.HandleErrorAsync("Workflow non trouvé pour cette étape.");
                return;
            }

            var resultat = await WorkflowApprobationService.RejeterEtapeAsync(workflow.Id, UtilisateurActuelId, commentaire);
            if (resultat)
            {
                await ExceptionHandlingService.HandleSuccessAsync("L'étape a été rejetée.");
                // Effacer le commentaire de cette étape
                CommentairesEtapes[etapeId] = string.Empty;
                await LoadWorkflows();

                // Remettre l'actif en brouillon
                // Trouver le statut "Brouillon"
                var statuts = await WorkflowApprobationService.ObtenirStatutsAsync();
                var statutBrouillon = statuts.FirstOrDefault(s => s.Nom == "Brouillon");
                if (statutBrouillon != null)
                {
                    ActifDonnees.StatutActifDonneesId = statutBrouillon.Id;
                    ActifDonnees.StatutActifDonnees = statutBrouillon;
                }
                await NotifyChange();
            }
            else
            {
                await ExceptionHandlingService.HandleErrorAsync("Impossible de rejeter l'étape.");
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du rejet de l'étape");
        }
    }

    private bool UtilisateurEstApprobateur(EtapeWorkflowApprobation etape)
    {
        // Simuler que l'utilisateur actuel est l'approbateur
        // Dans une implémentation réelle, vérifier si l'ID de l'utilisateur actuel correspond à l'ID de l'approbateur
        return true;
    }

    private string GetStatusBadgeClass(string statut)
    {
        return statut switch
        {
            "Brouillon" => "bg-secondary",
            "En attente d'approbation" => "bg-info",
            "En attente" => "bg-info",
            "Approuvé" => "bg-success",
            "Rejeté" => "bg-danger",
            "Annulé" => "bg-warning",
            "Terminé" => "bg-success",
            _ => "bg-secondary"
        };
    }

    private string GetEtapeMarkerClass(string statut)
    {
        return statut switch
        {
            "En attente" => "bg-info",
            "Approuvé" => "bg-success",
            "Rejeté" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetWorkflowStatusBadgeClass(string statut)
    {
        return statut switch
        {
            "Approuvé" => "bg-success",
            "Rejeté" => "bg-danger",
            "Annulé" => "bg-secondary",
            "En attente" => "bg-primary",
            _ => "bg-info"
        };
    }

    private async Task NotifyChange()
    {
        await OnChange.InvokeAsync(ActifDonnees);
    }

    private async Task ObtenirUtilisateurActuel()
    {
        try
        {
            // Pour les tests, on va utiliser l'admin créé automatiquement
            // Dans une vraie application, on obtiendrait l'utilisateur connecté

            // Créer un utilisateur de test avec un ID fixe
            var utilisateurTestId = new Guid("11111111-1111-1111-1111-111111111111");
            UtilisateurActuelId = utilisateurTestId;
            UtilisateurActuelNom = "Utilisateur Test";

            Console.WriteLine($"Utilisateur actuel: {UtilisateurActuelId}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'obtention de l'utilisateur: {ex.Message}");
            // Fallback: utiliser un ID fixe
            UtilisateurActuelId = new Guid("11111111-1111-1111-1111-111111111111");
        }
    }
}
