@inherits LayoutComponentBase
@implements IDisposable
@using DataHubGatineau.Web.Components.Shared
@using DataHubGatineau.Web.Services.Auth
@using Microsoft.AspNetCore.Components.Authorization
@inject IAuthService AuthService
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager NavigationManager
@using DataHubGatineau.Web.Components.Shared.Notifications
@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Services
@inject IPreloadService PreloadService
@inject IConfirmationService ConfirmationService

<PreloadData />
<CacheStatus />

@if (IsAuthenticatedPage())
{
    <!-- Layout pour les pages authentifiées -->
    <div class="page">
        <div class="sidebar">
            <DataHubGatineau.Web.Components.Layout.NavMenu />
        </div>

        <main>
            <div class="top-row px-4 d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <button class="btn btn-link d-md-none" @onclick="ToggleSidebar">
                        <i class="bi bi-list fs-4 text-dark"></i>
                    </button>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Temporalement commenté pour éviter conflits -->
                    <!-- <NotificationBadge /> -->
                    <div class="dropdown ms-3">
                        <a href="#" class="d-flex align-items-center text-dark text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                            <img src="images/default-avatar.svg" alt="Avatar" width="32" height="32" class="rounded-circle me-2" />
                            <span class="d-none d-md-inline">@GetDisplayName()</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="dropdownUser1">
                            <li><a class="dropdown-item" href="/profil"><i class="bi bi-person me-2"></i>Profil</a></li>
                            <li><a class="dropdown-item" href="/parametres"><i class="bi bi-gear me-2"></i>Paramètres</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right me-2"></i>Déconnexion</a></li>
                        </ul>
                    </div>
                    <a href="https://learn.microsoft.com/fr-ca/aspnet/core/" target="_blank" class="btn btn-link d-none d-md-block">
                        <i class="bi bi-question-circle me-1"></i>
                        <span>Aide</span>
                    </a>
                </div>
            </div>

            <article class="content px-4">
                @Body
            </article>
        </main>
    </div>
}
else
{
    <!-- Layout simple pour les pages publiques (login, etc.) -->
    <div class="auth-layout">
        @Body
    </div>
}

<div id="blazor-error-ui" data-nosnippet>
    Une erreur inattendue s'est produite.
    <a href="." class="reload">Recharger</a>
    <span class="dismiss">×</span>
</div>

<!-- Modal de confirmation global -->
<ModalConfirmation @ref="_modalConfirmation" />

<!-- Container de notifications toast -->
<ToastContainer />

<script src="js/localStorage.js"></script>

@code {
    private ModalConfirmation? _modalConfirmation;
    private bool _sidebarVisible = true;
    private string _currentUserDisplay = "Utilisateur";
    private bool _isAuthenticated = false;

    protected override async Task OnInitializedAsync()
    {
        // S'abonner aux changements d'état d'authentification
        AuthStateProvider.AuthenticationStateChanged += OnAuthenticationStateChanged;

        // Initialiser le service d'authentification pour charger les tokens persistés
        await AuthService.InitializeAsync();

        // Forcer la notification du changement d'état d'authentification
        if (AuthStateProvider is CustomAuthStateProvider customProvider)
        {
            await customProvider.NotifyAuthenticationStateChangedAsync();
        }

        // Charger l'utilisateur actuel
        await LoadCurrentUser();
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender && _modalConfirmation != null)
        {
            ConfirmationService.EnregistrerModal(_modalConfirmation);
        }
    }

    private async void OnAuthenticationStateChanged(Task<AuthenticationState> task)
    {
        await LoadCurrentUser();
        await InvokeAsync(StateHasChanged);
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            // Verificar primero el estado de autenticación
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            _isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;

            if (!_isAuthenticated)
            {
                _currentUserDisplay = "Utilisateur";
                return;
            }

            var user = await AuthService.GetCurrentUserAsync();
            if (user != null)
            {
                // Priorité: FullName > FirstName LastName > Username > Email > "Utilisateur"
                if (!string.IsNullOrEmpty(user.FullName) && user.FullName.Trim() != "")
                {
                    _currentUserDisplay = user.FullName;
                }
                else if (!string.IsNullOrEmpty(user.FirstName) || !string.IsNullOrEmpty(user.LastName))
                {
                    _currentUserDisplay = $"{user.FirstName} {user.LastName}".Trim();
                }
                else if (!string.IsNullOrEmpty(user.Username))
                {
                    _currentUserDisplay = user.Username;
                }
                else if (!string.IsNullOrEmpty(user.Email))
                {
                    _currentUserDisplay = user.Email;
                }
                else
                {
                    _currentUserDisplay = "Utilisateur";
                }

                // Log para debug - mostrar todos los datos recibidos
                Console.WriteLine($"=== DATOS DEL USUARIO ===");
                Console.WriteLine($"Username: '{user.Username}'");
                Console.WriteLine($"Email: '{user.Email}'");
                Console.WriteLine($"FirstName: '{user.FirstName}'");
                Console.WriteLine($"LastName: '{user.LastName}'");
                Console.WriteLine($"FullName: '{user.FullName}'");
                Console.WriteLine($"Display final: '{_currentUserDisplay}'");
                Console.WriteLine($"========================");
            }
            else
            {
                _currentUserDisplay = "Utilisateur";
                Console.WriteLine("No se pudo cargar el usuario");
            }
        }
        catch
        {
            _currentUserDisplay = "Utilisateur";
            _isAuthenticated = false;
        }
    }

    private bool IsAuthenticatedPage()
    {
        var currentUri = NavigationManager.Uri;
        var relativePath = NavigationManager.ToBaseRelativePath(currentUri).ToLower();

        // Pages qui ne nécessitent pas d'authentification
        var publicPages = new[] { "login", "logout", "register", "forgot-password", "reset-password" };

        return _isAuthenticated && !publicPages.Any(page => relativePath.StartsWith(page));
    }

    private string GetDisplayName()
    {
        return _currentUserDisplay;
    }

    private void ToggleSidebar()
    {
        _sidebarVisible = !_sidebarVisible;
        // Utiliser JS pour basculer une classe sur l'élément .sidebar
    }

    public void Dispose()
    {
        AuthStateProvider.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
}

