@page "/actifs-donnees-details/{Id:guid}"
@page "/elements-donnees/{Id:guid}"
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Models.Policy
@using DataHubGatineau.Web.Services
@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Components.Shared
@inject IActifDonneesService ActifDonneesService

@inject IRegleQualiteService RegleQualiteService
@inject IConnexionSourceDonneesService ConnexionSourceDonneesService
@inject IDomaineGouvernanceService DomaineGouvernanceService
@inject IProduitDonneesService ProduitDonneesService

@inject IExceptionHandlingService ExceptionHandlingService
@inject NavigationManager NavigationManager

<PageTitle>Détails de l'Actif de Données - DataHub Gatineau</PageTitle>

@if (_actif == null)
{
    <p><em>Chargement...</em></p>
}
else
{
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1>@_actif.Nom</h1>
                <div>
                    <a href="/actifs-donnees/modifier/@Id" class="btn btn-primary me-2">
                        <i class="bi bi-pencil"></i> Modifier
                    </a>
                    <button class="btn btn-danger" @onclick="ConfirmerSuppression">
                        <i class="bi bi-trash"></i> Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Informations générales</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-file-text text-primary me-2"></i> Description
                        </div>
                        <div class="col-md-8">@_actif.Description</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-tag-fill text-primary me-2"></i> Type
                        </div>
                        <div class="col-md-8">@(_actif.TypeActifDonnees?.Nom ?? _actif.Type ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-file-earmark text-primary me-2"></i> Format
                        </div>
                        <div class="col-md-8">@(_actif.FormatActifDonnees?.Nom ?? _actif.Format ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-database text-primary me-2"></i> Source
                        </div>
                        <div class="col-md-8">@(_actif.SourceActifDonnees?.Nom ?? _actif.Source ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-person text-primary me-2"></i> Propriétaire
                        </div>
                        <div class="col-md-8">@(_actif.Proprietaire ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-shield-lock text-primary me-2"></i> Classification
                        </div>
                        <div class="col-md-8">
                            <span class="badge @GetClassificationBadgeClass(_actif.ClassificationSensibilite)">
                                @_actif.ClassificationSensibilite
                            </span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-calendar-check text-primary me-2"></i> Dernière mise à jour
                        </div>
                        <div class="col-md-8">@(_actif.DateDerniereMiseAJour?.ToString("dd/MM/yyyy HH:mm") ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-arrow-repeat text-primary me-2"></i> Fréquence de mise à jour
                        </div>
                        <div class="col-md-8">@(_actif.FrequenceMiseAJour?.Nom ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-folder text-primary me-2"></i> Chemin d'accès
                        </div>
                        <div class="col-md-8">@(_actif.CheminAcces ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-diagram-3 text-primary me-2"></i> Domaine de gouvernance
                        </div>
                        <div class="col-md-8">@(_actif.DomaineGouvernance?.Nom ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-link text-primary me-2"></i> Connexion à la source de données
                        </div>
                        <div class="col-md-8">@(_actif.ConnexionSourceDonnees?.Nom ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-exclamation-triangle text-primary me-2"></i> Élément critique
                        </div>
                        <div class="col-md-8">
                            @if (_actif.EstElementCritique)
                            {
                                <span class="badge bg-danger">Oui</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Non</span>
                            }
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-flag text-primary me-2"></i> Statut
                        </div>
                        <div class="col-md-8">
                            @if (_actif.StatutActifDonnees != null)
                            {
                                <span class="badge @GetStatutBadgeClass(_actif.StatutActifDonnees.Nom)">
                                    @_actif.StatutActifDonnees.Nom
                                </span>
                            }
                            else if (!string.IsNullOrEmpty(_actif.Statut))
                            {
                                <span class="badge @GetStatutBadgeClass(_actif.Statut)">
                                    @_actif.Statut
                                </span>
                            }
                            else
                            {
                                <span>-</span>
                            }
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-calendar-plus text-primary me-2"></i> Date de création
                        </div>
                        <div class="col-md-8">@_actif.DateCreation.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-calendar-check text-primary me-2"></i> Dernière modification
                        </div>
                        <div class="col-md-8">@_actif.DateModification.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-person-plus text-primary me-2"></i> Créé par
                        </div>
                        <div class="col-md-8">@(_actif.CreePar ?? "Système")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-person-gear text-primary me-2"></i> Modifié par
                        </div>
                        <div class="col-md-8">@(_actif.ModifiePar ?? "Système")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Score de qualité</h5>
                </div>
                <div class="card-body text-center">
                    <div class="display-1 fw-bold mb-3 @GetScoreColorClass(_scoreGlobal)">@_scoreGlobal%</div>
                    <div class="progress mb-3" style="height: 10px;">
                        <div class="progress-bar @GetScoreProgressBarClass(_scoreGlobal)" role="progressbar" style="width: @_scoreGlobal%;" aria-valuenow="@_scoreGlobal" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <p class="text-muted">Basé sur @(_resultatsQualite?.Count() ?? 0) règles de qualité</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Métadonnées usando el componente MetadonneesTab -->
    <div class="row mb-4">
        <div class="col-md-12">
            @if (_actif != null)
            {
                <MetadonneesTab ActifDonnees="_actif" />
            }
        </div>
    </div>

    <!-- Risques de l'actif -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">
                        <i class="bi bi-shield-exclamation text-danger me-2"></i>
                        Gestion des Risques
                    </h5>
                </div>
                <div class="card-body">
                    @if (_actif != null)
                    {
                        <RisquesActifTab ActifDonnees="_actif" />
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Produits de données</h5>
                </div>
                <div class="card-body">
                    @if (_produitsDonnees == null || !_produitsDonnees.Any())
                    {
                        <p>Aucun produit de données associé à cet actif.</p>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Description</th>
                                        <th>Domaine</th>
                                        <th>Propriétaire</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var produit in _produitsDonnees)
                                    {
                                        <tr>
                                            <td>
                                                <a href="/produits-donnees-details/@produit.Id" class="text-decoration-none fw-semibold">
                                                    @produit.Nom
                                                </a>
                                            </td>
                                            <td>@produit.Description</td>
                                            <td>@produit.DomaineGouvernanceNom</td>
                                            <td>@produit.Proprietaire</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>
                        <i class="bi bi-file-text text-primary me-2"></i>
                        Politiques associées
                    </h5>
                </div>
                <div class="card-body">
                    @if (_politiquesAssociees == null)
                    {
                        <div class="d-flex justify-content-center">
                            <div class="spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                    }
                    else if (!_politiquesAssociees.Any())
                    {
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            Aucune politique associée à cet actif de données.
                        </div>
                    }
                    else
                    {
                        <div class="list-group">
                            @foreach (var politique in _politiquesAssociees)
                            {
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="/politiques/@politique.Id" class="text-decoration-none">
                                                @politique.Titre
                                            </a>
                                        </h6>
                                        <p class="mb-1 text-muted small">@(politique.Description ?? "Aucune description")</p>
                                        <div class="d-flex gap-2 align-items-center">
                                            <small class="text-muted">
                                                <i class="bi bi-tag me-1"></i>
                                                Catégorie: @(politique.Categorie ?? "Non définie")
                                            </small>
                                            <small class="text-muted">
                                                <i class="bi bi-calendar me-1"></i>
                                                Version: @(politique.Version ?? "1.0")
                                            </small>
                                            @if (politique.DateEntreeVigueur.HasValue)
                                            {
                                                <small class="text-muted">
                                                    <i class="bi bi-calendar-check me-1"></i>
                                                    En vigueur: @politique.DateEntreeVigueur.Value.ToString("dd/MM/yyyy")
                                                </small>
                                            }
                                        </div>
                                    </div>
                                    <div class="d-flex flex-column align-items-end gap-1">
                                        <span class="badge @GetStatutPolitiqueBadgeClass(politique.Statut)">
                                            @(politique.Statut ?? "Brouillon")
                                        </span>
                                        @if (politique.EstActive)
                                        {
                                            <span class="badge bg-success">Active</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">Inactive</span>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Règles de qualité</h5>
                    <a href="/qualite-donnees/regles/ajouter" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus-circle"></i> Ajouter une règle
                    </a>
                </div>
                <div class="card-body">
                    @if (_reglesQualite == null || !_reglesQualite.Any())
                    {
                        <p>Aucune règle de qualité trouvée.</p>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Description</th>
                                        <th>Type</th>
                                        <th>Seuil</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var regle in _reglesQualite)
                                    {
                                        <tr>
                                            <td>@regle.Nom</td>
                                            <td>@regle.Description</td>
                                            <td>@regle.Type</td>
                                            <td>@regle.Seuil</td>
                                            <td>
                                                <a href="/qualite-donnees/regles/modifier/@regle.Id" class="btn btn-warning btn-sm">Modifier</a>
                                                <button class="btn btn-danger btn-sm" @onclick="() => SupprimerRegleQualite(regle.Id)">Supprimer</button>
                                                <button class="btn btn-primary btn-sm" @onclick="() => ExecuterRegleQualite(regle.Id)">Exécuter</button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

<div class="modal fade @(_showDeleteConfirmation ? "show" : "")" tabindex="-1" style="display: @(_showDeleteConfirmation ? "block" : "none")">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" @onclick="AnnulerSuppression"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer cet actif de données? Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @onclick="AnnulerSuppression">Annuler</button>
                <button type="button" class="btn btn-danger" @onclick="SupprimerActif">Supprimer</button>
            </div>
        </div>
    </div>
</div>



@if (_showDeleteConfirmation)
{
    <div class="modal-backdrop fade show"></div>
}

@code {
    [Parameter]
    public Guid Id { get; set; }

    private ActifDonnees? _actif;

    private IEnumerable<RegleQualite>? _reglesQualite;
    private IEnumerable<ResultatRegleQualite>? _resultatsQualite;
    private IEnumerable<ProduitDonnees>? _produitsDonnees;
    private IEnumerable<Politique>? _politiquesAssociees;

    private double _scoreGlobal = 0;
    private bool _showDeleteConfirmation = false;

    private bool _loading = true;


    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    protected override async Task OnParametersSetAsync()
    {
        // Recargar los datos cuando cambian los parámetros (por ejemplo, después de una edición)
        await ChargerDonnees();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Recargar los datos después de que el componente se haya renderizado
            try
            {
                // Esperar un momento para asegurarse de que el componente esté completamente renderizado
                await Task.Delay(500);

                // Recargar los datos
                await ChargerDonnees();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error en OnAfterRenderAsync: {ex.Message}");
            }
        }
    }

    private async Task ChargerDonnees()
    {
        _loading = true;
        try
        {
            // Obtenir l'actif de données
            _actif = await ActifDonneesService.ObtenirParIdAsync(Id);

            if (_actif == null)
            {
                await ExceptionHandlingService.HandleErrorAsync("Actif de données non trouvé");
                return;
            }

            // Logging pour diagnostiquer le problème des métadonnées
            Console.WriteLine($"🔍 ActifDonneesDetails - Actif chargé: {_actif.Nom}");
            Console.WriteLine($"🔍 ActifDonneesDetails - Type actif: {_actif.TypeActifDonnees?.Nom ?? "NULL"}");
            Console.WriteLine($"🔍 ActifDonneesDetails - ID actif: {_actif.Id}");
            Console.WriteLine($"🔍 ActifDonneesDetails - Domaine gouvernance: {_actif.DomaineGouvernance?.Nom ?? "NULL"}");
            Console.WriteLine($"🔍 ActifDonneesDetails - DomaineGouvernanceId: {_actif.DomaineGouvernanceId}");
            Console.WriteLine($"🔍 ActifDonneesDetails - Connexion source: {_actif.ConnexionSourceDonnees?.Nom ?? "NULL"}");
            Console.WriteLine($"🔍 ActifDonneesDetails - ConnexionSourceDonneesId: {_actif.ConnexionSourceDonneesId}");

            // Charger les données de base de manière sécurisée
            await ChargerDonneesSecurisees();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement des données: {ex.Message}");
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement des détails de l'actif");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task ChargerDonneesSecurisees()
    {
        if (_actif == null) return;

        try
        {
            // Charger les règles de qualité
            try
            {
                _reglesQualite = await RegleQualiteService.ObtenirParActifDonneesAsync(Id);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du chargement des règles de qualité: {ex.Message}");
                _reglesQualite = new List<RegleQualite>();
            }

            // Charger les résultats de qualité
            try
            {
                _resultatsQualite = await RegleQualiteService.ObtenirResultatsParActifDonneesAsync(Id);

                if (_resultatsQualite != null && _resultatsQualite.Any())
                {
                    _scoreGlobal = Math.Round(_resultatsQualite.Average(r => r.ValeurMesuree), 1);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du chargement des résultats de qualité: {ex.Message}");
                _resultatsQualite = new List<ResultatRegleQualite>();
            }

            // Charger les produits de données
            try
            {
                _produitsDonnees = await ProduitDonneesService.ObtenirParActifDonneesAsync(Id);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du chargement des produits de données: {ex.Message}");
                _produitsDonnees = new List<ProduitDonnees>();
            }

            // Charger les politiques associées
            try
            {
                _politiquesAssociees = await ActifDonneesService.ObtenirPolitiquesAssocieesAsync(Id);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du chargement des politiques associées: {ex.Message}");
                _politiquesAssociees = new List<Politique>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des données sécurisées: {ex.Message}");
        }
    }

    private void ConfirmerSuppression()
    {
        _showDeleteConfirmation = true;
    }

    private void AnnulerSuppression()
    {
        _showDeleteConfirmation = false;
    }

    private async Task SupprimerActif()
    {
        try
        {
            var resultat = await ActifDonneesService.SupprimerAsync(Id);
            if (resultat)
            {
                await ExceptionHandlingService.HandleSuccessAsync("L'actif de données a été supprimé avec succès.");
                NavigationManager.NavigateTo("/actifs-donnees");
            }
            else
            {
                await ExceptionHandlingService.HandleErrorAsync("La suppression de l'actif de données a échoué.");
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de la suppression de l'actif de données");
        }
    }

    private async Task SupprimerRegleQualite(Guid regleId)
    {
        if (await RegleQualiteService.SupprimerAsync(regleId))
        {
            await ChargerDonnees();
        }
    }

    private async Task ExecuterRegleQualite(Guid regleId)
    {
        await RegleQualiteService.ExecuterRegleAsync(regleId);
        await ChargerDonnees();
    }

    private string GetClassificationBadgeClass(ClassificationSensibilite classification)
    {
        return classification switch
        {
            ClassificationSensibilite.Public => "bg-success text-white",
            ClassificationSensibilite.Interne => "bg-info text-white",
            ClassificationSensibilite.Confidentiel => "bg-warning text-dark",
            ClassificationSensibilite.Critique => "bg-danger text-white",
            ClassificationSensibilite.Restreint => "bg-danger text-white",
            _ => "bg-secondary text-white"
        };
    }

    private string GetScoreColorClass(double score)
    {
        return score switch
        {
            >= 90 => "text-success",
            >= 70 => "text-info",
            >= 50 => "text-warning",
            _ => "text-danger"
        };
    }

    private string GetScoreProgressBarClass(double score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 70 => "bg-info",
            >= 50 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private string GetStatutBadgeClass(string? statut)
    {
        if (string.IsNullOrEmpty(statut))
            return "bg-secondary text-white";

        // Corriger l'encodage avant de faire la comparaison
        statut = CorrigerEncodage(statut);

        return statut.ToLower() switch
        {
            "brouillon" => "bg-secondary text-white",
            "en révision" => "bg-info text-white",
            "actif" => "bg-success text-white",
            "déprécié" => "bg-warning text-dark",
            "archivé" => "bg-dark text-white",
            "supprimé" => "bg-danger text-white",
            _ => "bg-secondary text-white"
        };
    }

    private string CorrigerEncodage(string? texte)
    {
        if (string.IsNullOrEmpty(texte))
            return string.Empty;

        // Remplacer les caractères mal encodés
        string resultat = texte
            .Replace("Ã©", "é")
            .Replace("Ã¨", "è")
            .Replace("Ã ", "à")
            .Replace("Ã®", "î")
            .Replace("Ã´", "ô")
            .Replace("Ã»", "û")
            .Replace("Ã§", "ç")
            .Replace("Ã‰", "É")
            .Replace("Ãª", "ê")
            .Replace("Ã¢", "â")
            .Replace("Ã¹", "ù")
            .Replace("Ã¯", "ï")
            .Replace("Ã«", "ë")
            .Replace("Ã¶", "ö")
            .Replace("Ã¼", "ü")
            .Replace("rÃ©el", "réel")
            .Replace("donnÃ©es", "données")
            .Replace("Base de donnÃ©es", "Base de données")
            .Replace("ArchivÃ©", "Archivé")
            .Replace("DÃ©prÃ©ciÃ©", "Déprécié")
            .Replace("En rÃ©vision", "En révision");

        return resultat;
    }

    private string GetStatutPolitiqueBadgeClass(string? statut)
    {
        if (string.IsNullOrEmpty(statut))
            return "bg-secondary text-white";

        return statut.ToLower() switch
        {
            "brouillon" => "bg-secondary text-white",
            "en révision" => "bg-info text-white",
            "approuvé" => "bg-primary text-white",
            "en vigueur" => "bg-success text-white",
            "expiré" => "bg-warning text-dark",
            "archivé" => "bg-dark text-white",
            "rejeté" => "bg-danger text-white",
            _ => "bg-secondary text-white"
        };
    }
}
