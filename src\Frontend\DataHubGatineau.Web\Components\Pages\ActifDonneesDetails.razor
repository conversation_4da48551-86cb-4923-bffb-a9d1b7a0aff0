@page "/actifs-donnees-details/{Id:guid}"
@page "/elements-donnees/{Id:guid}"
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services
@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Components.Shared
@inject IActifDonneesService ActifDonneesService

@inject IRegleQualiteService RegleQualiteService
@inject IConnexionSourceDonneesService ConnexionSourceDonneesService
@inject IDomaineGouvernanceService DomaineGouvernanceService
@inject IProduitDonneesService ProduitDonneesService

@inject IExceptionHandlingService ExceptionHandlingService
@inject NavigationManager NavigationManager

<PageTitle>Détails de l'Actif de Données - DataHub Gatineau</PageTitle>

@if (_actif == null)
{
    <p><em>Chargement...</em></p>
}
else
{
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1>@_actif.Nom</h1>
                <div>
                    <a href="/actifs-donnees/modifier/@Id" class="btn btn-primary me-2">
                        <i class="bi bi-pencil"></i> Modifier
                    </a>
                    <button class="btn btn-danger" @onclick="ConfirmerSuppression">
                        <i class="bi bi-trash"></i> Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Informations générales</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-file-text text-primary me-2"></i> Description
                        </div>
                        <div class="col-md-8">@_actif.Description</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-tag-fill text-primary me-2"></i> Type
                        </div>
                        <div class="col-md-8">@(_actif.TypeActifDonnees?.Nom ?? _actif.Type ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-file-earmark text-primary me-2"></i> Format
                        </div>
                        <div class="col-md-8">@(_actif.FormatActifDonnees?.Nom ?? _actif.Format ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-database text-primary me-2"></i> Source
                        </div>
                        <div class="col-md-8">@(_actif.SourceActifDonnees?.Nom ?? _actif.Source ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-person text-primary me-2"></i> Propriétaire
                        </div>
                        <div class="col-md-8">@(_actif.Proprietaire ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-shield-lock text-primary me-2"></i> Classification
                        </div>
                        <div class="col-md-8">
                            <span class="badge @GetClassificationBadgeClass(_actif.ClassificationSensibilite)">
                                @_actif.ClassificationSensibilite
                            </span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-calendar-check text-primary me-2"></i> Dernière mise à jour
                        </div>
                        <div class="col-md-8">@(_actif.DateDerniereMiseAJour?.ToString("dd/MM/yyyy HH:mm") ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-arrow-repeat text-primary me-2"></i> Fréquence de mise à jour
                        </div>
                        <div class="col-md-8">@(_actif.FrequenceMiseAJour?.Nom ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-folder text-primary me-2"></i> Chemin d'accès
                        </div>
                        <div class="col-md-8">@(_actif.CheminAcces ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-diagram-3 text-primary me-2"></i> Domaine de gouvernance
                        </div>
                        <div class="col-md-8">@(_actif.DomaineGouvernance?.Nom ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-link text-primary me-2"></i> Connexion à la source de données
                        </div>
                        <div class="col-md-8">@(_actif.ConnexionSourceDonnees?.Nom ?? "-")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-exclamation-triangle text-primary me-2"></i> Élément critique
                        </div>
                        <div class="col-md-8">
                            @if (_actif.EstElementCritique)
                            {
                                <span class="badge bg-danger">Oui</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Non</span>
                            }
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-flag text-primary me-2"></i> Statut
                        </div>
                        <div class="col-md-8">
                            @if (_actif.StatutActifDonnees != null)
                            {
                                <span class="badge @GetStatutBadgeClass(_actif.StatutActifDonnees.Nom)">
                                    @_actif.StatutActifDonnees.Nom
                                </span>
                            }
                            else if (!string.IsNullOrEmpty(_actif.Statut))
                            {
                                <span class="badge @GetStatutBadgeClass(_actif.Statut)">
                                    @_actif.Statut
                                </span>
                            }
                            else
                            {
                                <span>-</span>
                            }
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-calendar-plus text-primary me-2"></i> Date de création
                        </div>
                        <div class="col-md-8">@_actif.DateCreation.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-calendar-check text-primary me-2"></i> Dernière modification
                        </div>
                        <div class="col-md-8">@_actif.DateModification.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-person-plus text-primary me-2"></i> Créé par
                        </div>
                        <div class="col-md-8">@(_actif.CreePar ?? "Système")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 fw-bold">
                            <i class="bi bi-person-gear text-primary me-2"></i> Modifié par
                        </div>
                        <div class="col-md-8">@(_actif.ModifiePar ?? "Système")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Score de qualité</h5>
                </div>
                <div class="card-body text-center">
                    <div class="display-1 fw-bold mb-3 @GetScoreColorClass(_scoreGlobal)">@_scoreGlobal%</div>
                    <div class="progress mb-3" style="height: 10px;">
                        <div class="progress-bar @GetScoreProgressBarClass(_scoreGlobal)" role="progressbar" style="width: @_scoreGlobal%;" aria-valuenow="@_scoreGlobal" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <p class="text-muted">Basé sur @(_resultatsQualite?.Count() ?? 0) règles de qualité</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Métadonnées usando el componente MetadonneesTab -->
    <div class="row mb-4">
        <div class="col-md-12">
            @if (_actif != null)
            {
                <p>🔍 DEBUG: Actif cargado: @_actif.Nom, Tipo: @_actif.TypeActifDonnees?.Nom</p>
                <MetadonneesTab ActifDonnees="_actif" />
            }
            else
            {
                <p>⚠️ DEBUG: Actif es null</p>
            }
        </div>
    </div>

    <!-- Risques de l'actif -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">
                        <i class="bi bi-shield-exclamation text-danger me-2"></i>
                        Gestion des Risques
                    </h5>
                </div>
                <div class="card-body">
                    @if (_actif != null)
                    {
                        <RisquesActifTab ActifDonnees="_actif" />
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Produits de données</h5>
                </div>
                <div class="card-body">
                    @if (_produitsDonnees == null || !_produitsDonnees.Any())
                    {
                        <p>Aucun produit de données associé à cet actif.</p>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Description</th>
                                        <th>Domaine</th>
                                        <th>Propriétaire</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var produit in _produitsDonnees)
                                    {
                                        <tr>
                                            <td>
                                                <a href="/produits-donnees-details/@produit.Id" class="text-decoration-none fw-semibold">
                                                    @produit.Nom
                                                </a>
                                            </td>
                                            <td>@produit.Description</td>
                                            <td>@produit.DomaineGouvernanceNom</td>
                                            <td>@produit.Proprietaire</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Règles de qualité</h5>
                    <a href="/qualite-donnees/regles/ajouter" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus-circle"></i> Ajouter une règle
                    </a>
                </div>
                <div class="card-body">
                    @if (_reglesQualite == null || !_reglesQualite.Any())
                    {
                        <p>Aucune règle de qualité trouvée.</p>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Description</th>
                                        <th>Type</th>
                                        <th>Seuil</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var regle in _reglesQualite)
                                    {
                                        <tr>
                                            <td>@regle.Nom</td>
                                            <td>@regle.Description</td>
                                            <td>@regle.Type</td>
                                            <td>@regle.Seuil</td>
                                            <td>
                                                <a href="/qualite-donnees/regles/modifier/@regle.Id" class="btn btn-warning btn-sm">Modifier</a>
                                                <button class="btn btn-danger btn-sm" @onclick="() => SupprimerRegleQualite(regle.Id)">Supprimer</button>
                                                <button class="btn btn-primary btn-sm" @onclick="() => ExecuterRegleQualite(regle.Id)">Exécuter</button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

<div class="modal fade @(_showDeleteConfirmation ? "show" : "")" tabindex="-1" style="display: @(_showDeleteConfirmation ? "block" : "none")">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" @onclick="AnnulerSuppression"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer cet actif de données? Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @onclick="AnnulerSuppression">Annuler</button>
                <button type="button" class="btn btn-danger" @onclick="SupprimerActif">Supprimer</button>
            </div>
        </div>
    </div>
</div>



@if (_showDeleteConfirmation)
{
    <div class="modal-backdrop fade show"></div>
}

@code {
    [Parameter]
    public Guid Id { get; set; }

    private ActifDonnees? _actif;

    private IEnumerable<RegleQualite>? _reglesQualite;
    private IEnumerable<ResultatRegleQualite>? _resultatsQualite;
    private IEnumerable<ProduitDonnees>? _produitsDonnees;

    private double _scoreGlobal = 0;
    private bool _showDeleteConfirmation = false;

    private bool _loading = true;


    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    protected override async Task OnParametersSetAsync()
    {
        // Recargar los datos cuando cambian los parámetros (por ejemplo, después de una edición)
        await ChargerDonnees();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Recargar los datos después de que el componente se haya renderizado
            try
            {
                // Esperar un momento para asegurarse de que el componente esté completamente renderizado
                await Task.Delay(500);

                // Recargar los datos
                await ChargerDonnees();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error en OnAfterRenderAsync: {ex.Message}");
            }
        }
    }

    private async Task ChargerDonnees()
    {
        _loading = true;
        try
        {
            // Forzar una recarga completa desde el servidor
            Console.WriteLine($"Cargando datos del activo {Id}");

            // Obtener el activo de datos
            _actif = await ActifDonneesService.ObtenirParIdAsync(Id);
            Console.WriteLine($"Actif chargé: Id={_actif?.Id}, Nom={_actif?.Nom}");
            Console.WriteLine($"TypeActifDonneesId={_actif?.TypeActifDonneesId}, TypeActifDonnees={_actif?.TypeActifDonnees?.Nom}");
            Console.WriteLine($"FormatActifDonneesId={_actif?.FormatActifDonneesId}, FormatActifDonnees={_actif?.FormatActifDonnees?.Nom}");
            Console.WriteLine($"SourceActifDonneesId={_actif?.SourceActifDonneesId}, SourceActifDonnees={_actif?.SourceActifDonnees?.Nom}");
            Console.WriteLine($"FrequenceMiseAJourId={_actif?.FrequenceMiseAJourId}, FrequenceMiseAJour={_actif?.FrequenceMiseAJour?.Nom}");
            Console.WriteLine($"StatutActifDonneesId={_actif?.StatutActifDonneesId}, StatutActifDonnees={_actif?.StatutActifDonnees?.Nom}");
            Console.WriteLine($"DomaineGouvernanceId={_actif?.DomaineGouvernanceId}, DomaineGouvernance={_actif?.DomaineGouvernance?.Nom}");
            Console.WriteLine($"ConnexionSourceDonneesId={_actif?.ConnexionSourceDonneesId}, ConnexionSourceDonnees={_actif?.ConnexionSourceDonnees?.Nom}");
            Console.WriteLine($"EstElementCritique={_actif?.EstElementCritique}");

            // Afficher les valeurs brutes reçues du serveur
            Console.WriteLine($"Type brut: {_actif?.Type}");
            Console.WriteLine($"Format brut: {_actif?.Format}");
            Console.WriteLine($"Source brute: {_actif?.Source}");
            Console.WriteLine($"Fréquence brute: {_actif?.FrequenceMiseAJour?.Nom}");
            Console.WriteLine($"Statut brut: {_actif?.Statut}");

            if (_actif != null)
            {
                // Cargar todos los datos de referencia de una vez
                var types = await ActifDonneesService.ObtenirTypesAsync();
                var formats = await ActifDonneesService.ObtenirFormatsAsync();
                var sources = await ActifDonneesService.ObtenirSourcesAsync();
                var frequences = await ActifDonneesService.ObtenirFrequencesAsync();
                var statuts = await ActifDonneesService.ObtenirStatutsAsync();

                Console.WriteLine($"Datos de referencia cargados: Types={types.Count()}, Formats={formats.Count()}, Sources={sources.Count()}, Frequences={frequences.Count()}, Statuts={statuts.Count()}");

                // Imprimir los datos de referencia para depuración
                foreach (var type in types)
                {
                    Console.WriteLine($"Type disponible: Id={type.Id}, Nom={type.Nom}");
                }

                foreach (var format in formats)
                {
                    Console.WriteLine($"Format disponible: Id={format.Id}, Nom={format.Nom}");
                }

                foreach (var source in sources)
                {
                    Console.WriteLine($"Source disponible: Id={source.Id}, Nom={source.Nom}");
                }

                foreach (var frequence in frequences)
                {
                    Console.WriteLine($"Frequence disponible: Id={frequence.Id}, Nom={frequence.Nom}");
                }

                foreach (var statut in statuts)
                {
                    Console.WriteLine($"Statut disponible: Id={statut.Id}, Nom={statut.Nom}");
                }

                // Cargar explícitamente los datos de referencia
                if (_actif.TypeActifDonneesId.HasValue)
                {
                    _actif.TypeActifDonnees = types.FirstOrDefault(t => t.Id == _actif.TypeActifDonneesId);
                    Console.WriteLine($"Type chargé explicitement: {_actif.TypeActifDonnees?.Nom}");
                }
                else if (!string.IsNullOrEmpty(_actif.Type))
                {
                    var type = types.FirstOrDefault(t => t.Nom.Equals(_actif.Type, StringComparison.OrdinalIgnoreCase));
                    if (type != null)
                    {
                        _actif.TypeActifDonnees = type;
                        _actif.TypeActifDonneesId = type.Id;
                        Console.WriteLine($"Type trouvé par nom: {type.Nom}");
                    }
                }

                if (_actif.FormatActifDonneesId.HasValue)
                {
                    _actif.FormatActifDonnees = formats.FirstOrDefault(f => f.Id == _actif.FormatActifDonneesId);
                    Console.WriteLine($"Format chargé explicitement: {_actif.FormatActifDonnees?.Nom}");
                }
                else if (!string.IsNullOrEmpty(_actif.Format))
                {
                    var format = formats.FirstOrDefault(f => f.Nom.Equals(_actif.Format, StringComparison.OrdinalIgnoreCase));
                    if (format != null)
                    {
                        _actif.FormatActifDonnees = format;
                        _actif.FormatActifDonneesId = format.Id;
                        Console.WriteLine($"Format trouvé par nom: {format.Nom}");
                    }
                }

                if (_actif.SourceActifDonneesId.HasValue)
                {
                    _actif.SourceActifDonnees = sources.FirstOrDefault(s => s.Id == _actif.SourceActifDonneesId);
                    Console.WriteLine($"Source chargée explicitement: {_actif.SourceActifDonnees?.Nom}");
                }
                else if (!string.IsNullOrEmpty(_actif.Source))
                {
                    var source = sources.FirstOrDefault(s => s.Nom.Equals(_actif.Source, StringComparison.OrdinalIgnoreCase));
                    if (source != null)
                    {
                        _actif.SourceActifDonnees = source;
                        _actif.SourceActifDonneesId = source.Id;
                        Console.WriteLine($"Source trouvée par nom: {source.Nom}");
                    }
                }

                if (_actif.FrequenceMiseAJourId.HasValue)
                {
                    _actif.FrequenceMiseAJour = frequences.FirstOrDefault(f => f.Id == _actif.FrequenceMiseAJourId);
                    Console.WriteLine($"Fréquence chargée explicitement: {_actif.FrequenceMiseAJour?.Nom}");
                }
                else if (!string.IsNullOrEmpty(_actif.FrequenceMiseAJour?.Nom))
                {
                    var frequence = frequences.FirstOrDefault(f => f.Nom.Equals(_actif.FrequenceMiseAJour.Nom, StringComparison.OrdinalIgnoreCase));
                    if (frequence != null)
                    {
                        _actif.FrequenceMiseAJour = frequence;
                        _actif.FrequenceMiseAJourId = frequence.Id;
                        Console.WriteLine($"Fréquence trouvée par nom: {frequence.Nom}");
                    }
                }

                if (_actif.StatutActifDonneesId.HasValue)
                {
                    _actif.StatutActifDonnees = statuts.FirstOrDefault(s => s.Id == _actif.StatutActifDonneesId);
                    Console.WriteLine($"Statut chargé explicitement: {_actif.StatutActifDonnees?.Nom}");
                }
                else if (!string.IsNullOrEmpty(_actif.Statut))
                {
                    var statut = statuts.FirstOrDefault(s => s.Nom.Equals(_actif.Statut, StringComparison.OrdinalIgnoreCase));
                    if (statut != null)
                    {
                        _actif.StatutActifDonnees = statut;
                        _actif.StatutActifDonneesId = statut.Id;
                        Console.WriteLine($"Statut trouvé par nom: {statut.Nom}");
                    }
                }

                // Charger les données de référence si elles ne sont pas déjà chargées
                if (_actif.DomaineGouvernanceId.HasValue && _actif.DomaineGouvernance == null)
                {
                    try
                    {
                        _actif.DomaineGouvernance = await DomaineGouvernanceService.ObtenirParIdAsync(_actif.DomaineGouvernanceId.Value);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur lors du chargement du domaine de gouvernance: {ex.Message}");
                    }
                }

                if (_actif.ConnexionSourceDonneesId.HasValue && _actif.ConnexionSourceDonnees == null)
                {
                    try
                    {
                        _actif.ConnexionSourceDonnees = await ConnexionSourceDonneesService.ObtenirParIdAsync(_actif.ConnexionSourceDonneesId.Value);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Erreur lors du chargement de la connexion à la source de données: {ex.Message}");
                    }
                }



                try {
                    _reglesQualite = await RegleQualiteService.ObtenirParActifDonneesAsync(Id);
                } catch (Exception ex) {
                    Console.WriteLine($"Erreur lors du chargement des règles de qualité: {ex.Message}");
                    _reglesQualite = new List<RegleQualite>();
                }

                try {
                    _resultatsQualite = await RegleQualiteService.ObtenirResultatsParActifDonneesAsync(Id);

                    // Calculer le score global
                    if (_resultatsQualite != null && _resultatsQualite.Any())
                    {
                        _scoreGlobal = Math.Round(_resultatsQualite
                            .Average(r => r.ValeurMesuree), 1);
                    }
                } catch (Exception ex) {
                    Console.WriteLine($"Erreur lors du chargement des résultats de qualité: {ex.Message}");
                    _resultatsQualite = new List<ResultatRegleQualite>();
                }

                // Cargar los productos de datos asociados
                try {
                    _produitsDonnees = await ProduitDonneesService.ObtenirParActifDonneesAsync(Id);
                    Console.WriteLine($"Produits de données chargés: {_produitsDonnees?.Count() ?? 0}");
                } catch (Exception ex) {
                    Console.WriteLine($"Erreur lors du chargement des produits de données: {ex.Message}");
                    _produitsDonnees = new List<ProduitDonnees>();
                }
            }
        }
        finally
        {
            _loading = false;
        }
    }

    private void ConfirmerSuppression()
    {
        _showDeleteConfirmation = true;
    }

    private void AnnulerSuppression()
    {
        _showDeleteConfirmation = false;
    }

    private async Task SupprimerActif()
    {
        try
        {
            var resultat = await ActifDonneesService.SupprimerAsync(Id);
            if (resultat)
            {
                await ExceptionHandlingService.HandleSuccessAsync("L'actif de données a été supprimé avec succès.");
                NavigationManager.NavigateTo("/actifs-donnees");
            }
            else
            {
                await ExceptionHandlingService.HandleErrorAsync("La suppression de l'actif de données a échoué.");
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de la suppression de l'actif de données");
        }
    }



    private async Task SupprimerRegleQualite(Guid regleId)
    {
        if (await RegleQualiteService.SupprimerAsync(regleId))
        {
            await ChargerDonnees();
        }
    }

    private async Task ExecuterRegleQualite(Guid regleId)
    {
        await RegleQualiteService.ExecuterRegleAsync(regleId);
        await ChargerDonnees();
    }

    private string GetClassificationBadgeClass(ClassificationSensibilite classification)
    {
        return classification switch
        {
            ClassificationSensibilite.Public => "bg-success text-white",
            ClassificationSensibilite.Interne => "bg-info text-white",
            ClassificationSensibilite.Confidentiel => "bg-warning text-dark",
            ClassificationSensibilite.Critique => "bg-danger text-white",
            ClassificationSensibilite.Restreint => "bg-danger text-white",
            _ => "bg-secondary text-white"
        };
    }

    private string GetScoreColorClass(double score)
    {
        return score switch
        {
            >= 90 => "text-success",
            >= 70 => "text-info",
            >= 50 => "text-warning",
            _ => "text-danger"
        };
    }

    private string GetScoreProgressBarClass(double score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 70 => "bg-info",
            >= 50 => "bg-warning",
            _ => "bg-danger"
        };
    }



    private string GetStatutBadgeClass(string? statut)
    {
        if (string.IsNullOrEmpty(statut))
            return "bg-secondary text-white";

        // Corriger l'encodage avant de faire la comparaison
        statut = CorrigerEncodage(statut);

        return statut.ToLower() switch
        {
            "brouillon" => "bg-secondary text-white",
            "en révision" => "bg-info text-white",
            "actif" => "bg-success text-white",
            "déprécié" => "bg-warning text-dark",
            "archivé" => "bg-dark text-white",
            "supprimé" => "bg-danger text-white",
            _ => "bg-secondary text-white"
        };
    }

    private string CorrigerEncodage(string? texte)
    {
        if (string.IsNullOrEmpty(texte))
            return string.Empty;

        // Remplacer les caractères mal encodés
        string resultat = texte
            .Replace("Ã©", "é")
            .Replace("Ã¨", "è")
            .Replace("Ã ", "à")
            .Replace("Ã®", "î")
            .Replace("Ã´", "ô")
            .Replace("Ã»", "û")
            .Replace("Ã§", "ç")
            .Replace("Ã‰", "É")
            .Replace("Ãª", "ê")
            .Replace("Ã¢", "â")
            .Replace("Ã¹", "ù")
            .Replace("Ã¯", "ï")
            .Replace("Ã«", "ë")
            .Replace("Ã¶", "ö")
            .Replace("Ã¼", "ü")
            .Replace("rÃ©el", "réel")
            .Replace("donnÃ©es", "données")
            .Replace("Base de donnÃ©es", "Base de données")
            .Replace("ArchivÃ©", "Archivé")
            .Replace("DÃ©prÃ©ciÃ©", "Déprécié")
            .Replace("En rÃ©vision", "En révision");

        return resultat;
    }
}
