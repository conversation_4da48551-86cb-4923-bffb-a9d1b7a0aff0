@page "/elements-donnees/ajouter"
@page "/elements-donnees/modifier/{Id:guid}"
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services
@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Services.Implementations
@using DataHubGatineau.Web.Components.Shared
@inject IActifDonneesService ActifDonneesService
@inject IDomaineGouvernanceService DomaineGouvernanceService
@inject IConnexionSourceDonneesService ConnexionSourceDonneesService
@inject ITypeActifDonneesService TypeActifDonneesService
@inject IFormatActifDonneesService FormatActifDonneesService
@inject ISourceActifDonneesService SourceActifDonneesService
@inject IFrequenceMiseAJourService FrequenceMiseAJourService
@inject IStatutActifDonneesService StatutActifDonneesService
@inject IWorkflowApprobationService WorkflowApprobationService
@inject IExceptionHandlingService ExceptionHandlingService
@inject ISchemaMetadonneesService SchemaMetadonneesService
@inject IMetadonneeService MetadonneeService
@inject NavigationManager NavigationManager

<ActifDonneesNotification @ref="notification" />

<PageTitle>@(_isNewActif ? "Ajouter" : "Modifier") un Actif de Données - DataHub Gatineau</PageTitle>

<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card shadow-sm border-0 rounded-4">
            <div class="card-header bg-white border-0 py-3">
                <h3 class="mb-0 fw-bold">@(_isNewActif ? "Ajouter" : "Modifier") un Actif de Données</h3>
            </div>
            <div class="card-body">
                @if (_actif == null)
                {
                    <div class="d-flex justify-content-center my-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                }
                else
                {
                    <EditForm Model="@_actif" OnValidSubmit="@SoumettreFormulaire">
                        <DataAnnotationsValidator />
                        <ValidationSummary />

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Informations de base</h5>
                            </div>
                            <div class="card-body">
                                <InformationBaseTab ActifDonnees="@_actif" OnChange="@HandleActifDonneesChange" />
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="cheminAcces" class="form-label">Chemin d'accès</label>
                                <InputText id="cheminAcces" @bind-value="_actif.CheminAcces" class="form-control" />
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _actif.CheminAcces)" />
                            </div>
                            <div class="col-md-6">
                                <label for="connexionSourceDonnees" class="form-label">Connexion à la source de données</label>
                                <div class="input-group">
                                    <InputSelect id="connexionSourceDonnees" @bind-value="_actif.ConnexionSourceDonneesId" class="form-select">
                                        <option value="">Sélectionner une connexion</option>
                                        @foreach (var connexion in _connexionsSourceDonnees)
                                        {
                                            <option value="@connexion.Id">@connexion.Nom</option>
                                        }
                                    </InputSelect>
                                    <button class="btn btn-outline-secondary" type="button" @onclick="AjouterConnexionSourceDonnees">
                                        <i class="bi bi-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="dateDerniereMiseAJour" class="form-label">Date de dernière mise à jour</label>
                                <InputDate id="dateDerniereMiseAJour" @bind-value="_actif.DateDerniereMiseAJour" class="form-control" />
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _actif.DateDerniereMiseAJour)" />
                            </div>
                            <div class="col-md-6">
                                <label for="statut" class="form-label">Statut</label>
                                <InputSelect id="statut" @bind-value="_actif.StatutActifDonneesId" class="form-select">
                                    <option value="">Sélectionner un statut</option>
                                    @foreach (var statut in _statutsActifDonnees)
                                    {
                                        <option value="@statut.Id">@statut.Nom</option>
                                    }
                                </InputSelect>
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _actif.StatutActifDonneesId)" />
                            </div>
                        </div>

                    </EditForm>

                    @if (!_isNewActif && _actif != null)
                    {
                        <!-- Sección de Metadatos para activos existentes -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Métadonnées</h5>
                            </div>
                            <div class="card-body">
                                <MetadonneesTab ActifDonnees="@_actif" />
                            </div>
                        </div>

                        <!-- Sección de Productos de Datos -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0">Produits de Données</h5>
                            </div>
                            <div class="card-body">
                                <InformationBaseTab ActifDonnees="@_actif" OnActifDonneesChanged="HandleActifDonneesChange" />
                            </div>
                        </div>
                    }

                    <!-- Botones fuera del EditForm para evitar conflictos -->
                    <div class="d-flex justify-content-between mt-4">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary px-4" @onclick="EnregistrerCommeBrouillon">
                                <i class="bi bi-file-earmark me-1"></i> Enregistrer comme brouillon
                            </button>
                            <button type="button" class="btn btn-primary px-4" @onclick="@(async () => await SoumettreApprobation())">
                                <i class="bi bi-send me-1"></i> Soumettre pour approbation
                            </button>
                        </div>
                        <a href="@(_isNewActif ? "/actifs-donnees" : $"/actifs-donnees-details/{Id}")" class="btn btn-outline-secondary px-4">
                            <i class="bi bi-x-circle me-1"></i> Annuler
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Modal d'ajout de domaine de gouvernance -->
@if (_showDomaineModal)
{
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter un domaine de gouvernance</h5>
                    <button type="button" class="btn-close" @onclick="() => _showDomaineModal = false"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="@_nouveauDomaine" OnValidSubmit="EnregistrerDomaineGouvernance">
                        <DataAnnotationsValidator />
                        <ValidationSummary />

                        <div class="mb-3">
                            <label for="nomDomaine" class="form-label">Nom</label>
                            <InputText id="nomDomaine" @bind-value="_nouveauDomaine.Nom" class="form-control" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _nouveauDomaine.Nom)" />
                        </div>

                        <div class="mb-3">
                            <label for="descriptionDomaine" class="form-label">Description</label>
                            <InputTextArea id="descriptionDomaine" @bind-value="_nouveauDomaine.Description" class="form-control" rows="3" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _nouveauDomaine.Description)" />
                        </div>

                        <div class="mb-3">
                            <label for="proprietaireDomaine" class="form-label">Propriétaire</label>
                            <InputText id="proprietaireDomaine" @bind-value="_nouveauDomaine.Proprietaire" class="form-control" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _nouveauDomaine.Proprietaire)" />
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @onclick="() => _showDomaineModal = false">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

<!-- Modal d'ajout de connexion à la source de données -->
@if (_showConnexionModal)
{
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter une connexion à la source de données</h5>
                    <button type="button" class="btn-close" @onclick="() => _showConnexionModal = false"></button>
                </div>
                <div class="modal-body">
                    <EditForm Model="@_nouvelleConnexion" OnValidSubmit="EnregistrerConnexionSourceDonnees">
                        <DataAnnotationsValidator />
                        <ValidationSummary />

                        <div class="mb-3">
                            <label for="nomConnexion" class="form-label">Nom</label>
                            <InputText id="nomConnexion" @bind-value="_nouvelleConnexion.Nom" class="form-control" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _nouvelleConnexion.Nom)" />
                        </div>

                        <div class="mb-3">
                            <label for="descriptionConnexion" class="form-label">Description</label>
                            <InputTextArea id="descriptionConnexion" @bind-value="_nouvelleConnexion.Description" class="form-control" rows="3" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _nouvelleConnexion.Description)" />
                        </div>

                        <div class="mb-3">
                            <label for="typeSource" class="form-label">Type de source</label>
                            <InputSelect id="typeSource" @bind-value="_nouvelleConnexion.TypeSource" class="form-select">
                                <option value="SQL">SQL</option>
                                <option value="API">API</option>
                                <option value="Fichier">Fichier</option>
                                <option value="SFTP">SFTP</option>
                                <option value="Autre">Autre</option>
                            </InputSelect>
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _nouvelleConnexion.TypeSource)" />
                        </div>

                        <div class="mb-3">
                            <label for="chaineConnexion" class="form-label">Chaîne de connexion</label>
                            <InputText id="chaineConnexion" @bind-value="_nouvelleConnexion.ChaineConnexion" class="form-control" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _nouvelleConnexion.ChaineConnexion)" />
                        </div>

                        <div class="form-check mb-3">
                            <InputCheckbox id="estActive" @bind-value="_nouvelleConnexion.EstActive" class="form-check-input" />
                            <label class="form-check-label" for="estActive">
                                Connexion active
                            </label>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" @onclick="() => _showConnexionModal = false">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@code {
    [Parameter]
    public Guid? Id { get; set; }

    [Parameter]
    [SupplyParameterFromQuery(Name = "domaineId")]
    public Guid? DomaineId { get; set; }

    private ActifDonnees? _actif;
    private bool _isNewActif => Id == null;
    private List<DomaineGouvernance> _domainesGouvernance = new List<DomaineGouvernance>();
    private List<ConnexionSourceDonnees> _connexionsSourceDonnees = new List<ConnexionSourceDonnees>();
    private List<TypeActifDonneesItem> _typesActifDonnees = new List<TypeActifDonneesItem>();
    private List<FormatActifDonneesItem> _formatsActifDonnees = new List<FormatActifDonneesItem>();
    private List<SourceActifDonneesItem> _sourcesActifDonnees = new List<SourceActifDonneesItem>();
    private List<FrequenceMiseAJour> _frequencesMiseAJour = new List<FrequenceMiseAJour>();
    private List<StatutActifDonnees> _statutsActifDonnees = new List<StatutActifDonnees>();

    // Notification
    private ActifDonneesNotification? notification;

    // Modals
    private bool _showDomaineModal = false;
    private bool _showConnexionModal = false;
    private DomaineGouvernance _nouveauDomaine = new DomaineGouvernance();
    private ConnexionSourceDonnees _nouvelleConnexion = new ConnexionSourceDonnees();

    protected override async Task OnInitializedAsync()
    {
        Console.WriteLine("=== BLAZOR FUNCIONA: OnInitializedAsync ejecutado ===");
        Console.WriteLine($"=== PÁGINA: ActifDonneesForm - ID: {Id} ===");
        await ChargerDonnees();

        if (_isNewActif)
        {
            _actif = new ActifDonnees
            {
                Id = Guid.NewGuid(),
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now,
                DateDerniereMiseAJour = DateTime.Now,
                CreePar = "Utilisateur",
                ModifiePar = "Utilisateur",
                DomaineGouvernanceId = DomaineId
            };
        }
        else
        {
            _actif = await ActifDonneesService.ObtenirParIdAsync(Id!.Value);

            if (_actif == null)
            {
                NavigationManager.NavigateTo("/actifs-donnees");
            }
        }
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            // Marquer les services comme rendus pour permettre les appels JavaScript
            ((ActifDonneesService)ActifDonneesService).MarquerCommeRendu();

            // No necesitamos marcar el servicio de excepción como renderizado
            // porque estamos usando el servicio de DataHubGatineau.Web.Services.ExceptionHandlingService
            // que no tiene el método MarquerCommeRendu

            Console.WriteLine("ActifDonneesForm (Pages): Services marqués comme rendus après le premier rendu");
        }

        base.OnAfterRender(firstRender);
    }

    private async Task ChargerDonnees()
    {
        try
        {
            // Charger les domaines de gouvernance
            var domaines = await DomaineGouvernanceService.ObtenirTousAsync();
            _domainesGouvernance = domaines.ToList();

            // Charger les connexions aux sources de données
            var connexions = await ConnexionSourceDonneesService.ObtenirTousAsync();
            _connexionsSourceDonnees = connexions.ToList();

            // Charger les types d'actifs de données
            var types = await TypeActifDonneesService.ObtenirTousAsync();
            _typesActifDonnees = types.ToList();

            // Charger les formats d'actifs de données
            var formats = await FormatActifDonneesService.ObtenirTousAsync();
            _formatsActifDonnees = formats.ToList();

            // Charger les sources d'actifs de données
            var sources = await SourceActifDonneesService.ObtenirTousAsync();
            _sourcesActifDonnees = sources.ToList();

            // Charger les fréquences de mise à jour
            var frequences = await FrequenceMiseAJourService.ObtenirTousAsync();
            _frequencesMiseAJour = frequences.ToList();

            // Charger les statuts d'actifs de données
            var statuts = await StatutActifDonneesService.ObtenirTousAsync();
            _statutsActifDonnees = statuts.ToList();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement des données: {ex.Message}");
        }
    }

    private void AjouterDomaineGouvernance()
    {
        _nouveauDomaine = new DomaineGouvernance
        {
            Id = Guid.NewGuid(),
            DateCreation = DateTime.Now,
            DateModification = DateTime.Now,
            CreePar = "Utilisateur",
            ModifiePar = "Utilisateur"
        };
        _showDomaineModal = true;
    }

    private async Task EnregistrerDomaineGouvernance()
    {
        try
        {
            var domaine = await DomaineGouvernanceService.AjouterAsync(_nouveauDomaine);
            _domainesGouvernance.Add(domaine);
            _actif!.DomaineGouvernanceId = domaine.Id;
            _showDomaineModal = false;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'enregistrement du domaine de gouvernance: {ex.Message}");
        }
    }

    private void AjouterConnexionSourceDonnees()
    {
        _nouvelleConnexion = new ConnexionSourceDonnees
        {
            Id = Guid.NewGuid(),
            DateCreation = DateTime.Now,
            DateModification = DateTime.Now,
            CreePar = "Utilisateur",
            ModifiePar = "Utilisateur",
            EstActive = true
        };
        _showConnexionModal = true;
    }

    private async Task EnregistrerConnexionSourceDonnees()
    {
        try
        {
            var connexion = await ConnexionSourceDonneesService.AjouterAsync(_nouvelleConnexion);
            _connexionsSourceDonnees.Add(connexion);
            _actif!.ConnexionSourceDonneesId = connexion.Id;
            _showConnexionModal = false;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'enregistrement de la connexion à la source de données: {ex.Message}");
        }
    }

    private void OnClassificationChanged(ChangeEventArgs e)
    {
        if (e.Value != null && int.TryParse(e.Value.ToString(), out int value))
        {
            _actif!.ClassificationSensibilite = (ClassificationSensibilite)value;
            Console.WriteLine($"Classification changée à: {_actif.ClassificationSensibilite}");
        }
    }

    private void HandleActifDonneesChange(ActifDonnees actif)
    {
        // Mettre à jour les propriétés de l'actif de données
        if (_actif != null)
        {
            _actif.Nom = actif.Nom;
            _actif.Description = actif.Description;
            _actif.TypeActifDonneesId = actif.TypeActifDonneesId;
            _actif.FormatActifDonneesId = actif.FormatActifDonneesId;
            _actif.SourceActifDonneesId = actif.SourceActifDonneesId;
            _actif.Proprietaire = actif.Proprietaire;
            _actif.ClassificationSensibilite = actif.ClassificationSensibilite;
            _actif.FrequenceMiseAJourId = actif.FrequenceMiseAJourId;
            _actif.DomaineGouvernanceId = actif.DomaineGouvernanceId;
            _actif.EstElementCritique = actif.EstElementCritique;
            _actif.ProduitsDonnees = actif.ProduitsDonnees;
        }
    }

    private async Task<bool> ValiderSchemasObligatoires()
    {
        if (_actif == null || _actif.TypeActifDonnees == null) return true;

        try
        {
            // Obtenir le schéma actif pour ce type d'actif
            var schemaActif = await SchemaMetadonneesService.ObtenirActifParTypeActifAsync(_actif.TypeActifDonnees.Nom);
            if (schemaActif?.DefinitionsMetadonnees == null) return true;

            // Obtenir les métadonnées existantes pour cet actif
            var metadonneesExistantes = await MetadonneeService.ObtenirParActifDonneesAsync(_actif.Id);
            var metadonneesDict = metadonneesExistantes.ToDictionary(m => m.TypeMetadonnee?.Nom ?? "", m => m);

            // Vérifier chaque définition obligatoire
            var champsObligatoiresManquants = new List<string>();
            foreach (var definition in schemaActif.DefinitionsMetadonnees.Where(d => d.EstObligatoire))
            {
                if (!metadonneesDict.ContainsKey(definition.Nom) ||
                    string.IsNullOrWhiteSpace(metadonneesDict[definition.Nom].Valeur))
                {
                    champsObligatoiresManquants.Add(definition.Nom);
                }
            }

            if (champsObligatoiresManquants.Any())
            {
                var message = $"Les métadonnées suivantes sont obligatoires et doivent être renseignées : {string.Join(", ", champsObligatoiresManquants)}";
                notification?.ShowError("Métadonnées obligatoires manquantes", message);
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la validation des schémas obligatoires: {ex.Message}");
            return true; // En cas d'erreur, on laisse passer pour ne pas bloquer
        }
    }

    private async Task SoumettreFormulaire()
    {
        if (_actif == null) return;

        // Valider les schémas obligatoires pour tous les actifs
        if (!await ValiderSchemasObligatoires())
        {
            return; // Arrêter si la validation échoue
        }

        _actif.DateModification = DateTime.Now;
        _actif.ModifiePar = "Utilisateur";

        try
        {
            if (_isNewActif)
            {
                // Asegurarse de que el ID no sea vacío
                if (_actif.Id == Guid.Empty)
                {
                    _actif.Id = Guid.NewGuid();
                }

                // Asegurarse de que los IDs de referencia estén correctamente asignados
                if (!string.IsNullOrEmpty(_actif.Type) && (_actif.TypeActifDonneesId == null || _actif.TypeActifDonneesId == Guid.Empty))
                {
                    var type = _typesActifDonnees.FirstOrDefault(t => string.Equals(t.Nom, _actif.Type, StringComparison.OrdinalIgnoreCase));
                    if (type != null)
                    {
                        _actif.TypeActifDonneesId = type.Id;
                        Console.WriteLine($"Type assigné: {type.Nom} (ID: {type.Id})");
                    }
                }

                if (!string.IsNullOrEmpty(_actif.Format) && (_actif.FormatActifDonneesId == null || _actif.FormatActifDonneesId == Guid.Empty))
                {
                    var format = _formatsActifDonnees.FirstOrDefault(f => string.Equals(f.Nom, _actif.Format, StringComparison.OrdinalIgnoreCase));
                    if (format != null)
                    {
                        _actif.FormatActifDonneesId = format.Id;
                        Console.WriteLine($"Format assigné: {format.Nom} (ID: {format.Id})");
                    }
                }

                if (!string.IsNullOrEmpty(_actif.Source) && (_actif.SourceActifDonneesId == null || _actif.SourceActifDonneesId == Guid.Empty))
                {
                    var source = _sourcesActifDonnees.FirstOrDefault(s => string.Equals(s.Nom, _actif.Source, StringComparison.OrdinalIgnoreCase));
                    if (source != null)
                    {
                        _actif.SourceActifDonneesId = source.Id;
                        Console.WriteLine($"Source assignée: {source.Nom} (ID: {source.Id})");
                    }
                }

                Console.WriteLine($"Enviando activo para guardar con ID: {_actif.Id}");
                Console.WriteLine($"Classification: {_actif.ClassificationSensibilite}");
                Console.WriteLine($"TypeActifDonneesId: {_actif.TypeActifDonneesId}");
                Console.WriteLine($"FormatActifDonneesId: {_actif.FormatActifDonneesId}");
                Console.WriteLine($"SourceActifDonneesId: {_actif.SourceActifDonneesId}");
                Console.WriteLine($"FrequenceMiseAJourId: {_actif.FrequenceMiseAJourId}");

                var actifAjoute = await ActifDonneesService.AjouterAsync(_actif);

                if (actifAjoute != null && actifAjoute.Id != Guid.Empty)
                {
                    Console.WriteLine($"Activo guardado exitosamente con ID: {actifAjoute.Id}");
                    // Afficher une notification de succès
                    notification?.ShowSuccess("Actif de données ajouté", "L'actif de données a été ajouté avec succès. Il est disponible localement et sera synchronisé avec le serveur dès que possible.");
                    Console.WriteLine("Notification de succès affichée");
                    // Esperar un momento para que el backend procese la solicitud
                    await Task.Delay(500);
                    // Redirigir al componente con pestañas para poder agregar metadatos
                    NavigationManager.NavigateTo($"/actifs-donnees/modifier/{actifAjoute.Id}");
                }
                else
                {
                    Console.WriteLine("El activo devuelto es nulo o tiene ID vacío");
                    // Afficher une notification d'erreur
                    notification?.ShowError("Erreur", "Une erreur est survenue lors de l'ajout de l'actif de données.");
                    Console.WriteLine("Notification d'erreur affichée");
                    NavigationManager.NavigateTo("/actifs-donnees");
                }
            }
            else
            {
                Console.WriteLine($"Actualizando activo con ID: {Id!.Value}");
                Console.WriteLine($"Classification: {_actif.ClassificationSensibilite}");
                Console.WriteLine($"TypeActifDonneesId: {_actif.TypeActifDonneesId}");
                Console.WriteLine($"FormatActifDonneesId: {_actif.FormatActifDonneesId}");
                Console.WriteLine($"SourceActifDonneesId: {_actif.SourceActifDonneesId}");
                Console.WriteLine($"FrequenceMiseAJourId: {_actif.FrequenceMiseAJourId}");

                await ActifDonneesService.MettreAJourAsync(Id!.Value, _actif);
                // Esperar un momento para que el backend procese la solicitud
                await Task.Delay(500);
                NavigationManager.NavigateTo($"/actifs-donnees-details/{Id}");
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'enregistrement de l'actif de données: {ex.Message}");
            // En cas d'erreur, rester sur la page actuelle
        }
    }

    private async Task EnregistrerCommeBrouillon()
    {
        if (_actif == null) return;

        // Valider les schémas obligatoires pour les actifs existants
        if (!_isNewActif && !await ValiderSchemasObligatoires())
        {
            return; // Arrêter si la validation échoue
        }

        try
        {
            // Définir le statut comme brouillon
            var statutBrouillon = _statutsActifDonnees.FirstOrDefault(s => s.Nom.ToLower().Contains("brouillon") || s.Nom.ToLower().Contains("draft"));
            if (statutBrouillon != null)
            {
                _actif.StatutActifDonneesId = statutBrouillon.Id;
            }

            _actif.DateModification = DateTime.Now;
            _actif.ModifiePar = "Utilisateur";

            if (_isNewActif)
            {
                if (_actif.Id == Guid.Empty)
                {
                    _actif.Id = Guid.NewGuid();
                }

                var actifAjoute = await ActifDonneesService.AjouterAsync(_actif);
                if (actifAjoute != null)
                {
                    notification?.ShowSuccess("Brouillon enregistré", "L'actif de données a été enregistré comme brouillon.");
                    await Task.Delay(500);
                    NavigationManager.NavigateTo($"/actifs-donnees/modifier/{actifAjoute.Id}");
                }
            }
            else
            {
                await ActifDonneesService.MettreAJourAsync(Id!.Value, _actif);
                notification?.ShowSuccess("Brouillon mis à jour", "Les modifications ont été enregistrées comme brouillon.");
            }
        }
        catch (Exception ex)
        {
            notification?.ShowError("Erreur", $"Erreur lors de l'enregistrement du brouillon: {ex.Message}");
            Console.Error.WriteLine($"Erreur lors de l'enregistrement du brouillon: {ex.Message}");
        }
    }

    private async Task SoumettreApprobation()
    {
        // Log directo a la consola que SIEMPRE funciona
        Console.WriteLine("=== BOUTON CLIQUÉ: SoumettreApprobation (ASYNC) ===");

        try
        {
            Console.WriteLine("1. Entrée dans la méthode SoumettreApprobation");

            Console.WriteLine($"2. Vérification actif: _actif is null = {_actif == null}");
            if (_actif == null)
            {
                Console.WriteLine("3. ERREUR: Actif est null");
                return;
            }

            // Valider les schémas obligatoires pour les actifs existants
            if (!_isNewActif && !await ValiderSchemasObligatoires())
            {
                Console.WriteLine("4. ERREUR: Validation des schémas obligatoires échouée");
                return; // Arrêter si la validation échoue
            }

            Console.WriteLine($"3. Actif trouvé: {_actif.Nom} (ID: {_actif.Id})");

            Console.WriteLine("4. Test simple réussi");

            Console.WriteLine("5. Fin de la méthode (return)");
            return;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"EXCEPTION dans SoumettreApprobation: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
