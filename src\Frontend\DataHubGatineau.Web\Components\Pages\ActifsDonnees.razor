@page "/actifs-donnees"
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services
@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Components.Shared
@inject IActifDonneesService ActifDonneesService
@inject ITypeActifDonneesService TypeActifDonneesService
@inject ISourceActifDonneesService SourceActifDonneesService
@inject IDomaineGouvernanceService DomaineGouvernanceService
@inject IExceptionHandlingService ExceptionHandlingService
@inject IConfirmationService ConfirmationService
@inject NavigationManager NavigationManager

<PageTitle>Catalogue de Données - DataHub Gatineau</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Catalogue de Données</h1>
    <div>
        <button class="btn btn-outline-primary me-2" @onclick="ToggleAdvancedFilters">
            <i class="bi bi-funnel"></i> Filtres avancés
        </button>
        <a href="/actifs-donnees/ajouter" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Ajouter un actif
        </a>
    </div>
</div>

<div class="card shadow-sm border-0 rounded-4 mb-4">
    <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center py-3">
        <h5 class="mb-0 fw-bold">
            <i class="bi bi-search text-primary me-2"></i>
            Recherche rapide
        </h5>
        <button class="btn btn-sm btn-outline-secondary rounded-pill" @onclick="ReinitialiserFiltres">
            <i class="bi bi-arrow-counterclockwise me-1"></i> Réinitialiser
        </button>
    </div>
    <div class="card-body">
        <div class="input-group">
            <span class="input-group-text bg-light border-end-0">
                <i class="bi bi-search"></i>
            </span>
            <input type="text" class="form-control border-start-0"
                   placeholder="Rechercher par nom, description, propriétaire..."
                   @bind="_searchTerm"
                   @bind:event="oninput"
                   @onkeyup="@(async e => { if (e.Key == "Enter") await FiltrerActifs(); })" />
            <button class="btn btn-primary" @onclick="async () => await FiltrerActifs()">Rechercher</button>
        </div>
    </div>
</div>

@if (_showAdvancedFilters)
{
    <ActifDonneesFilters
        TypeId="@_typeId"
        TypeIdChanged="@((Guid? id) => _typeId = id)"
        SourceId="@_sourceId"
        SourceIdChanged="@((Guid? id) => _sourceId = id)"
        DomaineId="@_domaineId"
        DomaineIdChanged="@((Guid? id) => _domaineId = id)"
        EstCritique="@_estCritique"
        EstCritiqueChanged="@((string? val) => _estCritique = val)"
        Classification="@_classificationFilter"
        ClassificationChanged="@((string? val) => _classificationFilter = val)"
        ConnexionId="@_connexionId"
        ConnexionIdChanged="@((Guid? id) => _connexionId = id)"
        OnFiltresAppliques="FiltrerActifs" />
}

@if (_loading)
{
    <div class="card shadow-sm border-0 rounded-4 mb-4">
        <div class="card-body p-5">
            <div class="d-flex flex-column align-items-center justify-content-center">
                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <h5 class="mb-2">Chargement des actifs de données</h5>
                <p class="text-muted">Veuillez patienter pendant que nous récupérons les données...</p>
            </div>
        </div>
    </div>
}
else if (_actifs == null || !_actifs.Any())
{
    <div class="alert alert-info">
        Aucun actif de données trouvé.
    </div>
}
else
{
    <div class="card shadow-sm border-0 rounded-4">
        <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center py-3">
            <div class="d-flex align-items-center">
                <h5 class="mb-0 fw-bold">
                    <i class="bi bi-table text-primary me-2"></i>
                    Résultats (@_actifs.Count() actifs)
                </h5>
                @if (_filtresActifs)
                {
                    <span class="badge bg-primary ms-2">Filtres actifs</span>
                }
            </div>
            <div class="btn-group">
                <button class="btn btn-outline-secondary @(_viewMode == "table" ? "active" : "")" @onclick="@(() => _viewMode = "table")">
                    <i class="bi bi-table"></i>
                </button>
                <button class="btn btn-outline-secondary @(_viewMode == "cards" ? "active" : "")" @onclick="@(() => _viewMode = "cards")">
                    <i class="bi bi-grid-3x3-gap"></i>
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (_viewMode == "table")
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="sortable" @onclick="@(() => TrierPar("Nom"))">
                                    Nom
                                    @if (_triPar == "Nom")
                                    {
                                        <i class="bi @(_triAscendant ? "bi-sort-alpha-down" : "bi-sort-alpha-up")"></i>
                                    }
                                </th>
                                <th class="sortable" @onclick="@(() => TrierPar("Type"))">
                                    Type
                                    @if (_triPar == "Type")
                                    {
                                        <i class="bi @(_triAscendant ? "bi-sort-alpha-down" : "bi-sort-alpha-up")"></i>
                                    }
                                </th>
                                <th class="sortable" @onclick="@(() => TrierPar("Source"))">
                                    Source
                                    @if (_triPar == "Source")
                                    {
                                        <i class="bi @(_triAscendant ? "bi-sort-alpha-down" : "bi-sort-alpha-up")"></i>
                                    }
                                </th>
                                <th class="sortable" @onclick="@(() => TrierPar("BaseDonnees"))">
                                    Base de données
                                    @if (_triPar == "BaseDonnees")
                                    {
                                        <i class="bi @(_triAscendant ? "bi-sort-alpha-down" : "bi-sort-alpha-up")"></i>
                                    }
                                </th>
                                <th class="sortable" @onclick="@(() => TrierPar("Proprietaire"))">
                                    Propriétaire
                                    @if (_triPar == "Proprietaire")
                                    {
                                        <i class="bi @(_triAscendant ? "bi-sort-alpha-down" : "bi-sort-alpha-up")"></i>
                                    }
                                </th>
                                <th class="sortable" @onclick="@(() => TrierPar("Classification"))">
                                    Classification
                                    @if (_triPar == "Classification")
                                    {
                                        <i class="bi @(_triAscendant ? "bi-sort-alpha-down" : "bi-sort-alpha-up")"></i>
                                    }
                                </th>
                                <th class="sortable" @onclick="@(() => TrierPar("DateModification"))">
                                    Date de modification
                                    @if (_triPar == "DateModification")
                                    {
                                        <i class="bi @(_triAscendant ? "bi-sort-down" : "bi-sort-up")"></i>
                                    }
                                </th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var actif in _actifs)
                            {
                                <tr class="transition-hover">
                                    <td>
                                        <a href="/actifs-donnees-details/@actif.Id" class="text-decoration-none fw-semibold text-primary">
                                            @actif.Nom
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary bg-opacity-10 text-primary px-2 py-1 rounded-pill">
                                            @{
                                                // Mostrar el tipo del activo de datos
                                                string typeDisplay = "-";
                                                if (actif.TypeActifDonnees != null && !string.IsNullOrEmpty(actif.TypeActifDonnees.Nom))
                                                {
                                                    typeDisplay = actif.TypeActifDonnees.Nom;
                                                }
                                                else if (!string.IsNullOrEmpty(actif.Type))
                                                {
                                                    typeDisplay = actif.Type;
                                                }
                                                @typeDisplay
                                            }
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary bg-opacity-10 text-secondary px-2 py-1 rounded-pill">
                                            @{
                                                // Mostrar la fuente del activo de datos
                                                string sourceDisplay = "-";
                                                if (actif.SourceActifDonnees != null && !string.IsNullOrEmpty(actif.SourceActifDonnees.Nom))
                                                {
                                                    sourceDisplay = actif.SourceActifDonnees.Nom;
                                                }
                                                else if (!string.IsNullOrEmpty(actif.Source))
                                                {
                                                    sourceDisplay = actif.Source;
                                                }
                                                @sourceDisplay
                                            }
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info bg-opacity-10 text-info px-2 py-1 rounded-pill">
                                            <i class="bi bi-server me-1"></i>
                                            @{
                                                // Mostrar la base de datos de la conexión
                                                string baseDonneesDisplay = "-";
                                                if (actif.ConnexionSourceDonnees != null && !string.IsNullOrEmpty(actif.ConnexionSourceDonnees.BaseDonnees))
                                                {
                                                    baseDonneesDisplay = $"{actif.ConnexionSourceDonnees.BaseDonnees}@{actif.ConnexionSourceDonnees.Serveur}";
                                                }
                                                @baseDonneesDisplay
                                            }
                                        </span>
                                    </td>
                                    <td>@actif.Proprietaire</td>
                                    <td>
                                        <span class="badge @GetClassificationBadgeClass(actif.ClassificationSensibilite)">
                                            @actif.ClassificationSensibilite
                                        </span>
                                    </td>
                                    <td>@actif.DateModification.ToString("dd/MM/yyyy")</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="/actifs-donnees-details/@actif.Id" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="/actifs-donnees/ajouter?Id=@actif.Id" class="btn btn-sm btn-outline-warning">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-danger" @onclick="() => ConfirmerSuppression(actif)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="p-3">
                    <div class="row">
                        @foreach (var actif in _actifs)
                        {
                            <div class="col-md-4 mb-4">
                                <div class="card h-100 border-0 shadow-sm hover-card">
                                    <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0 text-truncate">
                                            <a href="/actifs-donnees-details/@actif.Id" class="text-decoration-none text-primary">
                                                @actif.Nom
                                            </a>
                                        </h5>
                                        <span class="badge @GetClassificationBadgeClass(actif.ClassificationSensibilite)">
                                            @actif.ClassificationSensibilite
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text text-muted small mb-3">
                                            @(actif.Description?.Length > 100 ? actif.Description.Substring(0, 97) + "..." : actif.Description)
                                        </p>
                                        <div class="d-flex flex-wrap gap-2 mb-3">
                                            <span class="badge bg-primary bg-opacity-10 text-primary">
                                                <i class="bi bi-tag-fill me-1"></i>
                                                @{
                                                    // Mostrar el tipo del activo de datos
                                                    string typeDisplay = "-";
                                                    if (actif.TypeActifDonnees != null && !string.IsNullOrEmpty(actif.TypeActifDonnees.Nom))
                                                    {
                                                        typeDisplay = actif.TypeActifDonnees.Nom;
                                                    }
                                                    else if (!string.IsNullOrEmpty(actif.Type))
                                                    {
                                                        typeDisplay = actif.Type;
                                                    }
                                                    @typeDisplay
                                                }
                                            </span>
                                            <span class="badge bg-secondary bg-opacity-10 text-secondary">
                                                <i class="bi bi-database me-1"></i>
                                                @{
                                                    // Mostrar la fuente del activo de datos
                                                    string sourceDisplay = "-";
                                                    if (actif.SourceActifDonnees != null && !string.IsNullOrEmpty(actif.SourceActifDonnees.Nom))
                                                    {
                                                        sourceDisplay = actif.SourceActifDonnees.Nom;
                                                    }
                                                    else if (!string.IsNullOrEmpty(actif.Source))
                                                    {
                                                        sourceDisplay = actif.Source;
                                                    }
                                                    @sourceDisplay
                                                }
                                            </span>
                                            @if (actif.ConnexionSourceDonnees != null && !string.IsNullOrEmpty(actif.ConnexionSourceDonnees.BaseDonnees))
                                            {
                                                <span class="badge bg-info bg-opacity-10 text-info">
                                                    <i class="bi bi-server me-1"></i>
                                                    @actif.ConnexionSourceDonnees.BaseDonnees
                                                </span>
                                            }
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="bi bi-calendar-event me-1"></i> @actif.DateModification.ToString("dd/MM/yyyy")
                                            </small>
                                            <div class="btn-group" role="group">
                                                <a href="/actifs-donnees-details/@actif.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="/actifs-donnees/ajouter?Id=@actif.Id" class="btn btn-sm btn-outline-warning">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" @onclick="() => ConfirmerSuppression(actif)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
        <div class="card-footer bg-white border-0 d-flex justify-content-between align-items-center py-3">
            <div>
                <span class="text-muted">Affichage de @((_currentPage - 1) * _pageSize + 1) à @Math.Min(_currentPage * _pageSize, _totalActifs) sur @_totalActifs actif(s)</span>
            </div>
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <select class="form-select form-select-sm" @bind="_pageSize">
                        <option value="12">12 par page</option>
                        <option value="24">24 par page</option>
                        <option value="48">48 par page</option>
                        <option value="96">96 par page</option>
                    </select>
                </div>
                <nav aria-label="Pagination">
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item @(_currentPage <= 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => ChangerPage(1)" disabled="@(_currentPage <= 1)">
                                <i class="bi bi-chevron-double-left"></i>
                            </button>
                        </li>
                        <li class="page-item @(_currentPage <= 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => ChangerPage(_currentPage - 1)" disabled="@(_currentPage <= 1)">
                                <i class="bi bi-chevron-left"></i>
                            </button>
                        </li>

                        @{
                            int totalPages = (_totalActifs + _pageSize - 1) / _pageSize;
                            int startPage = Math.Max(1, _currentPage - 2);
                            int endPage = Math.Min(totalPages, startPage + 4);

                            if (startPage > 1)
                            {
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            }

                            for (int i = startPage; i <= endPage; i++)
                            {
                                int pageNumber = i;
                                <li class="page-item @(pageNumber == _currentPage ? "active" : "")">
                                    <button class="page-link" @onclick="() => ChangerPage(pageNumber)">@pageNumber</button>
                                </li>
                            }

                            if (endPage < totalPages)
                            {
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            }
                        }

                        <li class="page-item @(!_hasMoreActifs ? "disabled" : "")">
                            <button class="page-link" @onclick="() => ChangerPage(_currentPage + 1)" disabled="@(!_hasMoreActifs)">
                                <i class="bi bi-chevron-right"></i>
                            </button>
                        </li>
                        <li class="page-item @(!_hasMoreActifs ? "disabled" : "")">
                            <button class="page-link" @onclick="() => ChangerPage((_totalActifs + _pageSize - 1) / _pageSize)" disabled="@(!_hasMoreActifs)">
                                <i class="bi bi-chevron-double-right"></i>
                            </button>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <style>
        .sortable {
            cursor: pointer;
        }

        .sortable:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .hover-card {
            transition: all 0.3s ease;
        }

        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
        }
    </style>
}



@code {
    private IEnumerable<ActifDonnees>? _actifs;
    private IEnumerable<ActifDonnees>? _cachedActifs;
    private DateTime _lastCacheRefresh = DateTime.MinValue;
    private readonly TimeSpan _cacheExpirationTime = TimeSpan.FromMinutes(5);
    private bool _loading = true;
    private bool _hasMoreActifs = false;
    private int _pageSize = 12;
    private int _currentPage = 1;
    private int _totalActifs = 0;

    // Filtres de base
    private string _searchTerm = string.Empty;

    // Filtres avancés
    private Guid? _typeId = null;
    private Guid? _sourceId = null;
    private Guid? _domaineId = null;
    private Guid? _connexionId = null;
    private string? _estCritique = null;
    private string _classificationFilter = string.Empty;
    private string _proprietaireFilter = string.Empty;
    private DateTime? _dateModificationDebut = null;
    private DateTime? _dateModificationFin = null;
    private int _scoreQualiteMin = 0;

    // Options d'affichage
    private bool _showAdvancedFilters = false;
    private bool _filtresActifs = false;
    private string _viewMode = "cards";
    private string _triPar = "DateModification";
    private bool _triAscendant = false;

    // Listes pour les filtres
    private List<string> _sources = new();
    private List<string> _proprietaires = new();





    protected override async Task OnInitializedAsync()
    {
        await ChargerListesFiltres();
        await ChargerActifs();
    }

    private async Task ChargerListesFiltres()
    {
        var allActifs = await ActifDonneesService.ObtenirTousAsync();

        // Extraire les sources uniques
        _sources = allActifs.Where(a => a.SourceActifDonnees != null)
                           .Select(a => a.SourceActifDonnees.Nom)
                           .Where(s => !string.IsNullOrEmpty(s))
                           .Distinct()
                           .OrderBy(s => s)
                           .ToList();

        // Extraire les propriétaires uniques
        _proprietaires = allActifs.Select(a => a.Proprietaire)
                                 .Where(p => !string.IsNullOrEmpty(p))
                                 .Distinct()
                                 .OrderBy(p => p)
                                 .ToList();
    }

    private async Task ChargerActifs()
    {
        _loading = true;

        try
        {
            // Utiliser la caché si disponible
            if (_cachedActifs == null)
            {
                Console.WriteLine("Chargement des actifs depuis le service");
                _cachedActifs = await ActifDonneesService.ObtenirTousAsync();
                _lastCacheRefresh = DateTime.Now;
            }
            else if (DateTime.Now - _lastCacheRefresh > _cacheExpirationTime)
            {
                Console.WriteLine("Rafraîchissement de la caché des actifs");
                _cachedActifs = await ActifDonneesService.ObtenirTousAsync();
                _lastCacheRefresh = DateTime.Now;
            }
            else
            {
                Console.WriteLine("Utilisation de la caché des actifs");
            }

            // Appliquer les filtres
            var filteredActifs = ApplyFilters(_cachedActifs);
            _totalActifs = filteredActifs.Count();

            // Appliquer le tri
            filteredActifs = ApplySort(filteredActifs);

            // Vérifier si des filtres sont actifs
            _filtresActifs = !string.IsNullOrWhiteSpace(_searchTerm) ||
                            (_typeId.HasValue && _typeId != Guid.Empty) ||
                            (_sourceId.HasValue && _sourceId != Guid.Empty) ||
                            (_domaineId.HasValue && _domaineId != Guid.Empty) ||
                            (_connexionId.HasValue && _connexionId != Guid.Empty) ||
                            !string.IsNullOrWhiteSpace(_estCritique) ||
                            !string.IsNullOrWhiteSpace(_classificationFilter) ||
                            !string.IsNullOrWhiteSpace(_proprietaireFilter) ||
                            _dateModificationDebut.HasValue ||
                            _dateModificationFin.HasValue ||
                            _scoreQualiteMin > 0;

            // Pagination efficiente
            _actifs = filteredActifs.Skip((_currentPage - 1) * _pageSize).Take(_pageSize);
            _hasMoreActifs = filteredActifs.Count() > _pageSize * _currentPage;

            Console.WriteLine($"Affichage de {_actifs.Count()} actifs sur {_totalActifs} (page {_currentPage})");
        }
        finally
        {
            _loading = false;
        }
    }

    private IEnumerable<ActifDonnees> ApplyFilters(IEnumerable<ActifDonnees> actifs)
    {
        var filtered = actifs;

        // Filtre de recherche
        if (!string.IsNullOrWhiteSpace(_searchTerm))
        {
            filtered = filtered.Where(e =>
                e.Nom.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (e.Description != null && e.Description.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                (e.Proprietaire != null && e.Proprietaire.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                (e.SourceActifDonnees != null && e.SourceActifDonnees.Nom != null &&
                 e.SourceActifDonnees.Nom.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase))
            );
        }

        // Filtre par type
        if (_typeId.HasValue && _typeId != Guid.Empty)
        {
            filtered = filtered.Where(e => e.TypeActifDonneesId == _typeId);
        }

        // Filtre par source
        if (_sourceId.HasValue && _sourceId != Guid.Empty)
        {
            filtered = filtered.Where(e => e.SourceActifDonneesId == _sourceId);
        }

        // Filtre par domaine
        if (_domaineId.HasValue && _domaineId != Guid.Empty)
        {
            filtered = filtered.Where(e => e.DomaineGouvernanceId == _domaineId);
        }

        // Filtre par connexion de base de données
        if (_connexionId.HasValue && _connexionId != Guid.Empty)
        {
            filtered = filtered.Where(e => e.ConnexionSourceDonneesId == _connexionId);
        }

        // Filtre par classification
        if (!string.IsNullOrWhiteSpace(_classificationFilter) &&
            Enum.TryParse<ClassificationSensibilite>(_classificationFilter, out var classification))
        {
            filtered = filtered.Where(e => e.ClassificationSensibilite == classification);
        }

        // Filtre par actifs critiques
        if (!string.IsNullOrWhiteSpace(_estCritique))
        {
            bool estCritique = _estCritique == "true";
            filtered = filtered.Where(e => e.EstElementCritique == estCritique);
        }

        if (!string.IsNullOrWhiteSpace(_proprietaireFilter))
        {
            filtered = filtered.Where(e => e.Proprietaire.Equals(_proprietaireFilter, StringComparison.OrdinalIgnoreCase));
        }

        if (_dateModificationDebut.HasValue)
        {
            filtered = filtered.Where(e => e.DateModification.Date >= _dateModificationDebut.Value.Date);
        }

        if (_dateModificationFin.HasValue)
        {
            filtered = filtered.Where(e => e.DateModification.Date <= _dateModificationFin.Value.Date);
        }

        // Simuler un filtre de score de qualité (dans une application réelle, cela viendrait d'un service)
        if (_scoreQualiteMin > 0)
        {
            // Ici, nous simulons un score de qualité basé sur l'ID pour la démonstration
            filtered = filtered.Where(e => (e.Id.GetHashCode() % 100) >= _scoreQualiteMin);
        }

        return filtered;
    }

    private IEnumerable<ActifDonnees> ApplySort(IEnumerable<ActifDonnees> actifs)
    {
        return _triPar switch
        {
            "Nom" => _triAscendant
                ? actifs.OrderBy(e => e.Nom)
                : actifs.OrderByDescending(e => e.Nom),

            "Type" => _triAscendant
                ? actifs.OrderBy(e => e.TypeActifDonnees != null ? e.TypeActifDonnees.Nom : string.Empty)
                : actifs.OrderByDescending(e => e.TypeActifDonnees != null ? e.TypeActifDonnees.Nom : string.Empty),

            "Source" => _triAscendant
                ? actifs.OrderBy(e => e.SourceActifDonnees != null ? e.SourceActifDonnees.Nom : string.Empty)
                : actifs.OrderByDescending(e => e.SourceActifDonnees != null ? e.SourceActifDonnees.Nom : string.Empty),

            "BaseDonnees" => _triAscendant
                ? actifs.OrderBy(e => e.ConnexionSourceDonnees != null ? e.ConnexionSourceDonnees.BaseDonnees : string.Empty)
                : actifs.OrderByDescending(e => e.ConnexionSourceDonnees != null ? e.ConnexionSourceDonnees.BaseDonnees : string.Empty),

            "Proprietaire" => _triAscendant
                ? actifs.OrderBy(e => e.Proprietaire)
                : actifs.OrderByDescending(e => e.Proprietaire),

            "Classification" => _triAscendant
                ? actifs.OrderBy(e => e.ClassificationSensibilite)
                : actifs.OrderByDescending(e => e.ClassificationSensibilite),

            "DateModification" => _triAscendant
                ? actifs.OrderBy(e => e.DateModification)
                : actifs.OrderByDescending(e => e.DateModification),

            _ => actifs.OrderByDescending(e => e.DateModification)
        };
    }

    private void ToggleAdvancedFilters()
    {
        _showAdvancedFilters = !_showAdvancedFilters;
    }

    private async Task TrierPar(string colonne)
    {
        if (_triPar == colonne)
        {
            // Inverser l'ordre si on clique sur la même colonne
            _triAscendant = !_triAscendant;
        }
        else
        {
            // Nouvelle colonne, tri par défaut
            _triPar = colonne;
            _triAscendant = true;
        }

        // Recharger les actifs avec le nouveau tri
        await ChargerActifs();
    }

    private async Task ReinitialiserFiltres()
    {
        _searchTerm = string.Empty;
        _typeId = null;
        _sourceId = null;
        _domaineId = null;
        _connexionId = null;
        _estCritique = null;
        _classificationFilter = string.Empty;
        _proprietaireFilter = string.Empty;
        _dateModificationDebut = null;
        _dateModificationFin = null;
        _scoreQualiteMin = 0;
        _currentPage = 1;
        _triPar = "DateModification";
        _triAscendant = false;

        await ChargerActifs();
    }

    private async Task FiltrerActifs()
    {
        _currentPage = 1;
        await ChargerActifs();
    }

    private async Task ChangerPage(int page)
    {
        if (page < 1) page = 1;

        int totalPages = (_totalActifs + _pageSize - 1) / _pageSize;
        if (page > totalPages) page = totalPages;

        if (_currentPage != page)
        {
            _currentPage = page;
            await ChargerActifs();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await ChargerActifs();
    }

    private async Task ConfirmerSuppression(ActifDonnees actif)
    {
        var details = new List<string>
        {
            $"Nom: {actif.Nom}",
            $"Type: {actif.TypeActifDonnees?.Nom ?? actif.Type ?? "-"}",
            $"Propriétaire: {actif.Proprietaire}",
            $"Classification: {actif.ClassificationSensibilite}",
            "Cette action est irréversible."
        };

        var confirme = await ConfirmationService.ConfirmerAvecDetailsAsync(
            "Supprimer l'actif de données",
            "Êtes-vous sûr de vouloir supprimer cet actif de données ?",
            details,
            DataHubGatineau.Web.Models.TypeMessage.Erreur
        );

        if (confirme)
        {
            await SupprimerActif(actif);
        }
    }

    private async Task SupprimerActif(ActifDonnees actif)
    {
        try
        {
            var resultat = await ActifDonneesService.SupprimerAsync(actif.Id);
            if (resultat)
            {
                // Invalider le cache pour forcer le rechargement
                _cachedActifs = null;
                await ChargerActifs();

                // Afficher un message de succès
                await ConfirmationService.AfficherSuccesAsync(
                    "Actif supprimé",
                    $"L'actif de données '{actif.Nom}' a été supprimé avec succès."
                );
            }
            else
            {
                await ConfirmationService.AfficherErreurAsync(
                    "Erreur de suppression",
                    "La suppression de l'actif de données a échoué."
                );
            }
        }
        catch (Exception ex)
        {
            await ConfirmationService.AfficherErreurAsync(
                "Erreur",
                $"Une erreur s'est produite lors de la suppression : {ex.Message}"
            );
        }
    }

    private string GetClassificationBadgeClass(ClassificationSensibilite classification)
    {
        return classification switch
        {
            ClassificationSensibilite.Public => "bg-success text-white",
            ClassificationSensibilite.Interne => "bg-info text-white",
            ClassificationSensibilite.Confidentiel => "bg-warning text-dark",
            ClassificationSensibilite.Critique => "bg-danger text-white",
            ClassificationSensibilite.Restreint => "bg-danger text-white",
            _ => "bg-secondary text-white"
        };
    }
}

