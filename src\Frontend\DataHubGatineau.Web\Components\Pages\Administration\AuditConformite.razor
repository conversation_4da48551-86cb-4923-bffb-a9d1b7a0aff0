@page "/administration/audit"
@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Models.Auditoria
@inject IServiceAudit ServiceAudit
@inject IJSRuntime JSRuntime
@inject ILogger<AuditConformite> Logger

<PageTitle>Audit et Conformité - DataHub Gatineau</PageTitle>

<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="bi bi-clipboard-data-fill me-2 text-primary"></i>
                        Audit et Conformité
                    </h1>
                    <p class="text-muted mb-0">Surveillance et conformité réglementaire du système de gouvernance des données</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" @onclick="ExporterAudits">
                        <i class="bi bi-download me-1"></i>
                        Exporter
                    </button>
                    <button class="btn btn-primary" @onclick="ActualiserDonnees">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        Actualiser
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques d'audit -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                <i class="bi bi-activity text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title mb-1">Total d'événements</h6>
                            <h4 class="mb-0">@Statistiques.TotalEntrees.ToString("N0")</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                <i class="bi bi-person-check text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title mb-1">Connexions</h6>
                            <h4 class="mb-0">@Statistiques.TotalConnexions.ToString("N0")</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                                <i class="bi bi-exclamation-triangle text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title mb-1">Erreurs</h6>
                            <h4 class="mb-0">@Statistiques.TotalErreurs.ToString("N0")</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-opacity-10 rounded-circle p-3">
                                <i class="bi bi-shield-exclamation text-danger fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="card-title mb-1">Événements sécurité</h6>
                            <h4 class="mb-0">@Statistiques.TotalActionsSecurite.ToString("N0")</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-funnel me-2"></i>
                        Filtres de recherche
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Recherche</label>
                            <input type="text" class="form-control" @bind="Filtres.TexteRecherche" placeholder="Rechercher..." />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date début</label>
                            <input type="date" class="form-control" @bind="Filtres.DateDebut" @bind:format="yyyy-MM-dd" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Date fin</label>
                            <input type="date" class="form-control" @bind="Filtres.DateFin" @bind:format="yyyy-MM-dd" />
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Type d'action</label>
                            <select class="form-select" @bind="TypeActionSelectionne">
                                <option value="">Tous les types</option>
                                @foreach (var type in Enum.GetValues<TypeActionAudit>())
                                {
                                    <option value="@type">@GetTypeActionDisplay(type)</option>
                                }
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button class="btn btn-primary me-2" @onclick="AppliquerFiltres">
                                <i class="bi bi-search me-1"></i>
                                Rechercher
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="ReinitialiserFiltres">
                                <i class="bi bi-x-circle me-1"></i>
                                Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des événements d'audit -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>
                        Événements d'audit
                    </h6>
                    <span class="badge bg-primary">@ResultatAudit.TotalEntrees événements</span>
                </div>
                <div class="card-body p-0">
                    @if (EstEnChargement)
                    {
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-2 text-muted">Chargement des données d'audit...</p>
                        </div>
                    }
                    else if (ResultatAudit.Entrees.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Date/Heure</th>
                                        <th>Action</th>
                                        <th>Utilisateur</th>
                                        <th>Entité</th>
                                        <th>Type</th>
                                        <th>Gravité</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var entree in ResultatAudit.Entrees)
                                    {
                                        <tr>
                                            <td>
                                                <small class="text-muted">
                                                    @entree.DateAction.ToString("dd/MM/yyyy HH:mm:ss")
                                                </small>
                                            </td>
                                            <td>
                                                <strong>@entree.Action</strong>
                                                @if (!string.IsNullOrEmpty(entree.Commentaire))
                                                {
                                                    <br><small class="text-muted">@entree.Commentaire</small>
                                                }
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="bi bi-person-circle me-2"></i>
                                                    <div>
                                                        <div>@entree.NomUtilisateur</div>
                                                        @if (!string.IsNullOrEmpty(entree.AdresseIP))
                                                        {
                                                            <small class="text-muted">@entree.AdresseIP</small>
                                                        }
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark">@entree.TypeEntite</span>
                                                @if (!string.IsNullOrEmpty(entree.NomEntite))
                                                {
                                                    <br><small class="text-muted">@entree.NomEntite</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge @GetTypeActionBadgeClass(entree.TypeAction)">
                                                    @GetTypeActionDisplay(entree.TypeAction)
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge @GetGraviteBadgeClass(entree.NiveauGravite)">
                                                    @GetGraviteDisplay(entree.NiveauGravite)
                                                </span>
                                            </td>
                                            <td>
                                                @if (entree.Succes)
                                                {
                                                    <i class="bi bi-check-circle-fill text-success"></i>
                                                }
                                                else
                                                {
                                                    <i class="bi bi-x-circle-fill text-danger" title="@entree.MessageErreur"></i>
                                                }
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" @onclick="() => VoirDetails(entree)">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if (ResultatAudit.TotalPages > 1)
                        {
                            <div class="card-footer bg-light">
                                <nav aria-label="Navigation des pages d'audit">
                                    <ul class="pagination pagination-sm justify-content-center mb-0">
                                        <li class="page-item @(ResultatAudit.APrecedent ? "" : "disabled")">
                                            <button class="page-link" @onclick="() => ChangerPage(ResultatAudit.Page - 1)" disabled="@(!ResultatAudit.APrecedent)">
                                                <i class="bi bi-chevron-left"></i>
                                            </button>
                                        </li>
                                        
                                        @for (int i = Math.Max(1, ResultatAudit.Page - 2); i <= Math.Min(ResultatAudit.TotalPages, ResultatAudit.Page + 2); i++)
                                        {
                                            <li class="page-item @(i == ResultatAudit.Page ? "active" : "")">
                                                <button class="page-link" @onclick="() => ChangerPage(i)">@i</button>
                                            </li>
                                        }
                                        
                                        <li class="page-item @(ResultatAudit.ASuivant ? "" : "disabled")">
                                            <button class="page-link" @onclick="() => ChangerPage(ResultatAudit.Page + 1)" disabled="@(!ResultatAudit.ASuivant)">
                                                <i class="bi bi-chevron-right"></i>
                                            </button>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center p-4">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h5 class="mt-3">Aucun événement d'audit trouvé</h5>
                            <p class="text-muted">Aucun événement ne correspond aux critères de recherche.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private FiltresAudit Filtres { get; set; } = new();
    private ResultatPagineAudit ResultatAudit { get; set; } = new();
    private StatistiquesAuditAvancees Statistiques { get; set; } = new();
    private bool EstEnChargement { get; set; } = true;
    
    private string TypeActionSelectionne { get; set; } = "";

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    private async Task ChargerDonnees()
    {
        try
        {
            EstEnChargement = true;
            
            // Charger les statistiques
            Statistiques = await ServiceAudit.ObtenirStatistiquesAvanceesAsync();
            
            // Charger les événements d'audit
            ResultatAudit = await ServiceAudit.ObtenirAuditsAsync(Filtres);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des données d'audit");
            await MostrarNotificacion("error", "Erreur lors du chargement des données d'audit");
        }
        finally
        {
            EstEnChargement = false;
        }
    }

    private async Task AppliquerFiltres()
    {
        // Appliquer le filtre de type d'action
        if (!string.IsNullOrEmpty(TypeActionSelectionne) && Enum.TryParse<TypeActionAudit>(TypeActionSelectionne, out var typeAction))
        {
            Filtres.TypesAction = new List<TypeActionAudit> { typeAction };
        }
        else
        {
            Filtres.TypesAction = null;
        }
        
        Filtres.Page = 1; // Retourner à la première page
        await ChargerDonnees();
    }

    private async Task ReinitialiserFiltres()
    {
        Filtres = new FiltresAudit();
        TypeActionSelectionne = "";
        await ChargerDonnees();
    }

    private async Task ChangerPage(int nouvellePage)
    {
        Filtres.Page = nouvellePage;
        await ChargerDonnees();
    }

    private async Task ActualiserDonnees()
    {
        await ChargerDonnees();
        await MostrarNotificacion("success", "Données actualisées avec succès");
    }

    private async Task ExporterAudits()
    {
        try
        {
            var donnees = await ServiceAudit.ExporterAuditsAsync(Filtres, FormatExport.Excel);
            if (donnees.Length > 0)
            {
                await JSRuntime.InvokeVoidAsync("downloadFile", "audit-export.xlsx", Convert.ToBase64String(donnees));
                await MostrarNotificacion("success", "Export réalisé avec succès");
            }
            else
            {
                await MostrarNotificacion("warning", "Aucune donnée à exporter");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'export des données d'audit");
            await MostrarNotificacion("error", "Erreur lors de l'export");
        }
    }

    private async Task VoirDetails(EntreeAudit entree)
    {
        // TODO: Implémenter modal de détails
        await MostrarNotificacion("info", $"Détails de l'événement: {entree.Action}");
    }

    private string GetTypeActionDisplay(TypeActionAudit type)
    {
        return type switch
        {
            TypeActionAudit.Creation => "Création",
            TypeActionAudit.Lecture => "Lecture",
            TypeActionAudit.Modification => "Modification",
            TypeActionAudit.Suppression => "Suppression",
            TypeActionAudit.Connexion => "Connexion",
            TypeActionAudit.Deconnexion => "Déconnexion",
            TypeActionAudit.EchecConnexion => "Échec connexion",
            TypeActionAudit.DetectionAnomalie => "Anomalie",
            TypeActionAudit.ViolationPolitique => "Violation",
            _ => type.ToString()
        };
    }

    private string GetTypeActionBadgeClass(TypeActionAudit type)
    {
        return type switch
        {
            TypeActionAudit.Creation => "bg-success",
            TypeActionAudit.Lecture => "bg-info",
            TypeActionAudit.Modification => "bg-warning",
            TypeActionAudit.Suppression => "bg-danger",
            TypeActionAudit.Connexion => "bg-primary",
            TypeActionAudit.Deconnexion => "bg-secondary",
            TypeActionAudit.EchecConnexion => "bg-danger",
            TypeActionAudit.DetectionAnomalie => "bg-warning",
            TypeActionAudit.ViolationPolitique => "bg-danger",
            _ => "bg-light text-dark"
        };
    }

    private string GetGraviteDisplay(NiveauGraviteAudit gravite)
    {
        return gravite switch
        {
            NiveauGraviteAudit.Trace => "Trace",
            NiveauGraviteAudit.Debug => "Debug",
            NiveauGraviteAudit.Information => "Info",
            NiveauGraviteAudit.Avertissement => "Attention",
            NiveauGraviteAudit.Erreur => "Erreur",
            NiveauGraviteAudit.Critique => "Critique",
            NiveauGraviteAudit.Securite => "Sécurité",
            NiveauGraviteAudit.Conformite => "Conformité",
            _ => gravite.ToString()
        };
    }

    private string GetGraviteBadgeClass(NiveauGraviteAudit gravite)
    {
        return gravite switch
        {
            NiveauGraviteAudit.Trace => "bg-light text-dark",
            NiveauGraviteAudit.Debug => "bg-light text-dark",
            NiveauGraviteAudit.Information => "bg-info",
            NiveauGraviteAudit.Avertissement => "bg-warning",
            NiveauGraviteAudit.Erreur => "bg-danger",
            NiveauGraviteAudit.Critique => "bg-danger",
            NiveauGraviteAudit.Securite => "bg-dark",
            NiveauGraviteAudit.Conformite => "bg-success",
            _ => "bg-secondary"
        };
    }

    private async Task MostrarNotificacion(string tipo, string mensaje)
    {
        try
        {
            switch (tipo.ToLower())
            {
                case "success":
                    await JSRuntime.InvokeVoidAsync("afficherNotificationSucces", mensaje);
                    break;
                case "error":
                    await JSRuntime.InvokeVoidAsync("afficherNotificationErreur", mensaje);
                    break;
                case "warning":
                    await JSRuntime.InvokeVoidAsync("afficherNotificationAvertissement", mensaje);
                    break;
                case "info":
                    await JSRuntime.InvokeVoidAsync("afficherNotificationInfo", mensaje);
                    break;
                default:
                    await JSRuntime.InvokeVoidAsync("afficherNotificationInfo", mensaje);
                    break;
            }
        }
        catch (Exception ex)
        {
            Logger.LogWarning(ex, "No se pudo mostrar la notificación JavaScript: {Mensaje}", mensaje);
            Console.WriteLine($"Notification: {mensaje}");
        }
    }
}
