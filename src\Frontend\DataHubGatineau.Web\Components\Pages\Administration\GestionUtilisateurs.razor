@page "/administration/utilisateurs"
@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Components.Administration
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components.Forms
@inject IServiceGestionUtilisateurs ServiceGestionUtilisateurs
@inject ILogger<GestionUtilisateurs> Logger
@inject IJSRuntime JSRuntime

<PageTitle>Gestion des Utilisateurs - DataHub Gatineau</PageTitle>

<div class="container-fluid">
    <!-- En-tête de la page -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="bi bi-people"></i> Gestion des Utilisateurs</h2>
            <p class="text-muted mb-0">G<PERSON>rez les utilisateurs, leurs rôles et permissions dans la plateforme de gouvernance des données</p>
        </div>
        <div>
            <button class="btn btn-primary" @onclick="AfficherModalCreation">
                <i class="bi bi-person-plus"></i> Ajouter un Utilisateur
            </button>
            <button class="btn btn-outline-secondary ms-2" @onclick="ActualiserUtilisateurs">
                <i class="bi bi-arrow-clockwise"></i> Actualiser
            </button>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Utilisateurs</h6>
                            <h3 class="mb-0">@Utilisateurs.Count</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Utilisateurs Actifs</h6>
                            <h3 class="mb-0">@Utilisateurs.Count(u => u.EstActif)</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-check fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Utilisateurs Inactifs</h6>
                            <h3 class="mb-0">@Utilisateurs.Count(u => !u.EstActif)</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-person-x fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Rôles Disponibles</h6>
                            <h3 class="mb-0">@Roles.Count</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-shield-check fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">Rechercher</label>
                    <InputText class="form-control" placeholder="Nom, courriel, nom d'utilisateur..."
                               @bind-value="FiltreRecherche" @bind-value:event="oninput" />
                </div>
                <div class="col-md-3">
                    <label class="form-label">Statut</label>
                    <InputSelect class="form-select" @bind-value="FiltreStatut">
                        <option value="">Tous</option>
                        <option value="actif">Actifs seulement</option>
                        <option value="inactif">Inactifs seulement</option>
                    </InputSelect>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Niveau de rôle</label>
                    <InputSelect class="form-select" @bind-value="FiltreNiveau">
                        <option value="">Tous les niveaux</option>
                        <option value="Organisationnel">Organisationnel</option>
                        <option value="Catalogue/Domaine">Catalogue/Domaine</option>
                        <option value="Workflow">Workflow</option>
                        <option value="Utilisateur Final">Utilisateur Final</option>
                    </InputSelect>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Affichage</label>
                    <InputSelect class="form-select" @bind-value="ModeAffichage">
                        <option value="tableau">Tableau</option>
                        <option value="cartes">Cartes</option>
                    </InputSelect>
                </div>
            </div>
        </div>
    </div>

    <!-- Affichage des utilisateurs -->
    @if (EstEnChargement)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-2">Chargement des utilisateurs...</p>
        </div>
    }
    else if (UtilisateursFiltres.Any())
    {
        @if (ModeAffichage == "tableau")
        {
            <!-- Vue tableau -->
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Utilisateur</th>
                                    <th>Courriel</th>
                                    <th>Rôles</th>
                                    <th>Statut</th>
                                    <th>Dernière connexion</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var utilisateur in UtilisateursFiltres)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2">
                                                    @utilisateur.Prenom.FirstOrDefault()@utilisateur.Nom.FirstOrDefault()
                                                </div>
                                                <div>
                                                    <strong>@utilisateur.NomComplet</strong><br>
                                                    <small class="text-muted">@utilisateur.NomUtilisateur</small>
                                                    @if (!string.IsNullOrEmpty(utilisateur.Telephone))
                                                    {
                                                        <br><small class="text-muted"><i class="fas fa-phone me-1"></i>@utilisateur.Telephone @(!string.IsNullOrEmpty(utilisateur.Poste) ? $"poste {utilisateur.Poste}" : "")</small>
                                                    }
                                                </div>
                                            </div>
                                        </td>
                                        <td>@utilisateur.Courriel</td>
                                        <td>
                                            @foreach (var role in utilisateur.Roles.Take(2))
                                            {
                                                <span class="badge <EMAIL> me-1">@role.Nom</span>
                                            }
                                            @if (utilisateur.Roles.Count > 2)
                                            {
                                                <span class="badge bg-light text-dark">+@(utilisateur.Roles.Count - 2)</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-@(utilisateur.EstActif ? "success" : "secondary")">
                                                @(utilisateur.EstActif ? "Actif" : "Inactif")
                                            </span>
                                        </td>
                                        <td>
                                            @if (utilisateur.DerniereConnexion.HasValue)
                                            {
                                                <span>@utilisateur.DerniereConnexion.Value.ToString("dd/MM/yyyy")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Jamais</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" @onclick="@(() => AfficherDetailsUtilisateur(utilisateur))">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-secondary" @onclick="@(() => AfficherModalModification(utilisateur))">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-outline-@(utilisateur.EstActif ? "warning" : "success")" @onclick="@(() => BasculerActivationUtilisateur(utilisateur))">
                                                    <i class="bi bi-@(utilisateur.EstActif ? "pause" : "play")"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" @onclick="@(() => ConfirmerSuppressionUtilisateur(utilisateur))">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        }
        else
        {
            <!-- Vue cartes -->
            <div class="row">
                @foreach (var utilisateur in UtilisateursFiltres)
                {
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="avatar-circle me-3">
                                        @utilisateur.Prenom.FirstOrDefault()@utilisateur.Nom.FirstOrDefault()
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="card-title mb-1">@utilisateur.NomComplet</h6>
                                        <small class="text-muted">@utilisateur.NomUtilisateur</small>
                                    </div>
                                    <span class="badge bg-@(utilisateur.EstActif ? "success" : "secondary")">
                                        @(utilisateur.EstActif ? "Actif" : "Inactif")
                                    </span>
                                </div>
                                
                                <p class="card-text">
                                    <i class="bi bi-envelope me-1"></i> @utilisateur.Courriel
                                </p>

                                @if (!string.IsNullOrEmpty(utilisateur.Telephone))
                                {
                                    <p class="card-text">
                                        <i class="fas fa-phone me-1"></i> @utilisateur.Telephone @(!string.IsNullOrEmpty(utilisateur.Poste) ? $"poste {utilisateur.Poste}" : "")
                                    </p>
                                }
                                
                                <div class="mb-3">
                                    <small class="text-muted">Rôles:</small><br>
                                    @foreach (var role in utilisateur.Roles)
                                    {
                                        <span class="badge <EMAIL> me-1 mb-1">@role.Nom</span>
                                    }
                                </div>
                                
                                @if (utilisateur.DerniereConnexion.HasValue)
                                {
                                    <small class="text-muted">
                                        <i class="bi bi-clock me-1"></i> Dernière connexion: @utilisateur.DerniereConnexion.Value.ToString("dd/MM/yyyy")
                                    </small>
                                }
                                else
                                {
                                    <small class="text-muted">
                                        <i class="bi bi-clock me-1"></i> Jamais connecté
                                    </small>
                                }
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100">
                                    <button class="btn btn-outline-primary btn-sm" @onclick="@(() => AfficherDetailsUtilisateur(utilisateur))">
                                        <i class="bi bi-eye"></i> Détails
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" @onclick="@(() => AfficherModalModification(utilisateur))">
                                        <i class="bi bi-pencil"></i> Modifier
                                    </button>
                                    <button class="btn btn-outline-@(utilisateur.EstActif ? "warning" : "success") btn-sm" @onclick="@(() => BasculerActivationUtilisateur(utilisateur))">
                                        <i class="bi bi-@(utilisateur.EstActif ? "pause" : "play")"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
    }
    else
    {
        <div class="text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h4 class="mt-3">Aucun utilisateur trouvé</h4>
            <p class="text-muted">Aucun utilisateur ne correspond aux critères de recherche.</p>
            <button class="btn btn-primary" @onclick="AfficherModalCreation">
                <i class="bi bi-person-plus"></i> Ajouter le premier utilisateur
            </button>
        </div>
    }
</div>

<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #007bff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }
</style>

@code {
    // =============================================
    // PROPRIÉTÉS ET VARIABLES
    // =============================================

    private List<UtilisateurAvecRoles> Utilisateurs { get; set; } = new();
    private List<UtilisateurAvecRoles> UtilisateursFiltres { get; set; } = new();
    private List<InfoRole> Roles { get; set; } = new();

    private bool EstEnChargement { get; set; } = true;

    // Filtres
    private string _filtreRecherche = string.Empty;
    private string FiltreRecherche
    {
        get => _filtreRecherche;
        set
        {
            _filtreRecherche = value;
            FiltrerUtilisateurs();
        }
    }

    private string _filtreStatut = string.Empty;
    private string FiltreStatut
    {
        get => _filtreStatut;
        set
        {
            _filtreStatut = value;
            FiltrerUtilisateurs();
        }
    }

    private string _filtreNiveau = string.Empty;
    private string FiltreNiveau
    {
        get => _filtreNiveau;
        set
        {
            _filtreNiveau = value;
            FiltrerUtilisateurs();
        }
    }

    private string _modeAffichage = "tableau";
    private string ModeAffichage
    {
        get => _modeAffichage;
        set
        {
            _modeAffichage = value;
            StateHasChanged();
        }
    }

    // Modals et sélections
    private UtilisateurAvecRoles? UtilisateurSelectionne { get; set; }
    private bool AfficherModalCreationUtilisateur { get; set; } = false;
    private bool AfficherModalModificationUtilisateur { get; set; } = false;
    private bool AfficherModalDetailsUtilisateur { get; set; } = false;

    // Références aux nouveaux modales
    private ModalEditionUtilisateur ModalEditionUtilisateur { get; set; } = null!;
    private ModalSuppressionUtilisateur ModalSuppressionUtilisateur { get; set; } = null!;

    // =============================================
    // CYCLE DE VIE DU COMPOSANT
    // =============================================

    protected override async Task OnInitializedAsync()
    {
        try
        {
            await ChargerDonnees();
        }
        catch (Exception ex)
        {
            // Si hay un error en la inicialización, puede estar bloqueando el renderizado
            await JSRuntime.InvokeVoidAsync("console.error", "Error en OnInitializedAsync:", ex.Message);
        }
    }

    // =============================================
    // MÉTHODES DE CHARGEMENT
    // =============================================

    private async Task ChargerDonnees()
    {
        try
        {
            EstEnChargement = true;
            StateHasChanged();

            // Charger utilisateurs et rôles en parallèle
            var tacheUtilisateurs = ServiceGestionUtilisateurs.ObtenirTousUtilisateursAsync();
            var tacheRoles = ServiceGestionUtilisateurs.ObtenirTousRolesAsync();

            await Task.WhenAll(tacheUtilisateurs, tacheRoles);

            Utilisateurs = await tacheUtilisateurs;
            Roles = await tacheRoles;

            FiltrerUtilisateurs();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du chargement des données");
            await MostrarNotificacion("error", "Erreur lors du chargement des données");
        }
        finally
        {
            EstEnChargement = false;
            StateHasChanged();
        }
    }

    private async Task ActualiserUtilisateurs()
    {
        await ChargerDonnees();
        await MostrarNotificacion("success", "Données actualisées avec succès");
    }

    // =============================================
    // MÉTHODES DE FILTRAGE
    // =============================================

    private void FiltrerUtilisateurs()
    {
        var utilisateursFiltres = Utilisateurs.AsEnumerable();

        // Filtre par recherche
        if (!string.IsNullOrWhiteSpace(FiltreRecherche))
        {
            var recherche = FiltreRecherche.ToLower();
            utilisateursFiltres = utilisateursFiltres.Where(u =>
                u.NomComplet.ToLower().Contains(recherche) ||
                u.Courriel.ToLower().Contains(recherche) ||
                u.NomUtilisateur.ToLower().Contains(recherche) ||
                u.RolesTexte.ToLower().Contains(recherche)
            );
        }

        // Filtre par statut
        if (!string.IsNullOrWhiteSpace(FiltreStatut))
        {
            utilisateursFiltres = FiltreStatut switch
            {
                "actif" => utilisateursFiltres.Where(u => u.EstActif),
                "inactif" => utilisateursFiltres.Where(u => !u.EstActif),
                _ => utilisateursFiltres
            };
        }

        // Filtre par niveau de rôle
        if (!string.IsNullOrWhiteSpace(FiltreNiveau))
        {
            utilisateursFiltres = utilisateursFiltres.Where(u =>
                u.Roles.Any(r => r.Niveau == FiltreNiveau)
            );
        }

        UtilisateursFiltres = utilisateursFiltres.OrderBy(u => u.NomComplet).ToList();
        StateHasChanged();
    }



    // =============================================
    // MÉTHODES D'ACTIONS UTILISATEUR
    // =============================================

    private void AfficherModalCreation()
    {
        AfficherModalCreationUtilisateur = true;
        StateHasChanged();
    }

    private async Task AfficherModalModification(UtilisateurAvecRoles utilisateur)
    {
        try
        {
            if (ModalEditionUtilisateur != null)
            {
                await ModalEditionUtilisateur.OuvrirModal(utilisateur);
            }
            else
            {
                // Fallback au modal ancien si le nouveau n'est pas disponible
                UtilisateurSelectionne = utilisateur;
                AfficherModalModificationUtilisateur = true;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors de l'ouverture du modal de modification");
            try
            {
                await JSRuntime.InvokeVoidAsync("afficherNotificationErreur", "Erreur lors de l'ouverture du modal de modification");
            }
            catch
            {
                // Fallback si la fonction JavaScript n'est pas disponible
                Console.WriteLine("Erreur lors de l'ouverture du modal de modification");
            }
        }
    }

    private void AfficherDetailsUtilisateur(UtilisateurAvecRoles utilisateur)
    {
        UtilisateurSelectionne = utilisateur;
        AfficherModalDetailsUtilisateur = true;
    }

    private async Task BasculerActivationUtilisateur(UtilisateurAvecRoles utilisateur)
    {
        try
        {
            var succes = await ServiceGestionUtilisateurs.BasculerActivationUtilisateurAsync(utilisateur.Id);
            if (succes)
            {
                utilisateur.EstActif = !utilisateur.EstActif;
                var action = utilisateur.EstActif ? "activé" : "désactivé";
                await MostrarNotificacion("success", $"Utilisateur {action} avec succès");
                StateHasChanged();
            }
            else
            {
                await MostrarNotificacion("error", "Erreur lors du changement de statut");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Erreur lors du changement de statut");
            await MostrarNotificacion("error", "Erreur lors du changement de statut");
        }
    }

    private async Task ConfirmerSuppressionUtilisateur(UtilisateurAvecRoles utilisateur)
    {
        await ModalSuppressionUtilisateur.OuvrirModal(utilisateur);
    }

    // =============================================
    // MÉTHODES DE CALLBACK POUR LES MODALS
    // =============================================

    private async Task OnUtilisateurCree(UtilisateurAvecRoles nouvelUtilisateur)
    {
        Utilisateurs.Add(nouvelUtilisateur);
        FiltrerUtilisateurs();
        AfficherModalCreationUtilisateur = false;
        await MostrarNotificacion("success", "Utilisateur créé avec succès");
    }

    private async Task OnUtilisateurModifie()
    {
        await ChargerDonnees();
    }

    private async Task OnUtilisateurSupprime()
    {
        await ChargerDonnees();
    }

    private void OnModalFerme()
    {
        AfficherModalCreationUtilisateur = false;
        AfficherModalModificationUtilisateur = false;
        AfficherModalDetailsUtilisateur = false;
        UtilisateurSelectionne = null;
    }

    /// <summary>
    /// Fonction helper pour afficher les notifications de manière robuste
    /// </summary>
    private async Task MostrarNotificacion(string tipo, string mensaje)
    {
        try
        {
            switch (tipo.ToLower())
            {
                case "success":
                    await JSRuntime.InvokeVoidAsync("afficherNotificationSucces", mensaje);
                    break;
                case "error":
                    await JSRuntime.InvokeVoidAsync("afficherNotificationErreur", mensaje);
                    break;
                case "warning":
                    await JSRuntime.InvokeVoidAsync("afficherNotificationAvertissement", mensaje);
                    break;
                case "info":
                    await JSRuntime.InvokeVoidAsync("afficherNotificationInfo", mensaje);
                    break;
                default:
                    await JSRuntime.InvokeVoidAsync("afficherNotificationInfo", mensaje);
                    break;
            }
        }
        catch (Exception ex)
        {
            // Fallback si las funciones JavaScript no están disponibles
            Logger.LogWarning(ex, "No se pudo mostrar la notificación JavaScript: {Mensaje}", mensaje);
            Console.WriteLine($"Notification: {mensaje}");
        }
    }
}

<!-- Modals -->
<ModalCreationUtilisateur AfficherModal="AfficherModalCreationUtilisateur"
                         OnUtilisateurCree="OnUtilisateurCree"
                         OnModalFermee="OnModalFerme" />

<ModalEditionUtilisateur @ref="ModalEditionUtilisateur"
                        OnUtilisateurModifie="OnUtilisateurModifie" />

<ModalSuppressionUtilisateur @ref="ModalSuppressionUtilisateur"
                            OnUtilisateurSupprime="OnUtilisateurSupprime" />

<ModalDetailsUtilisateur AfficherModal="AfficherModalDetailsUtilisateur"
                        Utilisateur="UtilisateurSelectionne"
                        OnModalFermee="OnModalFerme"
                        OnModifierUtilisateur="AfficherModalModification"
                        OnBasculerActivation="BasculerActivationUtilisateur" />
