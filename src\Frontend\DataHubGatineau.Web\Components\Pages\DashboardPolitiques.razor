@page "/politiques/dashboard"
@using DataHubGatineau.Web.Models.Policy
@using DataHubGatineau.Web.Services.Interfaces
@inject IPolitiqueService PolitiqueService
@inject IConformitePolitiqueService ConformitePolitiqueService
@inject NavigationManager NavigationManager

<PageTitle>Tableau de bord des politiques - DataHub Gatineau</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Tableau de bord des politiques</h1>
    <div>
        <a href="/politiques" class="btn btn-outline-secondary me-2">
            <i class="bi bi-arrow-left"></i> Retour aux politiques
        </a>
        <a href="/politiques/conformite" class="btn btn-outline-primary">
            <i class="bi bi-clipboard-check"></i> Conformité
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            Ce tableau de bord présente une vue d'ensemble des politiques et de la conformité dans l'organisation.
            Il permet de suivre l'état des politiques, leur adoption et leur impact sur la gouvernance des données.
        </div>
    </div>
</div>

@if (_loading)
{
    <div class="d-flex justify-content-center my-5">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else
{
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="card-body">
                    <div class="stats-number">@_politiques.Count()</div>
                    <div class="stats-label">Politiques totales</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card success">
                <div class="card-body">
                    <div class="stats-number">@_politiques.Count(p => p.Statut == StatutPolitique.Active)</div>
                    <div class="stats-label">Politiques actives</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card warning">
                <div class="card-body">
                    <div class="stats-number">@_politiques.Count(p => p.Statut == StatutPolitique.EnRevision || p.Statut == StatutPolitique.Brouillon)</div>
                    <div class="stats-label">Politiques en cours</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card danger">
                <div class="card-body">
                    <div class="stats-number">@_politiquesAReviser.Count</div>
                    <div class="stats-label">Politiques à réviser</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Politiques par type</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Nombre</th>
                                    <th>Actives</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var type in _politiquesParType.OrderByDescending(p => p.Value))
                                {
                                    <tr>
                                        <td>@GetTypePolitiqueLabel(type.Key)</td>
                                        <td>@type.Value</td>
                                        <td>@_politiquesActivesParType[type.Key]</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Politiques par niveau d'application</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Niveau</th>
                                    <th>Nombre</th>
                                    <th>Actives</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var niveau in _politiquesParNiveau.OrderByDescending(p => p.Value))
                                {
                                    <tr>
                                        <td>@GetNiveauApplicationLabel(niveau.Key)</td>
                                        <td>@niveau.Value</td>
                                        <td>@_politiquesActivesParNiveau[niveau.Key]</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Conformité globale</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center mb-3">
                                <div class="conformity-score @GetScoreClass(_scoreGlobalMoyen)">
                                    @_scoreGlobalMoyen.ToString("F1")
                                </div>
                                <div class="mt-2">Score moyen de conformité</div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card stats-card success">
                                        <div class="card-body">
                                            <div class="stats-number">@_conformitesParStatut["Conforme"]</div>
                                            <div class="stats-label">Conformes</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card stats-card warning">
                                        <div class="card-body">
                                            <div class="stats-number">@_conformitesParStatut["Partiellement conforme"]</div>
                                            <div class="stats-label">Partiellement conformes</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card stats-card danger">
                                        <div class="card-body">
                                            <div class="stats-number">@_conformitesParStatut["Non conforme"]</div>
                                            <div class="stats-label">Non conformes</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <h6>Conformité par type d'entité</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Type d'entité</th>
                                                <th>Score moyen</th>
                                                <th>Nombre</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var typeEntite in _conformitesParTypeEntite)
                                            {
                                                <tr>
                                                    <td>@GetTypeEntiteLabel(typeEntite.Key)</td>
                                                    <td>
                                                        <div class="progress" style="height: 15px;">
                                                            <div class="progress-bar @GetScoreColorClass(typeEntite.Value.ScoreMoyen)" 
                                                                 role="progressbar" 
                                                                 style="width: @typeEntite.Value.ScoreMoyen%;" 
                                                                 aria-valuenow="@typeEntite.Value.ScoreMoyen" 
                                                                 aria-valuemin="0" 
                                                                 aria-valuemax="100">
                                                                @typeEntite.Value.ScoreMoyen.ToString("F1")
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>@typeEntite.Value.Nombre</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Politiques à réviser</h5>
                </div>
                <div class="card-body">
                    @if (!_politiquesAReviser.Any())
                    {
                        <div class="alert alert-success">
                            Aucune politique à réviser pour le moment.
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Titre</th>
                                        <th>Type</th>
                                        <th>Version</th>
                                        <th>Date de révision</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var politique in _politiquesAReviser)
                                    {
                                        <tr>
                                            <td>@politique.Code</td>
                                            <td>@politique.Titre</td>
                                            <td>@GetTypePolitiqueLabel(politique.Type)</td>
                                            <td>@politique.Version</td>
                                            <td>
                                                @(politique.DateProchaineRevision.HasValue ? politique.DateProchaineRevision.Value.ToString("dd/MM/yyyy") : "-")
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="/politiques/@politique.Id" class="btn btn-sm btn-info">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="/politiques/modifier/@politique.Id" class="btn btn-sm btn-warning">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

@code {
    private IEnumerable<Politique> _politiques = Enumerable.Empty<Politique>();
    private List<Politique> _politiquesAReviser = new();
    private Dictionary<TypePolitique, int> _politiquesParType = new();
    private Dictionary<TypePolitique, int> _politiquesActivesParType = new();
    private Dictionary<NiveauApplicationPolitique, int> _politiquesParNiveau = new();
    private Dictionary<NiveauApplicationPolitique, int> _politiquesActivesParNiveau = new();
    private Dictionary<string, int> _conformitesParStatut = new();
    private Dictionary<string, ConformiteTypeEntite> _conformitesParTypeEntite = new();
    private double _scoreGlobalMoyen = 0;
    private bool _loading = true;

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    private async Task ChargerDonnees()
    {
        _loading = true;

        try
        {
            // Charger les politiques
            _politiques = await PolitiqueService.ObtenirTousAsync();
            
            // Identifier les politiques à réviser
            var aujourdhui = DateTime.Now;
            _politiquesAReviser = _politiques
                .Where(p => p.Statut == StatutPolitique.Active && 
                           p.DateProchaineRevision.HasValue && 
                           p.DateProchaineRevision.Value <= aujourdhui.AddDays(30))
                .OrderBy(p => p.DateProchaineRevision)
                .ToList();
            
            // Calculer les statistiques par type
            _politiquesParType = _politiques
                .GroupBy(p => p.Type)
                .ToDictionary(g => g.Key, g => g.Count());
            
            _politiquesActivesParType = _politiques
                .Where(p => p.Statut == StatutPolitique.Active)
                .GroupBy(p => p.Type)
                .ToDictionary(g => g.Key, g => g.Count());
            
            // Ajouter les types manquants
            foreach (var type in Enum.GetValues<TypePolitique>())
            {
                if (!_politiquesParType.ContainsKey(type))
                {
                    _politiquesParType[type] = 0;
                }
                
                if (!_politiquesActivesParType.ContainsKey(type))
                {
                    _politiquesActivesParType[type] = 0;
                }
            }
            
            // Calculer les statistiques par niveau d'application
            _politiquesParNiveau = _politiques
                .GroupBy(p => p.NiveauApplication)
                .ToDictionary(g => g.Key, g => g.Count());
            
            _politiquesActivesParNiveau = _politiques
                .Where(p => p.Statut == StatutPolitique.Active)
                .GroupBy(p => p.NiveauApplication)
                .ToDictionary(g => g.Key, g => g.Count());
            
            // Ajouter les niveaux manquants
            foreach (var niveau in Enum.GetValues<NiveauApplicationPolitique>())
            {
                if (!_politiquesParNiveau.ContainsKey(niveau))
                {
                    _politiquesParNiveau[niveau] = 0;
                }
                
                if (!_politiquesActivesParNiveau.ContainsKey(niveau))
                {
                    _politiquesActivesParNiveau[niveau] = 0;
                }
            }
            
            // Charger les statistiques de conformité
            var statistiquesConformite = await ConformitePolitiqueService.ObtenirStatistiquesAsync();
            
            // Extraire les statistiques par statut
            _conformitesParStatut["Conforme"] = Convert.ToInt32(statistiquesConformite["Conformes"]);
            _conformitesParStatut["Partiellement conforme"] = Convert.ToInt32(statistiquesConformite["Partiellement conformes"]);
            _conformitesParStatut["Non conforme"] = Convert.ToInt32(statistiquesConformite["Non conformes"]);
            
            // Extraire le score moyen global
            _scoreGlobalMoyen = Convert.ToDouble(statistiquesConformite["Score moyen global"]);
            
            // Extraire les statistiques par type d'entité
            if (statistiquesConformite.TryGetValue("Par type d'entité", out var statsParTypeEntite) && 
                statsParTypeEntite is Dictionary<string, object> statsParTypeEntiteDict)
            {
                foreach (var kvp in statsParTypeEntiteDict)
                {
                    if (kvp.Value is Dictionary<string, object> statsTypeEntite)
                    {
                        _conformitesParTypeEntite[kvp.Key] = new ConformiteTypeEntite
                        {
                            Nombre = Convert.ToInt32(statsTypeEntite["Nombre"]),
                            ScoreMoyen = Convert.ToDouble(statsTypeEntite["ScoreMoyen"])
                        };
                    }
                }
            }
        }
        finally
        {
            _loading = false;
        }
    }

    private string GetTypePolitiqueLabel(TypePolitique type)
    {
        return type switch
        {
            TypePolitique.Gouvernance => "Gouvernance",
            TypePolitique.Qualite => "Qualité",
            TypePolitique.Securite => "Sécurité",
            TypePolitique.Confidentialite => "Confidentialité",
            TypePolitique.Conservation => "Conservation",
            TypePolitique.Acces => "Accès",
            TypePolitique.Partage => "Partage",
            TypePolitique.StandardTechnique => "Standard technique",
            TypePolitique.StandardMetier => "Standard métier",
            TypePolitique.Autre => "Autre",
            _ => type.ToString()
        };
    }

    private string GetNiveauApplicationLabel(NiveauApplicationPolitique niveau)
    {
        return niveau switch
        {
            NiveauApplicationPolitique.Organisation => "Organisation",
            NiveauApplicationPolitique.Departement => "Département",
            NiveauApplicationPolitique.Projet => "Projet",
            NiveauApplicationPolitique.Domaine => "Domaine",
            NiveauApplicationPolitique.Actif => "Actif de données",
            NiveauApplicationPolitique.Systeme => "Système",
            _ => niveau.ToString()
        };
    }

    private string GetTypeEntiteLabel(string typeEntite)
    {
        return typeEntite switch
        {
            "ActifDonnees" => "Actif de données",
            "Departement" => "Département",
            "Projet" => "Projet",
            _ => typeEntite
        };
    }

    private string GetScoreClass(double score)
    {
        return score switch
        {
            >= 90 => "high",
            >= 70 => "medium",
            _ => "low"
        };
    }

    private string GetScoreColorClass(double score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 70 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private class ConformiteTypeEntite
    {
        public int Nombre { get; set; }
        public double ScoreMoyen { get; set; }
    }
}

