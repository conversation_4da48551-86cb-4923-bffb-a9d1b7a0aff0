@page "/politiques/{Id:guid}"
@using DataHubGatineau.Web.Models.Policy
@using DataHubGatineau.Web.Services.Interfaces
@using System.Text.Json
@using Microsoft.JSInterop
@inject IPolitiqueService PolitiqueService
@inject IConformitePolitiqueService ConformitePolitiqueService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Détail de la politique - DataHub Gatineau</PageTitle>

@if (_loading)
{
    <div class="d-flex justify-content-center my-5">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else if (_politique == null)
{
    <div class="alert alert-danger">
        <h4 class="alert-heading">Politique non trouvée</h4>
        <p>La politique demandée n'existe pas ou a été supprimée.</p>
        <hr>
        <p class="mb-0">
            <a href="/politiques" class="btn btn-primary">Retour à la liste des politiques</a>
        </p>
    </div>
}
else
{
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>@_politique.Titre</h1>
        <div>
            <a href="/politiques" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left"></i> Retour
            </a>
            <div class="btn-group" role="group">
                <a href="/politiques/modifier/@_politique.Id" class="btn btn-warning">
                    <i class="bi bi-pencil"></i> Modifier
                </a>
                <button class="btn btn-primary" @onclick="ExporterPDF">
                    <i class="bi bi-file-earmark-pdf"></i> Exporter en PDF
                </button>
                @if (_politique.Statut == StatutPolitique.Brouillon || _politique.Statut == StatutPolitique.EnRevision)
                {
                    <button class="btn btn-success" @onclick="() => ApprouverPolitique(_politique.Id)">
                        <i class="bi bi-check-lg"></i> Approuver
                    </button>
                }
                @if (_politique.Statut == StatutPolitique.Active)
                {
                    <button class="btn btn-secondary" @onclick="() => ArchiverPolitique(_politique.Id)">
                        <i class="bi bi-archive"></i> Archiver
                    </button>
                    <button class="btn btn-info" @onclick="() => CreerNouvelleVersion(_politique.Id)">
                        <i class="bi bi-plus-square"></i> Nouvelle version
                    </button>
                }
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Informations générales</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Code:</div>
                        <div class="col-md-9">@_politique.Code</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Type:</div>
                        <div class="col-md-9">@GetTypePolitiqueLabel(_politique.Type)</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Version:</div>
                        <div class="col-md-9">@_politique.Version</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Statut:</div>
                        <div class="col-md-9">
                            <span class="badge @GetStatutBadgeClass(_politique.Statut)">
                                @GetStatutPolitiqueLabel(_politique.Statut)
                            </span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Niveau d'application:</div>
                        <div class="col-md-9">@GetNiveauApplicationLabel(_politique.NiveauApplication)</div>
                    </div>
                    @if (!string.IsNullOrEmpty(_politique.EntiteApplicationNom))
                    {
                        <div class="row mb-3">
                            <div class="col-md-3 fw-bold">Entité d'application:</div>
                            <div class="col-md-9">@_politique.EntiteApplicationNom</div>
                        </div>
                    }
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Date d'entrée en vigueur:</div>
                        <div class="col-md-9">
                            @(_politique.DateEntreeVigueur.HasValue ? _politique.DateEntreeVigueur.Value.ToString("dd/MM/yyyy") : "Non définie")
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Date d'expiration:</div>
                        <div class="col-md-9">
                            @(_politique.DateExpiration.HasValue ? _politique.DateExpiration.Value.ToString("dd/MM/yyyy") : "Non définie")
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Prochaine révision:</div>
                        <div class="col-md-9">
                            @(_politique.DateProchaineRevision.HasValue ? _politique.DateProchaineRevision.Value.ToString("dd/MM/yyyy") : "Non définie")
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Mots-clés:</div>
                        <div class="col-md-9">
                            @if (!string.IsNullOrEmpty(_politique.MotsCles))
                            {
                                @foreach (var motCle in _politique.MotsCles.Split(',', StringSplitOptions.RemoveEmptyEntries))
                                {
                                    <span class="badge bg-info me-1">@motCle.Trim()</span>
                                }
                            }
                            else
                            {
                                <span>Aucun mot-clé</span>
                            }
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Description:</div>
                        <div class="col-md-9">@(_politique.Description ?? "Aucune description")</div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5>Contenu de la politique</h5>
                </div>
                <div class="card-body">
                    <div class="markdown-content">
                        @((MarkupString)_markdownContent)
                    </div>
                </div>
            </div>

            @if (_versions != null && _versions.Any())
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Versions</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Version</th>
                                        <th>Statut</th>
                                        <th>Date de création</th>
                                        <th>Date d'approbation</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var version in _versions.OrderByDescending(v => v.Version))
                                    {
                                        <tr class="@(version.Id == _politique.Id ? "table-primary" : "")">
                                            <td>@version.Version</td>
                                            <td>
                                                <span class="badge @GetStatutBadgeClass(version.Statut)">
                                                    @GetStatutPolitiqueLabel(version.Statut)
                                                </span>
                                            </td>
                                            <td>@version.DateCreation.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                @(version.DateApprobation.HasValue ? version.DateApprobation.Value.ToString("dd/MM/yyyy") : "-")
                                            </td>
                                            <td>
                                                @if (version.Id != _politique.Id)
                                                {
                                                    <a href="/politiques/@version.Id" class="btn btn-sm btn-info">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-primary">Version courante</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Métadonnées</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">Créé par:</div>
                        <div class="col-md-7">@(_politique.CreeParNom ?? "Inconnu")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">Date de création:</div>
                        <div class="col-md-7">@_politique.DateCreation.ToString("dd/MM/yyyy")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">Modifié par:</div>
                        <div class="col-md-7">@(_politique.ModifieParNom ?? "Inconnu")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-5 fw-bold">Date de modification:</div>
                        <div class="col-md-7">@_politique.DateModification.ToString("dd/MM/yyyy")</div>
                    </div>
                    @if (!string.IsNullOrEmpty(_politique.ApprouveParNom))
                    {
                        <div class="row mb-3">
                            <div class="col-md-5 fw-bold">Approuvé par:</div>
                            <div class="col-md-7">@_politique.ApprouveParNom</div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-5 fw-bold">Date d'approbation:</div>
                            <div class="col-md-7">
                                @(_politique.DateApprobation.HasValue ? _politique.DateApprobation.Value.ToString("dd/MM/yyyy") : "-")
                            </div>
                        </div>
                    }
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5>Approbations</h5>
                </div>
                <div class="card-body">
                    @if (_approbations == null || !_approbations.Any())
                    {
                        <div class="alert alert-info">
                            Aucune approbation pour cette politique.
                        </div>
                    }
                    else
                    {
                        <div class="list-group">
                            @foreach (var approbation in _approbations.OrderByDescending(a => a.DateApprobation))
                            {
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">@approbation.ApprobateurNom</h6>
                                        <small>@approbation.DateApprobation.ToString("dd/MM/yyyy")</small>
                                    </div>
                                    <p class="mb-1">@approbation.Role</p>
                                    <small>@(approbation.Commentaires ?? "Aucun commentaire")</small>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5>Conformité</h5>
                </div>
                <div class="card-body">
                    @if (_conformites == null || !_conformites.Any())
                    {
                        <div class="alert alert-info">
                            Aucune évaluation de conformité pour cette politique.
                        </div>
                    }
                    else
                    {
                        <div class="mb-3">
                            <div class="fw-bold mb-2">Score moyen de conformité:</div>
                            <div class="progress" style="height: 25px;">
                                <div class="progress-bar @GetScoreColorClass(_scoreMoyenConformite)"
                                     role="progressbar"
                                     style="width: @_scoreMoyenConformite%;"
                                     aria-valuenow="@_scoreMoyenConformite"
                                     aria-valuemin="0"
                                     aria-valuemax="100">
                                    @_scoreMoyenConformite.ToString("F1")%
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="fw-bold mb-2">Statuts de conformité:</div>
                            <div class="d-flex justify-content-between">
                                <span>Conforme: @_conformites.Count(c => c.Statut == "Conforme")</span>
                                <span>Partiellement: @_conformites.Count(c => c.Statut == "Partiellement conforme")</span>
                                <span>Non conforme: @_conformites.Count(c => c.Statut == "Non conforme")</span>
                            </div>
                        </div>
                        <a href="/politiques/conformite?politiqueId=@_politique.Id" class="btn btn-outline-primary w-100">
                            Voir toutes les évaluations
                        </a>
                    }
                </div>
            </div>

            @if (!string.IsNullOrEmpty(_politique.HistoriqueModificationsJson))
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Historique des modifications</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            @foreach (var modification in _historiqueModifications)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-badge">
                                        <i class="bi bi-clock"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-date">@modification.Date</div>
                                        <div class="timeline-title">@modification.Action</div>
                                        <div class="timeline-description">Par @modification.Utilisateur</div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}

<Confirmation
    IsVisible="@_showApprouverConfirmation"
    Title="Confirmer l'approbation"
    Message="Êtes-vous sûr de vouloir approuver cette politique? Elle deviendra active et applicable."
    ConfirmText="Approuver"
    CancelText="Annuler"
    ConfirmButtonType="success"
    OnConfirm="ConfirmerApprobation"
    OnCancel="AnnulerApprobation" />

<Confirmation
    IsVisible="@_showArchiverConfirmation"
    Title="Confirmer l'archivage"
    Message="Êtes-vous sûr de vouloir archiver cette politique? Elle ne sera plus active."
    ConfirmText="Archiver"
    CancelText="Annuler"
    ConfirmButtonType="secondary"
    OnConfirm="ConfirmerArchivage"
    OnCancel="AnnulerArchivage" />

@code {
    [Parameter]
    public Guid Id { get; set; }

    private Politique? _politique;
    private IEnumerable<Politique>? _versions;
    private IEnumerable<ApprobationPolitique>? _approbations;
    private IEnumerable<ConformitePolitique>? _conformites;
    private List<HistoriqueModification> _historiqueModifications = new();
    private bool _loading = true;
    private string _markdownContent = string.Empty;
    private double _scoreMoyenConformite = 0;
    private bool _showApprouverConfirmation = false;
    private bool _showArchiverConfirmation = false;

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Id != Guid.Empty)
        {
            await ChargerDonnees();
        }
    }

    private async Task ChargerDonnees()
    {
        _loading = true;

        try
        {
            _politique = await PolitiqueService.ObtenirParIdAsync(Id);

            if (_politique != null)
            {
                // Convertir le contenu Markdown en HTML
                _markdownContent = ConvertirMarkdownEnHtml(_politique.Contenu);

                // Charger les versions
                _versions = await PolitiqueService.ObtenirVersionsAsync(Id);

                // Charger les approbations
                _approbations = await PolitiqueService.ObtenirApprobationsAsync(Id);

                // Charger les évaluations de conformité
                _conformites = await ConformitePolitiqueService.ObtenirParPolitiqueAsync(Id);

                // Calculer le score moyen de conformité
                if (_conformites != null && _conformites.Any(c => c.Score.HasValue))
                {
                    _scoreMoyenConformite = Math.Round(_conformites.Where(c => c.Score.HasValue).Average(c => c.Score.Value), 1);
                }

                // Charger l'historique des modifications
                if (!string.IsNullOrEmpty(_politique.HistoriqueModificationsJson))
                {
                    try
                    {
                        _historiqueModifications = JsonSerializer.Deserialize<List<HistoriqueModification>>(_politique.HistoriqueModificationsJson) ?? new List<HistoriqueModification>();
                    }
                    catch
                    {
                        _historiqueModifications = new List<HistoriqueModification>();
                    }
                }
            }
        }
        finally
        {
            _loading = false;
        }
    }



    private void ApprouverPolitique(Guid politiqueId)
    {
        _showApprouverConfirmation = true;
    }

    private void AnnulerApprobation()
    {
        _showApprouverConfirmation = false;
    }

    private async Task ConfirmerApprobation()
    {
        try
        {
            if (_politique != null)
            {
                var approbation = new ApprobationPolitique
                {
                    ApprobateurId = "user1", // Utilisateur actuel
                    ApprobateurNom = "Jean Dupont", // Nom de l'utilisateur actuel
                    Role = "Administrateur",
                    Commentaires = "Politique approuvée"
                };

                await PolitiqueService.ApprouverPolitiqueAsync(_politique.Id, approbation);
                await ChargerDonnees();
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'approbation: {ex.Message}");
            // TODO: Afficher un message d'erreur à l'utilisateur
        }
        finally
        {
            _showApprouverConfirmation = false;
        }
    }

    private void ArchiverPolitique(Guid politiqueId)
    {
        _showArchiverConfirmation = true;
    }

    private void AnnulerArchivage()
    {
        _showArchiverConfirmation = false;
    }

    private async Task ConfirmerArchivage()
    {
        if (_politique != null)
        {
            await PolitiqueService.ArchiverPolitiqueAsync(_politique.Id);
            _showArchiverConfirmation = false;
            await ChargerDonnees();
        }
    }

    private async Task CreerNouvelleVersion(Guid politiqueId)
    {
        if (_politique != null)
        {
            var nouvelleVersion = await PolitiqueService.CreerNouvelleVersionAsync(_politique.Id);
            NavigationManager.NavigateTo($"/politiques/modifier/{nouvelleVersion.Id}");
        }
    }

    private async Task ExporterPDF()
    {
        try
        {
            if (_politique != null)
            {
                // Utiliser l'endpoint du backend pour générer le PDF
                var url = $"http://localhost:5187/api/v1/politiques/{_politique.Id}/export-pdf";
                Console.WriteLine($"🔗 Ouverture de l'URL PDF: {url}");
                await JSRuntime.InvokeVoidAsync("open", url, "_blank");
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"❌ Erreur lors de l'export PDF: {ex.Message}");
        }
    }

    private string ConvertirMarkdownEnHtml(string markdown)
    {
        // Dans une implémentation réelle, on utiliserait une bibliothèque Markdown
        // Pour simplifier, on fait une conversion basique
        var html = markdown
            .Replace("\n\n", "<br><br>")
            .Replace("\n", "<br>")
            .Replace("# ", "<h2>").Replace(" #", "</h2>")
            .Replace("## ", "<h3>").Replace(" ##", "</h3>")
            .Replace("### ", "<h4>").Replace(" ###", "</h4>")
            .Replace("- ", "<li>").Replace(" -", "</li>");

        return html;
    }

    private string GetTypePolitiqueLabel(TypePolitique type)
    {
        return type switch
        {
            TypePolitique.Gouvernance => "Gouvernance",
            TypePolitique.Qualite => "Qualité",
            TypePolitique.Securite => "Sécurité",
            TypePolitique.Confidentialite => "Confidentialité",
            TypePolitique.Conservation => "Conservation",
            TypePolitique.Acces => "Accès",
            TypePolitique.Partage => "Partage",
            TypePolitique.StandardTechnique => "Standard technique",
            TypePolitique.StandardMetier => "Standard métier",
            TypePolitique.Autre => "Autre",
            _ => type.ToString()
        };
    }

    private string GetStatutPolitiqueLabel(StatutPolitique statut)
    {
        return statut switch
        {
            StatutPolitique.Brouillon => "Brouillon",
            StatutPolitique.EnRevision => "En révision",
            StatutPolitique.Active => "Active",
            StatutPolitique.Archivee => "Archivée",
            StatutPolitique.Obsolete => "Obsolète",
            _ => statut.ToString()
        };
    }

    private string GetNiveauApplicationLabel(NiveauApplication niveau)
    {
        return niveau switch
        {
            NiveauApplication.Organisation => "Organisation",
            NiveauApplication.Departement => "Département",
            NiveauApplication.Projet => "Projet",
            NiveauApplication.ActifDonnees => "Actif de données",
            NiveauApplication.Champ => "Champ",
            _ => niveau.ToString()
        };
    }

    private string GetStatutBadgeClass(StatutPolitique statut)
    {
        return statut switch
        {
            StatutPolitique.Brouillon => "bg-info",
            StatutPolitique.EnRevision => "bg-warning",
            StatutPolitique.Active => "bg-success",
            StatutPolitique.Archivee => "bg-secondary",
            StatutPolitique.Obsolete => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetScoreColorClass(double score)
    {
        return score switch
        {
            >= 90 => "bg-success",
            >= 70 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private class HistoriqueModification
    {
        public string Date { get; set; } = string.Empty;
        public string Utilisateur { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
    }
}

