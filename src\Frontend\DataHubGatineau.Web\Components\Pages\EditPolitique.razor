@page "/politiques/ajouter"
@page "/politiques/modifier/{Id:guid}"
@using DataHubGatineau.Web.Models.Policy
@using DataHubGatineau.Web.Services.Interfaces
@using System.Text.Json
@inject IPolitiqueService PolitiqueService
@inject IActifDonneesService ActifDonneesService
@inject NavigationManager NavigationManager


<PageTitle>@(_isNewPolitique ? "Ajouter une politique" : "Modifier la politique") - DataHub Gatineau</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>@(_isNewPolitique ? "Ajouter une politique" : "Modifier la politique")</h1>
    <div>
        <a href="@(_isNewPolitique ? "/politiques" : $"/politiques/{Id}")" class="btn btn-outline-secondary me-2">
            <i class="bi bi-arrow-left"></i> Annuler
        </a>
        <button class="btn btn-primary" @onclick="SauvegarderPolitique">
            <i class="bi bi-save"></i> Enregistrer
        </button>
    </div>
</div>

@if (_loading)
{
    <div class="d-flex justify-content-center my-5">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else
{
    <EditForm Model="@_politique" OnValidSubmit="SauvegarderPolitique">
        <DataAnnotationsValidator />
        <ValidationSummary class="text-danger mb-4" />

        <div class="row">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Informations générales</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="code" class="form-label">Code</label>
                                <InputText id="code" class="form-control" @bind-Value="@_politique.Code" />
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.Code)" />
                            </div>
                            <div class="col-md-6">
                                <label for="version" class="form-label">Version</label>
                                <InputText id="version" class="form-control" @bind-Value="@_politique.Version" />
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.Version)" />
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="titre" class="form-label">Titre</label>
                            <InputText id="titre" class="form-control" @bind-Value="@_politique.Titre" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.Titre)" />
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <InputTextArea id="description" class="form-control" rows="3" @bind-Value="@_politique.Description" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.Description)" />
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="type" class="form-label">Type</label>
                                <InputSelect id="type" class="form-select" @bind-Value="@_politique.Type">
                                    @foreach (var type in Enum.GetValues<TypePolitique>())
                                    {
                                        <option value="@type">@GetTypePolitiqueLabel(type)</option>
                                    }
                                </InputSelect>
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.Type)" />
                            </div>
                            <div class="col-md-6">
                                <label for="statut" class="form-label">Statut</label>
                                <InputSelect id="statut" class="form-select" @bind-Value="@_politique.Statut">
                                    @foreach (var statut in Enum.GetValues<StatutPolitique>())
                                    {
                                        <option value="@statut">@GetStatutPolitiqueLabel(statut)</option>
                                    }
                                </InputSelect>
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.Statut)" />
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="categorie" class="form-label">Domaine d'application *</label>
                                <InputSelect id="categorie" class="form-select" @bind-Value="@_politique.Categorie">
                                    <option value="">-- Sélectionner un domaine --</option>
                                    <option value="Données personnelles">Données personnelles</option>
                                    <option value="Données financières">Données financières</option>
                                    <option value="Données opérationnelles">Données opérationnelles</option>
                                    <option value="Données de recherche">Données de recherche</option>
                                    <option value="Données publiques">Données publiques</option>
                                    <option value="Données sensibles">Données sensibles</option>
                                    <option value="Données réglementées">Données réglementées</option>
                                    <option value="Données critiques">Données critiques</option>
                                    <option value="Métadonnées">Métadonnées</option>
                                    <option value="Données de référence">Données de référence</option>
                                </InputSelect>
                                <small class="form-text text-muted">Le domaine spécifie le type de données concernées par cette politique</small>
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.Categorie)" />
                            </div>
                            <div class="col-md-6">
                                <label for="proprietaire" class="form-label">Propriétaire *</label>
                                <InputText id="proprietaire" class="form-control" @bind-Value="@_politique.Proprietaire" />
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.Proprietaire)" />
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="niveauApplication" class="form-label">Niveau d'application</label>
                                <InputSelect id="niveauApplication" class="form-select" @bind-Value="@_politique.NiveauApplication">
                                    @foreach (var niveau in Enum.GetValues<NiveauApplicationPolitique>())
                                    {
                                        <option value="@niveau">@GetNiveauApplicationLabel(niveau)</option>
                                    }
                                </InputSelect>
                                <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.NiveauApplication)" />
                            </div>
                            <div class="col-md-6">
                                <label for="entiteApplication" class="form-label">Actifs d'application</label>
                                <div class="mb-2" style="@(_politique.NiveauApplication == NiveauApplicationPolitique.Organisation ? "opacity: 0.5; pointer-events: none;" : "")">
                                    <select id="entiteApplication" class="form-select" @bind="_actifSelectionne">
                                        <option value="@Guid.Empty">-- Sélectionner un actif --</option>
                                        @if (_actifs != null)
                                        {
                                            @foreach (var actif in _actifs.Where(a => !_actifsSelectionnes.Any(s => s.Id == a.Id)))
                                            {
                                                <option value="@actif.Id">@actif.Nom</option>
                                            }
                                        }
                                    </select>
                                    <button type="button" class="btn btn-outline-primary btn-sm mt-1" @onclick="AjouterActif"
                                            disabled="@(_actifSelectionne == Guid.Empty)">
                                        <i class="bi bi-plus"></i> Ajouter l'actif
                                    </button>
                                </div>

                                @if (_actifsSelectionnes.Any())
                                {
                                    <div class="border rounded p-2 bg-light">
                                        <small class="text-muted fw-bold">Actifs sélectionnés:</small>
                                        @foreach (var actif in _actifsSelectionnes)
                                        {
                                            <div class="d-flex justify-content-between align-items-center mt-1 p-1 bg-white rounded">
                                                <span class="badge bg-primary">@actif.Nom</span>
                                                <button type="button" class="btn btn-sm btn-outline-danger" @onclick="() => RetirerActif(actif.Id)">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            </div>
                                        }
                                    </div>
                                }

                                <small class="form-text text-muted">
                                    Sélectionnez les actifs auxquels cette politique s'applique (optionnel pour les politiques organisationnelles).
                                </small>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="dateEntreeVigueur" class="form-label">Date d'entrée en vigueur</label>
                                <InputDate id="dateEntreeVigueur" class="form-control" @bind-Value="@_politique.DateEntreeVigueur" />
                            </div>
                            <div class="col-md-4">
                                <label for="dateExpiration" class="form-label">Date d'expiration</label>
                                <InputDate id="dateExpiration" class="form-control" @bind-Value="@_politique.DateExpiration" />
                            </div>
                            <div class="col-md-4">
                                <label for="dateProchaineRevision" class="form-label">Prochaine révision</label>
                                <InputDate id="dateProchaineRevision" class="form-control" @bind-Value="@_politique.DateProchaineRevision" />
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="motsCles" class="form-label">Mots-clés (séparés par des virgules)</label>
                            <InputText id="motsCles" class="form-control" @bind-Value="@_politique.MotsCles" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.MotsCles)" />
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Contenu de la politique</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="contenu" class="form-label">Contenu (format Markdown)</label>
                            <InputTextArea id="contenu" class="form-control" rows="15" @bind-Value="@_politique.Contenu" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _politique.Contenu)" />
                            <small class="form-text text-muted">
                                Utilisez la syntaxe Markdown pour formater le contenu. Exemple: # Titre, ## Sous-titre, - Liste à puces, etc.
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Aperçu</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6>@_politique.Titre</h6>
                            <p>@_politique.Description</p>
                            <div class="d-flex justify-content-between">
                                <span>
                                    <span class="badge @GetStatutBadgeClass(_politique.Statut)">
                                        @GetStatutPolitiqueLabel(_politique.Statut)
                                    </span>
                                </span>
                                <span>Version @_politique.Version</span>
                            </div>
                        </div>
                        <hr />
                        <div class="markdown-preview">
                            @((MarkupString)ConvertirMarkdownEnHtml(_politique.Contenu))
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Références</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="reference" class="form-label">Ajouter une référence</label>
                            <div class="input-group">
                                <input type="text" id="reference" class="form-control" placeholder="Titre de la référence" @bind="_nouvelleReference.Titre" />
                                <input type="text" class="form-control" placeholder="URL" @bind="_nouvelleReference.Url" />
                                <button class="btn btn-outline-primary" type="button" @onclick="AjouterReference">
                                    <i class="bi bi-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="list-group">
                            @foreach (var reference in _references)
                            {
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <div>@reference.Titre</div>
                                        <small>@reference.Url</small>
                                    </div>
                                    <button class="btn btn-sm btn-danger" type="button" @onclick="() => SupprimerReference(reference)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Pièces jointes</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="pieceJointe" class="form-label">Ajouter une pièce jointe</label>
                            <div class="input-group">
                                <input type="text" id="pieceJointe" class="form-control" placeholder="Nom du fichier" @bind="_nouvellePieceJointe.Nom" />
                                <input type="text" class="form-control" placeholder="URL" @bind="_nouvellePieceJointe.Url" />
                                <button class="btn btn-outline-primary" type="button" @onclick="AjouterPieceJointe">
                                    <i class="bi bi-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="list-group">
                            @foreach (var pieceJointe in _piecesJointes)
                            {
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <div>@pieceJointe.Nom</div>
                                        <small>@pieceJointe.Url</small>
                                    </div>
                                    <button class="btn btn-sm btn-danger" type="button" @onclick="() => SupprimerPieceJointe(pieceJointe)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-end mb-4">
            <a href="@(_isNewPolitique ? "/politiques" : $"/politiques/{Id}")" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left"></i> Annuler
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-save"></i> Enregistrer
            </button>
        </div>
    </EditForm>
}

@code {
    [Parameter]
    public Guid Id { get; set; }

    private Politique _politique = new();
    private IEnumerable<ActifDonnees>? _actifs;
    private bool _loading = true;
    private bool _isNewPolitique => Id == Guid.Empty;
    private Guid _entiteApplicationGuid = Guid.Empty;
    private Guid _actifSelectionne = Guid.Empty;
    private List<ActifDonnees> _actifsSelectionnes = new();
    private List<Reference> _references = new();
    private Reference _nouvelleReference = new();
    private List<PieceJointe> _piecesJointes = new();
    private PieceJointe _nouvellePieceJointe = new();

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Id != Guid.Empty)
        {
            await ChargerDonnees();
        }
    }

    private async Task ChargerDonnees()
    {
        _loading = true;

        try
        {
            // Charger les actifs de données pour le sélecteur d'entité d'application
            _actifs = await ActifDonneesService.ObtenirTousAsync();

            if (Id != Guid.Empty)
            {
                // Mode édition
                var politique = await PolitiqueService.ObtenirParIdAsync(Id);
                if (politique != null)
                {
                    _politique = politique;
                    // Convertir l'EntiteApplicationId en Guid si c'est un int
                    if (_politique.EntiteApplicationId.HasValue && _actifs != null)
                    {
                        var actif = _actifs.FirstOrDefault(a => a.Id.GetHashCode() == _politique.EntiteApplicationId.Value);
                        _entiteApplicationGuid = actif?.Id ?? Guid.Empty;
                    }

                    // Charger les références
                    if (!string.IsNullOrEmpty(_politique.ReferencesJson))
                    {
                        try
                        {
                            _references = JsonSerializer.Deserialize<List<Reference>>(_politique.ReferencesJson) ?? new List<Reference>();
                        }
                        catch
                        {
                            _references = new List<Reference>();
                        }
                    }

                    // Charger les pièces jointes
                    if (!string.IsNullOrEmpty(_politique.PiecesJointesJson))
                    {
                        try
                        {
                            _piecesJointes = JsonSerializer.Deserialize<List<PieceJointe>>(_politique.PiecesJointesJson) ?? new List<PieceJointe>();
                        }
                        catch
                        {
                            _piecesJointes = new List<PieceJointe>();
                        }
                    }

                    // Charger les actifs associés
                    try
                    {
                        var actifsAssocies = await PolitiqueService.ObtenirActifsAssociesAsync(Id);
                        _actifsSelectionnes = actifsAssocies.ToList();
                    }
                    catch (Exception ex)
                    {
                        Console.Error.WriteLine($"Erreur lors du chargement des actifs associés: {ex.Message}");
                        _actifsSelectionnes = new List<ActifDonnees>();
                    }
                }
            }
            else
            {
                // Mode création
                _politique = new Politique
                {
                    Code = "POL-",
                    Titre = "",
                    Description = "",
                    Type = TypePolitique.Gouvernance,
                    Statut = StatutPolitique.Brouillon,
                    NiveauApplication = NiveauApplicationPolitique.Organisation,
                    Categorie = "Gouvernance", // Valeur par défaut
                    Proprietaire = "", // Sera rempli par l'utilisateur
                    Version = "1.0",
                    Contenu = "# Titre de la politique\n\n## 1. Introduction\n\n## 2. Objectifs\n\n## 3. Champ d'application\n\n## 4. Principes\n\n## 5. Responsabilités\n\n## 6. Références",
                    DateCreation = DateTime.Now,
                    DateModification = DateTime.Now,
                    CreePar = "user1", // Utilisateur actuel
                    CreeParNom = "Jean Dupont", // Nom de l'utilisateur actuel
                    ModifiePar = "user1", // Utilisateur actuel
                    ModifieParNom = "Jean Dupont" // Nom de l'utilisateur actuel
                };
            }
        }
        catch (Exception)
        {
            // Gérer l'exception
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task SauvegarderPolitique()
    {
        try
        {
            // Mettre à jour les propriétés liées à l'entité d'application
            if (_entiteApplicationGuid != Guid.Empty && _politique.NiveauApplication != NiveauApplicationPolitique.Organisation)
            {
                var actif = _actifs?.FirstOrDefault(a => a.Id == _entiteApplicationGuid);
                if (actif != null)
                {
                    _politique.EntiteApplicationId = actif.Id.GetHashCode(); // Convertir Guid en int pour compatibilité
                    _politique.EntiteApplicationNom = actif.Nom;
                }
            }
            else
            {
                _politique.EntiteApplicationId = null;
                _politique.EntiteApplicationNom = null;
            }

            // Mettre à jour les références
            _politique.ReferencesJson = JsonSerializer.Serialize(_references);

            // Mettre à jour les pièces jointes
            _politique.PiecesJointesJson = JsonSerializer.Serialize(_piecesJointes);

            // Mettre à jour les dates et utilisateurs
            _politique.DateModification = DateTime.Now;
            _politique.ModifiePar = "user1"; // Utilisateur actuel
            _politique.ModifieParNom = "Jean Dupont"; // Nom de l'utilisateur actuel

            // Ajouter à l'historique des modifications
            var historique = new List<object>();
            if (!string.IsNullOrEmpty(_politique.HistoriqueModificationsJson))
            {
                try
                {
                    historique = JsonSerializer.Deserialize<List<object>>(_politique.HistoriqueModificationsJson) ?? new List<object>();
                }
                catch
                {
                    historique = new List<object>();
                }
            }

            historique.Add(new
            {
                Date = DateTime.Now.ToString("yyyy-MM-dd"),
                Utilisateur = "Jean Dupont", // Nom de l'utilisateur actuel
                Action = _isNewPolitique ? "Création" : "Modification"
            });

            _politique.HistoriqueModificationsJson = JsonSerializer.Serialize(historique);

            Guid politiqueId;
            if (_isNewPolitique)
            {
                // Création
                var nouvellePolitique = await PolitiqueService.AjouterAsync(_politique);
                politiqueId = nouvellePolitique.Id;
            }
            else
            {
                // Mise à jour
                await PolitiqueService.MettreAJourAsync(Id, _politique);
                politiqueId = Id;
            }

            // Associer les actifs sélectionnés à la politique
            if (_actifsSelectionnes.Any())
            {
                try
                {
                    var actifsIds = _actifsSelectionnes.Select(a => a.Id).ToList();
                    await PolitiqueService.AssocierActifsAsync(politiqueId, actifsIds);
                    Console.WriteLine($"Associé {actifsIds.Count} actif(s) à la politique {politiqueId}");
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'association des actifs: {ex.Message}");
                    // Ne pas bloquer la sauvegarde de la politique pour cette erreur
                }
            }

            NavigationManager.NavigateTo($"/politiques/{politiqueId}");
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la sauvegarde de la politique: {ex.Message}");
            // Afficher un message d'erreur à l'utilisateur
            // TODO: Implémenter un système de notification
        }
    }

    private void AjouterReference()
    {
        if (!string.IsNullOrWhiteSpace(_nouvelleReference.Titre))
        {
            _references.Add(new Reference
            {
                Titre = _nouvelleReference.Titre,
                Url = _nouvelleReference.Url,
                DateAjout = DateTime.Now.ToString("yyyy-MM-dd")
            });
            _nouvelleReference = new Reference();
        }
    }

    private void SupprimerReference(Reference reference)
    {
        _references.Remove(reference);
    }

    private void AjouterPieceJointe()
    {
        if (!string.IsNullOrWhiteSpace(_nouvellePieceJointe.Nom))
        {
            _piecesJointes.Add(new PieceJointe
            {
                Nom = _nouvellePieceJointe.Nom,
                Url = _nouvellePieceJointe.Url,
                DateAjout = DateTime.Now.ToString("yyyy-MM-dd")
            });
            _nouvellePieceJointe = new PieceJointe();
        }
    }

    private void SupprimerPieceJointe(PieceJointe pieceJointe)
    {
        _piecesJointes.Remove(pieceJointe);
    }

    private void AjouterActif()
    {
        if (_actifSelectionne != Guid.Empty && _actifs != null)
        {
            var actif = _actifs.FirstOrDefault(a => a.Id == _actifSelectionne);
            if (actif != null && !_actifsSelectionnes.Any(a => a.Id == actif.Id))
            {
                _actifsSelectionnes.Add(actif);
                _actifSelectionne = Guid.Empty; // Reset selection
            }
        }
    }

    private void RetirerActif(Guid actifId)
    {
        _actifsSelectionnes.RemoveAll(a => a.Id == actifId);
    }

    private string ConvertirMarkdownEnHtml(string markdown)
    {
        // Dans une implémentation réelle, on utiliserait une bibliothèque Markdown
        // Pour simplifier, on fait une conversion basique
        var html = markdown
            .Replace("\n\n", "<br><br>")
            .Replace("\n", "<br>")
            .Replace("# ", "<h2>").Replace(" #", "</h2>")
            .Replace("## ", "<h3>").Replace(" ##", "</h3>")
            .Replace("### ", "<h4>").Replace(" ###", "</h4>")
            .Replace("- ", "<li>").Replace(" -", "</li>");

        return html;
    }

    private string GetTypePolitiqueLabel(TypePolitique type)
    {
        return type switch
        {
            TypePolitique.Gouvernance => "Gouvernance",
            TypePolitique.Qualite => "Qualité",
            TypePolitique.Securite => "Sécurité",
            TypePolitique.Confidentialite => "Confidentialité",
            TypePolitique.Conservation => "Conservation",
            TypePolitique.Acces => "Accès",
            TypePolitique.Partage => "Partage",
            TypePolitique.StandardTechnique => "Standard technique",
            TypePolitique.StandardMetier => "Standard métier",
            TypePolitique.Autre => "Autre",
            _ => type.ToString()
        };
    }

    private string GetStatutPolitiqueLabel(StatutPolitique statut)
    {
        return statut switch
        {
            StatutPolitique.Brouillon => "Brouillon",
            StatutPolitique.EnRevision => "En révision",
            StatutPolitique.Active => "Active",
            StatutPolitique.Archivee => "Archivée",
            StatutPolitique.Obsolete => "Obsolète",
            _ => statut.ToString()
        };
    }

    private string GetNiveauApplicationLabel(NiveauApplicationPolitique niveau)
    {
        return niveau switch
        {
            NiveauApplicationPolitique.Organisation => "Organisation",
            NiveauApplicationPolitique.Departement => "Département",
            NiveauApplicationPolitique.Projet => "Projet",
            NiveauApplicationPolitique.Domaine => "Domaine",
            NiveauApplicationPolitique.Actif => "Actif de données",
            NiveauApplicationPolitique.Systeme => "Système",
            _ => niveau.ToString()
        };
    }

    private string GetStatutBadgeClass(StatutPolitique statut)
    {
        return statut switch
        {
            StatutPolitique.Brouillon => "bg-info",
            StatutPolitique.EnRevision => "bg-warning",
            StatutPolitique.Active => "bg-success",
            StatutPolitique.Archivee => "bg-secondary",
            StatutPolitique.Obsolete => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private class Reference
    {
        public string Titre { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string DateAjout { get; set; } = string.Empty;
    }

    private class PieceJointe
    {
        public string Nom { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string DateAjout { get; set; } = string.Empty;
    }
}

