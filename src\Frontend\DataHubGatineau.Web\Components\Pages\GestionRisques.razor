@page "/gestion-risques"
@using DataHubGatineau.Web.Models.RiskManagement
@using DataHubGatineau.Web.Services.Interfaces
@inject IRisqueActifDonneesService RisqueService
@inject IExceptionHandlingService ExceptionHandlingService
@inject NavigationManager NavigationManager

<PageTitle>Gestion des Risques - DataHub Gatineau</PageTitle>

<div class="container-fluid">
    <!-- En-tête avec statistiques -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1 class="h3 mb-0">
                    <i class="bi bi-shield-exclamation text-danger me-2"></i>
                    Gestion des Risques
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" @onclick="ExporterRapport">
                        <i class="bi bi-download me-1"></i> Exporter
                    </button>
                    <button class="btn btn-primary" @onclick="NouveauRisque">
                        <i class="bi bi-plus-circle me-1"></i> Nouveau Risque
                    </button>
                </div>
            </div>

            <!-- Cartes de statistiques -->
            <div class="row g-3 mb-4">
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="text-danger mb-2">
                                <i class="bi bi-exclamation-triangle-fill fs-1"></i>
                            </div>
                            <h3 class="text-danger mb-1">@_statistiques.NombreRisquesCritiques</h3>
                            <p class="text-muted mb-0">Risques Critiques</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="text-warning mb-2">
                                <i class="bi bi-exclamation-circle-fill fs-1"></i>
                            </div>
                            <h3 class="text-warning mb-1">@_statistiques.NombreRisquesEleves</h3>
                            <p class="text-muted mb-0">Risques Élevés</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="text-info mb-2">
                                <i class="bi bi-clock-fill fs-1"></i>
                            </div>
                            <h3 class="text-info mb-1">@_statistiques.NombreRisquesEnRetard</h3>
                            <p class="text-muted mb-0">En Retard</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body text-center">
                            <div class="text-success mb-2">
                                <i class="bi bi-check-circle-fill fs-1"></i>
                            </div>
                            <h3 class="text-success mb-1">@_statistiques.NombreRisquesResolus</h3>
                            <p class="text-muted mb-0">Résolus</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">Recherche</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search"></i>
                                </span>
                                <input type="text" class="form-control" placeholder="Rechercher des risques..." 
                                       @bind="_searchTerm" @onkeypress="@(async (e) => { if (e.Key == "Enter") await RechercherRisques(); })">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Type de Risque</label>
                            <select class="form-select" @bind="_filtreTypeRisque">
                                <option value="">Tous les types</option>
                                @foreach (var type in Enum.GetValues<TypeRisque>())
                                {
                                    <option value="@type">@GetTypeRisqueLabel(type)</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Niveau</label>
                            <select class="form-select" @bind="_filtreNiveauRisque">
                                <option value="">Tous les niveaux</option>
                                @foreach (var niveau in Enum.GetValues<NiveauRisque>())
                                {
                                    <option value="@niveau">@GetNiveauRisqueLabel(niveau)</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Statut</label>
                            <select class="form-select" @bind="_filtreStatut">
                                <option value="">Tous les statuts</option>
                                @foreach (var statut in Enum.GetValues<StatutRisque>())
                                {
                                    <option value="@statut">@GetStatutRisqueLabel(statut)</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" @onclick="AppliquerFiltres">
                                    <i class="bi bi-funnel me-1"></i> Filtrer
                                </button>
                                <button class="btn btn-outline-secondary" @onclick="ReinitialiserFiltres">
                                    <i class="bi bi-x-circle me-1"></i> Réinitialiser
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des risques -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul me-2"></i>
                        Risques Identifiés (@_risquesFiltres.Count())
                    </h5>
                </div>
                <div class="card-body p-0">
                    @if (_loading)
                    {
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                    }
                    else if (!_risquesFiltres.Any())
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-shield-check text-muted" style="font-size: 3rem;"></i>
                            <p class="text-muted mt-3">Aucun risque trouvé avec les critères sélectionnés.</p>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Type</th>
                                        <th>Description</th>
                                        <th>Niveau</th>
                                        <th>Score</th>
                                        <th>Statut</th>
                                        <th>Responsable</th>
                                        <th>Date Limite</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var risque in _risquesFiltres)
                                    {
                                        <tr class="@GetRowClass(risque)">
                                            <td>
                                                <span class="badge @GetTypeRisqueBadgeClass(risque.TypeRisque)">
                                                    @GetTypeRisqueLabel(risque.TypeRisque)
                                                </span>
                                            </td>
                                            <td>
                                                <div class="fw-medium">@risque.Description</div>
                                                @if (!string.IsNullOrEmpty(risque.Impact))
                                                {
                                                    <small class="text-muted">Impact: @risque.Impact</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge @GetNiveauRisqueBadgeClass(risque.NiveauRisque)">
                                                    @GetNiveauRisqueLabel(risque.NiveauRisque)
                                                </span>
                                            </td>
                                            <td>
                                                <span class="fw-bold @GetScoreClass(risque.ScoreRisque)">
                                                    @risque.ScoreRisque
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge @GetStatutBadgeClass(risque.Statut)">
                                                    @GetStatutRisqueLabel(risque.Statut)
                                                </span>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(risque.ResponsableNom))
                                                {
                                                    <span>@risque.ResponsableNom</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Non assigné</span>
                                                }
                                            </td>
                                            <td>
                                                @if (risque.DateLimite.HasValue)
                                                {
                                                    <span class="@GetDateLimiteClass(risque.DateLimite.Value)">
                                                        @risque.DateLimite.Value.ToString("dd/MM/yyyy")
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" @onclick="() => VoirDetails(risque.Id)" title="Voir détails">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary" @onclick="() => ModifierRisque(risque.Id)" title="Modifier">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn btn-outline-success" @onclick="() => ChangerStatut(risque.Id)" title="Changer statut">
                                                        <i class="bi bi-arrow-repeat"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private List<RisqueActifDonnees> _risques = new();
    private IEnumerable<RisqueActifDonnees> _risquesFiltres = new List<RisqueActifDonnees>();
    private bool _loading = true;
    
    // Filtres
    private string _searchTerm = string.Empty;
    private TypeRisque? _filtreTypeRisque = null;
    private NiveauRisque? _filtreNiveauRisque = null;
    private StatutRisque? _filtreStatut = null;

    // Statistiques
    private StatistiquesRisques _statistiques = new();

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    private async Task ChargerDonnees()
    {
        try
        {
            _loading = true;
            var risques = await RisqueService.ObtenirTousAsync();
            _risques = risques.ToList();
            
            await CalculerStatistiques();
            AppliquerFiltresInternes();
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement des risques");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task CalculerStatistiques()
    {
        var risquesEnRetard = await RisqueService.ObtenirRisquesEnRetardAsync();
        
        _statistiques = new StatistiquesRisques
        {
            NombreRisquesCritiques = _risques.Count(r => r.NiveauRisque == NiveauRisque.Critique),
            NombreRisquesEleves = _risques.Count(r => r.NiveauRisque == NiveauRisque.Eleve),
            NombreRisquesEnRetard = risquesEnRetard.Count(),
            NombreRisquesResolus = _risques.Count(r => r.Statut == StatutRisque.Resolu)
        };
    }

    private void AppliquerFiltresInternes()
    {
        var filtered = _risques.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(_searchTerm))
        {
            filtered = filtered.Where(r => 
                r.Description.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (r.Impact?.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) ?? false));
        }

        if (_filtreTypeRisque.HasValue)
        {
            filtered = filtered.Where(r => r.TypeRisque == _filtreTypeRisque.Value);
        }

        if (_filtreNiveauRisque.HasValue)
        {
            filtered = filtered.Where(r => r.NiveauRisque == _filtreNiveauRisque.Value);
        }

        if (_filtreStatut.HasValue)
        {
            filtered = filtered.Where(r => r.Statut == _filtreStatut.Value);
        }

        _risquesFiltres = filtered.OrderByDescending(r => r.ScoreRisque)
                                 .ThenByDescending(r => r.DateIdentification);
    }

    private async Task AppliquerFiltres()
    {
        AppliquerFiltresInternes();
        StateHasChanged();
    }

    private async Task ReinitialiserFiltres()
    {
        _searchTerm = string.Empty;
        _filtreTypeRisque = null;
        _filtreNiveauRisque = null;
        _filtreStatut = null;
        AppliquerFiltresInternes();
        StateHasChanged();
    }

    private async Task RechercherRisques()
    {
        if (!string.IsNullOrWhiteSpace(_searchTerm))
        {
            try
            {
                var resultats = await RisqueService.RechercherAsync(_searchTerm);
                _risques = resultats.ToList();
                AppliquerFiltresInternes();
            }
            catch (Exception ex)
            {
                await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de la recherche");
            }
        }
        else
        {
            await ChargerDonnees();
        }
    }

    private void NouveauRisque()
    {
        NavigationManager.NavigateTo("/gestion-risques/nouveau");
    }

    private void VoirDetails(Guid risqueId)
    {
        NavigationManager.NavigateTo($"/gestion-risques/details/{risqueId}");
    }

    private void ModifierRisque(Guid risqueId)
    {
        NavigationManager.NavigateTo($"/gestion-risques/modifier/{risqueId}");
    }

    private async Task ChangerStatut(Guid risqueId)
    {
        // TODO: Implémenter modal de changement de statut
        NavigationManager.NavigateTo($"/gestion-risques/statut/{risqueId}");
    }

    private async Task ExporterRapport()
    {
        // TODO: Implémenter export des risques
        Console.WriteLine("Export des risques demandé");
    }

    // Méthodes utilitaires pour l'affichage
    private string GetTypeRisqueLabel(TypeRisque type) => type switch
    {
        TypeRisque.Confidentialite => "Confidentialité",
        TypeRisque.Integrite => "Intégrité",
        TypeRisque.Disponibilite => "Disponibilité",
        TypeRisque.Conformite => "Conformité",
        TypeRisque.Qualite => "Qualité",
        TypeRisque.AccesNonAutorise => "Accès non autorisé",
        TypeRisque.FuiteDonnees => "Fuite de données",
        TypeRisque.PerteDonnees => "Perte de données",
        TypeRisque.Operationnel => "Opérationnel",
        TypeRisque.Reputation => "Réputation",
        _ => type.ToString()
    };

    private string GetNiveauRisqueLabel(NiveauRisque niveau) => niveau switch
    {
        NiveauRisque.TresFaible => "Très Faible",
        NiveauRisque.Faible => "Faible",
        NiveauRisque.Modere => "Modéré",
        NiveauRisque.Eleve => "Élevé",
        NiveauRisque.Critique => "Critique",
        _ => niveau.ToString()
    };

    private string GetStatutRisqueLabel(StatutRisque statut) => statut switch
    {
        StatutRisque.Identifie => "Identifié",
        StatutRisque.EnEvaluation => "En évaluation",
        StatutRisque.EnTraitement => "En traitement",
        StatutRisque.Mitige => "Mitigé",
        StatutRisque.Resolu => "Résolu",
        StatutRisque.Accepte => "Accepté",
        StatutRisque.Transfere => "Transféré",
        _ => statut.ToString()
    };

    private string GetTypeRisqueBadgeClass(TypeRisque type) => type switch
    {
        TypeRisque.Confidentialite => "bg-danger",
        TypeRisque.Integrite => "bg-warning",
        TypeRisque.Disponibilite => "bg-info",
        TypeRisque.Conformite => "bg-primary",
        TypeRisque.FuiteDonnees => "bg-danger",
        TypeRisque.PerteDonnees => "bg-danger",
        _ => "bg-secondary"
    };

    private string GetNiveauRisqueBadgeClass(NiveauRisque niveau) => niveau switch
    {
        NiveauRisque.TresFaible => "bg-success",
        NiveauRisque.Faible => "bg-success",
        NiveauRisque.Modere => "bg-warning",
        NiveauRisque.Eleve => "bg-danger",
        NiveauRisque.Critique => "bg-danger",
        _ => "bg-secondary"
    };

    private string GetStatutBadgeClass(StatutRisque statut) => statut switch
    {
        StatutRisque.Identifie => "bg-secondary",
        StatutRisque.EnEvaluation => "bg-info",
        StatutRisque.EnTraitement => "bg-warning",
        StatutRisque.Mitige => "bg-success",
        StatutRisque.Resolu => "bg-success",
        StatutRisque.Accepte => "bg-primary",
        StatutRisque.Transfere => "bg-info",
        _ => "bg-secondary"
    };

    private string GetRowClass(RisqueActifDonnees risque)
    {
        if (risque.NiveauRisque == NiveauRisque.Critique)
            return "table-danger";
        if (risque.NiveauRisque == NiveauRisque.Eleve)
            return "table-warning";
        if (risque.DateLimite.HasValue && risque.DateLimite < DateTime.Now && risque.Statut != StatutRisque.Resolu)
            return "table-warning";
        return "";
    }

    private string GetScoreClass(int score) => score switch
    {
        >= 20 => "text-danger",
        >= 15 => "text-warning",
        >= 10 => "text-info",
        _ => "text-success"
    };

    private string GetDateLimiteClass(DateTime dateLimite)
    {
        if (dateLimite < DateTime.Now)
            return "text-danger fw-bold";
        if (dateLimite < DateTime.Now.AddDays(7))
            return "text-warning";
        return "";
    }

    private class StatistiquesRisques
    {
        public int NombreRisquesCritiques { get; set; }
        public int NombreRisquesEleves { get; set; }
        public int NombreRisquesEnRetard { get; set; }
        public int NombreRisquesResolus { get; set; }
    }
}
