@page "/politiques"
@using DataHubGatineau.Web.Models.Policy
@using DataHubGatineau.Web.Services.Interfaces
@inject IPolitiqueService PolitiqueService
@inject IConformitePolitiqueService ConformitePolitiqueService
@inject NavigationManager NavigationManager

<PageTitle>Politiques et standards - DataHub Gatineau</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Politiques et standards</h1>
    <div>
        <a href="/politiques/dashboard" class="btn btn-outline-info me-2">
            <i class="bi bi-graph-up"></i> Tableau de bord
        </a>
        <a href="/politiques/conformite" class="btn btn-outline-primary me-2">
            <i class="bi bi-clipboard-check"></i> Conformité
        </a>
        <a href="/politiques/ajouter" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Ajouter une politique
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            Les politiques et standards définissent les règles et les directives pour la gouvernance des données dans l'organisation.
            Ils établissent les principes, les responsabilités et les exigences pour assurer une gestion efficace et conforme des données.
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5>Filtres</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="typeFilter" class="form-label">Type</label>
                    <select id="typeFilter" class="form-select" @bind="_typeFilter">
                        <option value="">Tous les types</option>
                        @foreach (var type in Enum.GetValues<TypePolitique>())
                        {
                            <option value="@type">@GetTypePolitiqueLabel(type)</option>
                        }
                    </select>
                </div>
                <div class="mb-3">
                    <label for="statutFilter" class="form-label">Statut</label>
                    <select id="statutFilter" class="form-select" @bind="_statutFilter">
                        <option value="">Tous les statuts</option>
                        @foreach (var statut in Enum.GetValues<StatutPolitique>())
                        {
                            <option value="@statut">@GetStatutPolitiqueLabel(statut)</option>
                        }
                    </select>
                </div>
                <div class="mb-3">
                    <label for="niveauFilter" class="form-label">Niveau d'application</label>
                    <select id="niveauFilter" class="form-select" @bind="_niveauFilter">
                        <option value="">Tous les niveaux</option>
                        @foreach (var niveau in Enum.GetValues<NiveauApplicationPolitique>())
                        {
                            <option value="@niveau">@GetNiveauApplicationLabel(niveau)</option>
                        }
                    </select>
                </div>
                <div class="mb-3">
                    <label for="searchText" class="form-label">Recherche</label>
                    <input type="text" id="searchText" class="form-control" placeholder="Titre, code, mots-clés..." @bind="_searchText" @bind:event="oninput" />
                </div>
                <button class="btn btn-primary w-100" @onclick="FiltrerPolitiques">
                    <i class="bi bi-funnel"></i> Filtrer
                </button>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5>Statistiques</h5>
            </div>
            <div class="card-body">
                @if (_loading)
                {
                    <div class="d-flex justify-content-center my-3">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                }
                else
                {
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Politiques actives
                            <span class="badge bg-success rounded-pill">@(_politiques?.Count(p => p.Statut == StatutPolitique.Active) ?? 0)</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Politiques en révision
                            <span class="badge bg-warning rounded-pill">@(_politiques?.Count(p => p.Statut == StatutPolitique.EnRevision) ?? 0)</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Brouillons
                            <span class="badge bg-info rounded-pill">@(_politiques?.Count(p => p.Statut == StatutPolitique.Brouillon) ?? 0)</span>
                        </li>
                    </ul>
                }
            </div>
        </div>
    </div>

    <div class="col-md-9">
        <div class="card">
            <div class="card-header">
                <h5>Liste des politiques et standards</h5>
            </div>
            <div class="card-body">
                @if (_loading)
                {
                    <div class="d-flex justify-content-center my-3">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                }
                else if (_politiquesFiltrees == null || !_politiquesFiltrees.Any())
                {
                    <div class="alert alert-info">
                        Aucune politique trouvée.
                    </div>
                }
                else
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Titre</th>
                                    <th>Type</th>
                                    <th>Version</th>
                                    <th>Statut</th>
                                    <th>Date d'entrée en vigueur</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var politique in _politiquesFiltrees)
                                {
                                    <tr>
                                        <td>@politique.Code</td>
                                        <td>@politique.Titre</td>
                                        <td>@GetTypePolitiqueLabel(politique.Type)</td>
                                        <td>@politique.Version</td>
                                        <td>
                                            <span class="badge @GetStatutBadgeClass(politique.Statut)">
                                                @GetStatutPolitiqueLabel(politique.Statut)
                                            </span>
                                        </td>
                                        <td>
                                            @(politique.DateEntreeVigueur.HasValue ? politique.DateEntreeVigueur.Value.ToString("dd/MM/yyyy") : "-")
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/politiques/@politique.Id" class="btn btn-sm btn-info">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="/politiques/modifier/@politique.Id" class="btn btn-sm btn-warning">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                @if (politique.Statut == StatutPolitique.Brouillon || politique.Statut == StatutPolitique.EnRevision)
                                                {
                                                    <button class="btn btn-sm btn-success" @onclick="() => ApprouverPolitique(politique.Id)">
                                                        <i class="bi bi-check-lg"></i>
                                                    </button>
                                                }
                                                @if (politique.Statut == StatutPolitique.Active)
                                                {
                                                    <button class="btn btn-sm btn-secondary" @onclick="() => ArchiverPolitique(politique.Id)">
                                                        <i class="bi bi-archive"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-primary" @onclick="() => CreerNouvelleVersion(politique.Id)">
                                                        <i class="bi bi-plus-square"></i>
                                                    </button>
                                                }
                                                <button class="btn btn-sm btn-danger" @onclick="() => ConfirmerSuppression(politique)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<Confirmation
    IsVisible="@_showDeleteConfirmation"
    Title="Confirmer la suppression"
    Message="Êtes-vous sûr de vouloir supprimer cette politique? Cette action est irréversible."
    ConfirmText="Supprimer"
    CancelText="Annuler"
    ConfirmButtonType="danger"
    OnConfirm="SupprimerPolitique"
    OnCancel="AnnulerSuppression" />

@code {
    private IEnumerable<Politique>? _politiques;
    private IEnumerable<Politique>? _politiquesFiltrees;
    private bool _loading = true;
    private bool _showDeleteConfirmation = false;
    private Politique? _politiqueToDelete;

    // Filtres
    private string _typeFilter = string.Empty;
    private string _statutFilter = string.Empty;
    private string _niveauFilter = string.Empty;
    private string _searchText = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    private async Task ChargerDonnees()
    {
        _loading = true;

        try
        {
            _politiques = await PolitiqueService.ObtenirTousAsync();
            FiltrerPolitiques();
        }
        finally
        {
            _loading = false;
        }
    }

    private void FiltrerPolitiques()
    {
        if (_politiques == null)
        {
            _politiquesFiltrees = Enumerable.Empty<Politique>();
            return;
        }

        var filtered = _politiques;

        // Filtre par type
        if (!string.IsNullOrEmpty(_typeFilter) && Enum.TryParse<TypePolitique>(_typeFilter, out var type))
        {
            filtered = filtered.Where(p => p.Type == type);
        }

        // Filtre par statut
        if (!string.IsNullOrEmpty(_statutFilter) && Enum.TryParse<StatutPolitique>(_statutFilter, out var statut))
        {
            filtered = filtered.Where(p => p.Statut == statut);
        }

        // Filtre par niveau d'application
        if (!string.IsNullOrEmpty(_niveauFilter) && Enum.TryParse<NiveauApplicationPolitique>(_niveauFilter, out var niveau))
        {
            filtered = filtered.Where(p => p.NiveauApplication == niveau);
        }

        // Filtre par texte de recherche
        if (!string.IsNullOrEmpty(_searchText))
        {
            filtered = filtered.Where(p =>
                p.Titre.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ||
                p.Code.Contains(_searchText, StringComparison.OrdinalIgnoreCase) ||
                p.Description?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) == true ||
                p.MotsCles?.Contains(_searchText, StringComparison.OrdinalIgnoreCase) == true);
        }

        _politiquesFiltrees = filtered.OrderBy(p => p.Code);
    }

    private async Task ApprouverPolitique(Guid politiqueId)
    {
        // Dans une implémentation réelle, on afficherait un formulaire d'approbation
        var approbation = new ApprobationPolitique
        {
            ApprobateurId = "user1", // Utilisateur actuel
            ApprobateurNom = "Jean Dupont", // Nom de l'utilisateur actuel
            Role = "Administrateur",
            Commentaires = "Politique approuvée"
        };

        await PolitiqueService.ApprouverPolitiqueAsync(politiqueId, approbation);
        await ChargerDonnees();
    }

    private async Task ArchiverPolitique(Guid politiqueId)
    {
        await PolitiqueService.ArchiverPolitiqueAsync(politiqueId);
        await ChargerDonnees();
    }

    private async Task CreerNouvelleVersion(Guid politiqueId)
    {
        var nouvelleVersion = await PolitiqueService.CreerNouvelleVersionAsync(politiqueId);
        NavigationManager.NavigateTo($"/politiques/modifier/{nouvelleVersion.Id}");
    }

    private void ConfirmerSuppression(Politique politique)
    {
        _politiqueToDelete = politique;
        _showDeleteConfirmation = true;
    }

    private void AnnulerSuppression()
    {
        _showDeleteConfirmation = false;
        _politiqueToDelete = null;
    }

    private async Task SupprimerPolitique()
    {
        try
        {
            if (_politiqueToDelete != null)
            {
                await PolitiqueService.SupprimerAsync(_politiqueToDelete.Id);
                await ChargerDonnees();
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la suppression: {ex.Message}");
            // TODO: Afficher un message d'erreur à l'utilisateur
        }
        finally
        {
            _showDeleteConfirmation = false;
            _politiqueToDelete = null;
        }
    }

    private string GetTypePolitiqueLabel(TypePolitique type)
    {
        return type switch
        {
            TypePolitique.Gouvernance => "Gouvernance",
            TypePolitique.Qualite => "Qualité",
            TypePolitique.Securite => "Sécurité",
            TypePolitique.Confidentialite => "Confidentialité",
            TypePolitique.Conservation => "Conservation",
            TypePolitique.Acces => "Accès",
            TypePolitique.Partage => "Partage",
            TypePolitique.StandardTechnique => "Standard technique",
            TypePolitique.StandardMetier => "Standard métier",
            TypePolitique.Autre => "Autre",
            _ => type.ToString()
        };
    }

    private string GetStatutPolitiqueLabel(StatutPolitique statut)
    {
        return statut switch
        {
            StatutPolitique.Brouillon => "Brouillon",
            StatutPolitique.EnRevision => "En révision",
            StatutPolitique.Active => "Active",
            StatutPolitique.Archivee => "Archivée",
            StatutPolitique.Obsolete => "Obsolète",
            _ => statut.ToString()
        };
    }

    private string GetNiveauApplicationLabel(NiveauApplicationPolitique niveau)
    {
        return niveau switch
        {
            NiveauApplicationPolitique.Organisation => "Organisation",
            NiveauApplicationPolitique.Departement => "Département",
            NiveauApplicationPolitique.Projet => "Projet",
            NiveauApplicationPolitique.Domaine => "Domaine",
            NiveauApplicationPolitique.Actif => "Actif de données",
            NiveauApplicationPolitique.Systeme => "Système",
            _ => niveau.ToString()
        };
    }

    private string GetStatutBadgeClass(StatutPolitique statut)
    {
        return statut switch
        {
            StatutPolitique.Brouillon => "bg-info",
            StatutPolitique.EnRevision => "bg-warning",
            StatutPolitique.Active => "bg-success",
            StatutPolitique.Archivee => "bg-secondary",
            StatutPolitique.Obsolete => "bg-danger",
            _ => "bg-secondary"
        };
    }
}

