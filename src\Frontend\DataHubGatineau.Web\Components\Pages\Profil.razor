@page "/profil"
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services
@using DataHubGatineau.Web.Services.Auth
@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Models.Auth
@using DataHubGatineau.Web.Models
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject IAuthService AuthService
@inject IServiceGestionUtilisateurs ServiceGestionUtilisateurs

<PageTitle>Profil Utilisateur - DataHub Gatineau</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Profil Utilisateur</h1>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card shadow-sm border-0 rounded-4 mb-4">
            <div class="card-header bg-white border-0 py-3">
                <h2 class="h5 mb-0 fw-bold">
                    <i class="bi bi-person-circle text-primary me-2"></i>
                    Informations personnelles
                </h2>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="avatar-container mb-3 position-relative d-inline-block">
                        <img src="images/default-avatar.svg" alt="Avatar" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;" />
                        <button class="btn btn-sm btn-primary position-absolute bottom-0 end-0 rounded-circle p-2" style="margin-right: 10px; margin-bottom: 10px;">
                            <i class="bi bi-pencil"></i>
                        </button>
                    </div>
                    <h3 class="h4 mb-1">@GetUserDisplayName()</h3>
                    <p class="text-muted mb-2">@GetUserRole()</p>
                    <span class="badge bg-success rounded-pill px-3 py-2">
                        <i class="bi bi-check-circle me-1"></i> Actif
                    </span>
                </div>

                <div class="mb-3">
                    <label for="nom" class="form-label">Nom complet</label>
                    <input type="text" class="form-control" id="nom" value="@GetUserDisplayName()" />
                </div>
                <div class="mb-3">
                    <label for="email" class="form-label">Adresse e-mail</label>
                    <input type="email" class="form-control" id="email" value="@GetUserEmail()" />
                </div>
                <div class="mb-3">
                    <label for="telephone" class="form-label">Téléphone</label>
                    <input type="tel" class="form-control" id="telephone" value="@GetUserTelephone()" />
                </div>
                <div class="mb-3">
                    <label for="poste" class="form-label">Poste</label>
                    <input type="text" class="form-control" id="poste" value="@GetUserPoste()" />
                </div>
                <div class="mb-3">
                    <label for="departement" class="form-label">Département</label>
                    <input type="text" class="form-control" id="departement" value="@GetUserDepartement()" />
                </div>
                <div class="d-grid">
                    <button class="btn btn-primary" @onclick="SauvegarderProfil">
                        <i class="bi bi-save me-1"></i> Sauvegarder les modifications
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card shadow-sm border-0 rounded-4 mb-4">
            <div class="card-header bg-white border-0 py-3">
                <h2 class="h5 mb-0 fw-bold">
                    <i class="bi bi-shield-lock text-primary me-2"></i>
                    Sécurité
                </h2>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h3 class="h6 mb-3">Changer le mot de passe</h3>
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">Mot de passe actuel</label>
                        <input type="password" class="form-control" id="currentPassword" />
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">Nouveau mot de passe</label>
                        <input type="password" class="form-control" id="newPassword" />
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirmer le mot de passe</label>
                        <input type="password" class="form-control" id="confirmPassword" />
                    </div>
                    <button class="btn btn-primary" @onclick="ChangerMotDePasse">
                        <i class="bi bi-key me-1"></i> Mettre à jour le mot de passe
                    </button>
                </div>

                <hr />

                <div class="mb-4">
                    <h3 class="h6 mb-3">Authentification à deux facteurs</h3>
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="enable2fa" checked />
                        <label class="form-check-label" for="enable2fa">Activer l'authentification à deux facteurs</label>
                    </div>
                    <p class="text-muted small">
                        L'authentification à deux facteurs ajoute une couche de sécurité supplémentaire à votre compte en exigeant un code de vérification en plus de votre mot de passe.
                    </p>
                    <button class="btn btn-outline-primary" @onclick="ConfigurerDeuxFacteurs">
                        <i class="bi bi-qr-code me-1"></i> Configurer l'authentification à deux facteurs
                    </button>
                </div>
            </div>
        </div>

        <div class="card shadow-sm border-0 rounded-4 mb-4">
            <div class="card-header bg-white border-0 py-3">
                <h2 class="h5 mb-0 fw-bold">
                    <i class="bi bi-gear text-primary me-2"></i>
                    Préférences
                </h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="language" class="form-label">Langue</label>
                            <select class="form-select" id="language">
                                <option value="fr" selected>Français</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="theme" class="form-label">Thème</label>
                            <select class="form-select" id="theme">
                                <option value="light" selected>Clair</option>
                                <option value="dark">Sombre</option>
                                <option value="system">Système</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Notifications</label>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="notifEmail" checked />
                        <label class="form-check-label" for="notifEmail">
                            Recevoir des notifications par e-mail
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="notifApp" checked />
                        <label class="form-check-label" for="notifApp">
                            Recevoir des notifications dans l'application
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="notifRapports" checked />
                        <label class="form-check-label" for="notifRapports">
                            Recevoir des rapports hebdomadaires
                        </label>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Affichage du tableau de bord</label>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="showQuality" checked />
                        <label class="form-check-label" for="showQuality">
                            Afficher les statistiques de qualité
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="showRecent" checked />
                        <label class="form-check-label" for="showRecent">
                            Afficher les actifs récents
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="showAlerts" checked />
                        <label class="form-check-label" for="showAlerts">
                            Afficher les alertes
                        </label>
                    </div>
                </div>

                <button class="btn btn-primary" @onclick="SauvegarderPreferences">
                    <i class="bi bi-save me-1"></i> Sauvegarder les préférences
                </button>
            </div>
        </div>

        <div class="card shadow-sm border-0 rounded-4">
            <div class="card-header bg-white border-0 py-3">
                <h2 class="h5 mb-0 fw-bold">
                    <i class="bi bi-clock-history text-primary me-2"></i>
                    Activité récente
                </h2>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush">
                    @foreach (var activite in _activitesRecentes)
                    {
                        <div class="list-group-item border-0 py-3">
                            <div class="d-flex">
                                <div class="icon-wrapper me-3 @activite.IconClass p-2 rounded-circle">
                                    <i class="bi @activite.Icon"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">@activite.Description</h6>
                                    <small class="text-muted">@activite.Date.ToString("dd/MM/yyyy HH:mm")</small>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private UserInfo? _currentUser;
    private UtilisateurAvecRoles? _utilisateurComplet;
    private List<ActiviteUtilisateur> _activitesRecentes = new List<ActiviteUtilisateur>
    {
        new ActiviteUtilisateur
        {
            Description = "Connexion au système",
            Date = DateTime.Now.AddHours(-1),
            Icon = "bi-box-arrow-in-right",
            IconClass = "bg-primary bg-opacity-10 text-primary"
        },
        new ActiviteUtilisateur
        {
            Description = "Modification de l'actif 'Adresse client'",
            Date = DateTime.Now.AddHours(-3),
            Icon = "bi-pencil",
            IconClass = "bg-warning bg-opacity-10 text-warning"
        },
        new ActiviteUtilisateur
        {
            Description = "Ajout d'une nouvelle règle de qualité",
            Date = DateTime.Now.AddDays(-1),
            Icon = "bi-plus-circle",
            IconClass = "bg-success bg-opacity-10 text-success"
        },
        new ActiviteUtilisateur
        {
            Description = "Exécution du rapport de qualité",
            Date = DateTime.Now.AddDays(-2),
            Icon = "bi-bar-chart",
            IconClass = "bg-info bg-opacity-10 text-info"
        },
        new ActiviteUtilisateur
        {
            Description = "Modification des paramètres du compte",
            Date = DateTime.Now.AddDays(-3),
            Icon = "bi-gear",
            IconClass = "bg-secondary bg-opacity-10 text-secondary"
        }
    };

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUser();
    }

    private async Task LoadCurrentUser()
    {
        try
        {
            _currentUser = await AuthService.GetCurrentUserAsync();
            if (_currentUser == null)
            {
                NavigationManager.NavigateTo("/login");
                return;
            }

            // Charger les informations complètes de l'utilisateur
            if (Guid.TryParse(_currentUser.Id, out var userId))
            {
                _utilisateurComplet = await ServiceGestionUtilisateurs.ObtenirUtilisateurParIdAsync(userId);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading user: {ex.Message}");
            NavigationManager.NavigateTo("/login");
        }
    }

    private string GetUserDisplayName()
    {
        if (_currentUser == null) return "Utilisateur";

        if (!string.IsNullOrEmpty(_currentUser.FullName) && _currentUser.FullName.Trim() != "")
            return _currentUser.FullName;

        if (!string.IsNullOrEmpty(_currentUser.FirstName) || !string.IsNullOrEmpty(_currentUser.LastName))
            return $"{_currentUser.FirstName} {_currentUser.LastName}".Trim();

        if (!string.IsNullOrEmpty(_currentUser.Username))
            return _currentUser.Username;

        return _currentUser.Email ?? "Utilisateur";
    }

    private string GetUserEmail()
    {
        return _currentUser?.Email ?? "";
    }

    private string GetUserRole()
    {
        if (_utilisateurComplet?.Roles?.Any() == true)
        {
            return string.Join(", ", _utilisateurComplet.Roles.Select(r => r.Nom));
        }
        if (_currentUser?.Roles?.Any() == true)
        {
            return string.Join(", ", _currentUser.Roles);
        }
        return "Utilisateur";
    }

    private string GetUserTelephone()
    {
        return _utilisateurComplet?.Telephone ?? "";
    }

    private string GetUserPoste()
    {
        return _utilisateurComplet?.Poste ?? "";
    }

    private string GetUserDepartement()
    {
        // Pour l'instant, retourner une valeur par défaut
        // À l'avenir, cela pourrait venir d'une table de départements
        return "Technologies de l'information";
    }

    private async Task SauvegarderProfil()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Profil sauvegardé avec succès!");
    }

    private async Task ChangerMotDePasse()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Mot de passe mis à jour avec succès!");
    }

    private async Task ConfigurerDeuxFacteurs()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Configuration de l'authentification à deux facteurs initiée.");
    }

    private async Task SauvegarderPreferences()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Préférences sauvegardées avec succès!");
    }

    private class ActiviteUtilisateur
    {
        public string Description { get; set; } = string.Empty;
        public DateTime Date { get; set; }
        public string Icon { get; set; } = string.Empty;
        public string IconClass { get; set; } = string.Empty;
    }
}

