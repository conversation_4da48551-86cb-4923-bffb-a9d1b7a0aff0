@page "/qualite-donnees/regles"
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services.Interfaces
@using DomainEnums = DataHubGatineau.Domain.Enums
@inject IRegleQualiteService RegleQualiteService
@inject IActifDonneesService ActifDonneesService
@inject IResultatRegleQualiteService ResultatRegleQualiteService
@inject NavigationManager NavigationManager
@inject IExceptionHandlingService ExceptionHandlingService

<PageTitle>Règles de qualité - DataHub Gatineau</PageTitle>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Règles de qualité</h1>
    <div>
        <a href="/qualite-donnees" class="btn btn-outline-secondary me-2">
            <i class="bi bi-arrow-left"></i> Retour
        </a>
        <button class="btn btn-success me-2" @onclick="ExecuterToutesReglesActives" disabled="@_executionEnCours">
            @if (_executionEnCours)
            {
                <i class="bi bi-hourglass-split"></i> <span>Exécution...</span>
            }
            else
            {
                <i class="bi bi-play-fill"></i> <span>Exécuter toutes les règles actives</span>
            }
        </button>
        <button class="btn btn-info me-2" @onclick="OuvrirProgrammation">
            <i class="bi bi-clock"></i> Programmer l'exécution
        </button>
        <a href="/qualite-donnees/regles/ajouter" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Ajouter une règle
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5>Filtres</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="actifFilter" class="form-label">Actif de données</label>
                    <select id="actifFilter" class="form-select" @bind="_actifFilter">
                        <option value="@Guid.Empty">Tous les actifs</option>
                        @if (_actifs != null)
                        {
                            @foreach (var actif in _actifs)
                            {
                                <option value="@actif.Id">@actif.Nom</option>
                            }
                        }
                    </select>
                </div>
                <div class="mb-3">
                    <label for="typeFilter" class="form-label">Type de règle</label>
                    <select id="typeFilter" class="form-select" @bind="_typeFilter">
                        <option value="">Tous les types</option>
                        @foreach (var type in Enum.GetValues<TypeRegleQualite>())
                        {
                            <option value="@type">@GetTypeRegleLabel(type)</option>
                        }
                    </select>
                </div>
                <div class="mb-3">
                    <label for="severiteFilter" class="form-label">Sévérité minimale</label>
                    <select id="severiteFilter" class="form-select" @bind="_severiteFilter">
                        <option value="">Toutes les sévérités</option>
                        @foreach (var severite in Enum.GetValues<SeveriteRegleQualite>())
                        {
                            <option value="@severite">@GetSeveriteLabel(severite)</option>
                        }
                    </select>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="activeFilter" @bind="_activeFilter" />
                        <label class="form-check-label" for="activeFilter">
                            Règles actives uniquement
                        </label>
                    </div>
                </div>
                <button class="btn btn-primary w-100" @onclick="FiltrerRegles">
                    <i class="bi bi-funnel"></i> Filtrer
                </button>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5>Statistiques</h5>
            </div>
            <div class="card-body">
                @if (_loading)
                {
                    <div class="d-flex justify-content-center my-3">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                }
                else
                {
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total des règles
                            <span class="badge bg-primary rounded-pill">@(_regles?.Count() ?? 0)</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Règles actives
                            <span class="badge bg-success rounded-pill">@(_regles?.Count(r => r.EstActive) ?? 0)</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Règles critiques
                            <span class="badge bg-danger rounded-pill">@(_regles?.Count(r => r.Severite == SeveriteRegleQualite.Critique) ?? 0)</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Règles en échec
                            <span class="badge bg-warning rounded-pill">@_reglesEnEchec</span>
                        </li>
                    </ul>
                }
            </div>
        </div>
    </div>

    <div class="col-md-9">
        <div class="card">
            <div class="card-header">
                <h5>Liste des règles de qualité</h5>
            </div>
            <div class="card-body">
                @if (_loading)
                {
                    <div class="d-flex justify-content-center my-3">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                }
                else if (_regles == null || !_regles.Any())
                {
                    <div class="alert alert-info">
                        Aucune règle de qualité trouvée.
                    </div>
                }
                else
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Actif</th>
                                    <th>Type</th>
                                    <th>Champ</th>
                                    <th>Sévérité</th>
                                    <th>Statut</th>
                                    <th>Dernière exécution</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var regle in _regles)
                                {
                                    <tr>
                                        <td>@regle.Nom</td>
                                        <td>
                                            @if (regle.ActifDonneesId != Guid.Empty)
                                            {
                                                <a href="/actifs-donnees-details/@regle.ActifDonneesId">@GetActifNom(regle.ActifDonneesId)</a>
                                            }
                                            else
                                            {
                                                <span>Global</span>
                                            }
                                        </td>
                                        <td>@GetTypeRegleLabel(regle.Type)</td>
                                        <td>@(string.IsNullOrEmpty(regle.Champ) ? "Tous" : regle.Champ)</td>
                                        <td>
                                            <span class="badge @GetSeveriteColorClass(regle.Severite)">
                                                @GetSeveriteLabel(regle.Severite)
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge @(regle.EstActive ? "bg-success" : "bg-secondary")">
                                                @(regle.EstActive ? "Active" : "Inactive")
                                            </span>
                                        </td>
                                        <td>
                                            @if (regle.DerniereExecution.HasValue)
                                            {
                                                <span>@regle.DerniereExecution.Value.ToString("dd/MM/yyyy HH:mm")</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Jamais</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/qualite-donnees/regles/modifier/@regle.Id" class="btn btn-sm btn-primary">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                @if (_reglesEnExecution.Contains(regle.Id))
                                                {
                                                    <button class="btn btn-sm btn-warning" disabled>
                                                        <i class="bi bi-hourglass-split"></i>
                                                    </button>
                                                }
                                                else
                                                {
                                                    <button class="btn btn-sm btn-success" @onclick="() => ExecuterRegle(regle.Id)" title="Exécuter la règle">
                                                        <i class="bi bi-play"></i>
                                                    </button>
                                                }
                                                <button class="btn btn-sm @(regle.EstActive ? "btn-secondary" : "btn-success")" @onclick="() => ToggleRegleActive(regle)">
                                                    <i class="bi @(regle.EstActive ? "bi-pause" : "bi-check")"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger" @onclick="() => ConfirmerSuppression(regle)">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<Confirmation
    IsVisible="@_showDeleteConfirmation"
    Title="Confirmer la suppression"
    Message="Êtes-vous sûr de vouloir supprimer cette règle de qualité? Cette action est irréversible."
    ConfirmText="Supprimer"
    CancelText="Annuler"
    ConfirmButtonType="danger"
    OnConfirm="SupprimerRegle"
    OnCancel="AnnulerSuppression" />

@if (_showProgrammationModal)
{
    <div class="modal fade show" style="display: block;" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-clock me-2"></i>
                        Programmer l'exécution des règles de qualité
                    </h5>
                    <button type="button" class="btn-close" @onclick="FermerProgrammation"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="frequence" class="form-label">Fréquence d'exécution</label>
                            <select id="frequence" class="form-select" @bind="_frequenceProgrammee">
                                <option value="Quotidienne">Quotidienne</option>
                                <option value="Hebdomadaire">Hebdomadaire</option>
                                <option value="Mensuelle">Mensuelle</option>
                                <option value="Personnalisee">Personnalisée (Cron)</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="heure" class="form-label">Heure d'exécution</label>
                            <input type="time" id="heure" class="form-control" @bind="_heureProgrammee" />
                        </div>
                    </div>

                    @if (_frequenceProgrammee == "Hebdomadaire")
                    {
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Jour de la semaine</label>
                                <select class="form-select" @bind="_jourSemaine">
                                    <option value="1">Lundi</option>
                                    <option value="2">Mardi</option>
                                    <option value="3">Mercredi</option>
                                    <option value="4">Jeudi</option>
                                    <option value="5">Vendredi</option>
                                    <option value="6">Samedi</option>
                                    <option value="0">Dimanche</option>
                                </select>
                            </div>
                        </div>
                    }

                    @if (_frequenceProgrammee == "Mensuelle")
                    {
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label class="form-label">Jour du mois</label>
                                <input type="number" class="form-control" min="1" max="31" @bind="_jourMois" />
                            </div>
                        </div>
                    }

                    @if (_frequenceProgrammee == "Personnalisee")
                    {
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="cronExpression" class="form-label">Expression Cron</label>
                                <input type="text" id="cronExpression" class="form-control" @bind="_cronExpression"
                                       placeholder="0 0 2 * * ? (tous les jours à 2h00)" />
                                <div class="form-text">
                                    Format: seconde minute heure jour mois jour-semaine année
                                </div>
                            </div>
                        </div>
                    }

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="notificationEmail" @bind="_envoyerNotification" />
                                <label class="form-check-label" for="notificationEmail">
                                    Envoyer une notification par email après l'exécution
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        La programmation s'appliquera à toutes les règles actives. Les règles inactives seront ignorées.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="FermerProgrammation">Annuler</button>
                    <button type="button" class="btn btn-primary" @onclick="SauvegarderProgrammation">
                        <i class="bi bi-check"></i> Programmer
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

@code {
    private IEnumerable<RegleQualite>? _regles;
    private IEnumerable<ActifDonnees>? _actifs;
    private Dictionary<Guid, string> _actifsNoms = new();
    private bool _loading = true;
    private bool _showDeleteConfirmation = false;
    private RegleQualite? _regleToDelete;
    private int _reglesEnEchec = 0;

    // Variables pour l'exécution des règles
    private HashSet<Guid> _reglesEnExecution = new();
    private Dictionary<Guid, string> _resultatsExecution = new();
    private bool _executionEnCours = false;

    // Filtres
    private Guid _actifFilter = Guid.Empty;
    private string _typeFilter = string.Empty;
    private string _severiteFilter = string.Empty;
    private bool _activeFilter = false;

    // Variables pour la programmation
    private bool _showProgrammationModal = false;
    private string _frequenceProgrammee = "Quotidienne";
    private TimeOnly _heureProgrammee = new TimeOnly(2, 0);
    private int _jourSemaine = 1;
    private int _jourMois = 1;
    private string _cronExpression = "0 0 2 * * ?";
    private bool _envoyerNotification = true;

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    private async Task ChargerDonnees()
    {
        _loading = true;

        try
        {
            var toutesRegles = await RegleQualiteService.ObtenirTousAsync();
            _regles = ApplyFilters(toutesRegles);

            _actifs = await ActifDonneesService.ObtenirTousAsync();

            // Créer un dictionnaire pour un accès rapide aux noms des actifs
            _actifsNoms = _actifs.ToDictionary(a => a.Id, a => a.Nom);

            // Calculer le nombre de règles en échec
            var resultats = await ResultatRegleQualiteService.ObtenirTousAsync();
            _reglesEnEchec = resultats
                .Where(r => r.Statut == DomainEnums.StatutResultatRegleQualite.Echec)
                .Select(r => r.RegleQualiteId)
                .Distinct()
                .Count();
        }
        finally
        {
            _loading = false;
        }
    }

    private IEnumerable<RegleQualite> ApplyFilters(IEnumerable<RegleQualite> regles)
    {
        var filtered = regles;

        if (_actifFilter != Guid.Empty)
        {
            filtered = filtered.Where(r => r.ActifDonneesId == _actifFilter);
        }

        if (!string.IsNullOrEmpty(_typeFilter) && Enum.TryParse<TypeRegleQualite>(_typeFilter, out var type))
        {
            filtered = filtered.Where(r => r.Type == type);
        }

        if (!string.IsNullOrEmpty(_severiteFilter) && Enum.TryParse<SeveriteRegleQualite>(_severiteFilter, out var severite))
        {
            filtered = filtered.Where(r => r.Severite >= severite);
        }

        if (_activeFilter)
        {
            filtered = filtered.Where(r => r.EstActive);
        }

        return filtered.OrderByDescending(r => r.Severite).ThenBy(r => r.Nom);
    }

    private async Task FiltrerRegles()
    {
        await ChargerDonnees();
    }

    private void ConfirmerSuppression(RegleQualite regle)
    {
        _regleToDelete = regle;
        _showDeleteConfirmation = true;
    }

    private void AnnulerSuppression()
    {
        _showDeleteConfirmation = false;
        _regleToDelete = null;
    }

    private async Task SupprimerRegle()
    {
        if (_regleToDelete != null)
        {
            await RegleQualiteService.SupprimerAsync(_regleToDelete.Id);
            _showDeleteConfirmation = false;
            _regleToDelete = null;
            await ChargerDonnees();
            await ExceptionHandlingService.HandleSuccessAsync("La règle de qualité a été supprimée avec succès.");
        }
    }

    private async Task ToggleRegleActive(RegleQualite regle)
    {
        regle.EstActive = !regle.EstActive;
        await RegleQualiteService.ModifierAsync(regle);
        await ChargerDonnees();
        await ExceptionHandlingService.HandleSuccessAsync($"La règle de qualité a été {(regle.EstActive ? "activée" : "désactivée")} avec succès.");
    }

    private async Task ExecuterRegle(Guid regleId)
    {
        // Marquer la règle comme en cours d'exécution
        _reglesEnExecution.Add(regleId);
        StateHasChanged();

        try
        {
            Console.WriteLine($"🚀 Début d'exécution de la règle: {regleId}");

            var regle = _regles?.FirstOrDefault(r => r.Id == regleId);
            var nomRegle = regle?.Nom ?? "Règle inconnue";

            Console.WriteLine($"📋 Exécution de la règle: {nomRegle}");

            // Exécuter la règle
            await ResultatRegleQualiteService.ExecuterRegleAsync(regleId);

            Console.WriteLine($"✅ Règle exécutée avec succès: {nomRegle}");

            // Recharger les données pour mettre à jour l'affichage
            await ChargerDonnees();

            // Marquer le résultat comme succès
            _resultatsExecution[regleId] = "Succès";

            await ExceptionHandlingService.HandleSuccessAsync($"La règle '{nomRegle}' a été exécutée avec succès.");
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"❌ Erreur lors de l'exécution de la règle {regleId}: {ex.Message}");
            Console.Error.WriteLine($"📋 Détails de l'exception: {ex}");

            // Marquer le résultat comme échec
            _resultatsExecution[regleId] = $"Échec: {ex.Message}";

            await ExceptionHandlingService.HandleExceptionAsync(ex, $"Erreur lors de l'exécution de la règle de qualité: {ex.Message}");
        }
        finally
        {
            // Retirer la règle de la liste des exécutions en cours
            _reglesEnExecution.Remove(regleId);
            StateHasChanged();

            // Nettoyer le résultat après 5 secondes
            _ = Task.Delay(5000).ContinueWith(_ =>
            {
                _resultatsExecution.Remove(regleId);
                InvokeAsync(StateHasChanged);
            });
        }
    }

    private async Task ExecuterToutesReglesActives()
    {
        if (_regles == null || _executionEnCours) return;

        _executionEnCours = true;
        StateHasChanged();

        try
        {
            var reglesActives = _regles.Where(r => r.EstActive).ToList();

            if (!reglesActives.Any())
            {
                await ExceptionHandlingService.HandleErrorAsync("Aucune règle active trouvée.");
                return;
            }

            Console.WriteLine($"🚀 Début d'exécution de {reglesActives.Count} règles actives");

            int successes = 0;
            int failures = 0;

            foreach (var regle in reglesActives)
            {
                try
                {
                    _reglesEnExecution.Add(regle.Id);
                    StateHasChanged();

                    Console.WriteLine($"📋 Exécution de la règle: {regle.Nom}");
                    await ResultatRegleQualiteService.ExecuterRegleAsync(regle.Id);

                    successes++;
                    Console.WriteLine($"✅ Règle exécutée avec succès: {regle.Nom}");
                }
                catch (Exception ex)
                {
                    failures++;
                    Console.Error.WriteLine($"❌ Erreur lors de l'exécution de la règle {regle.Nom}: {ex.Message}");
                }
                finally
                {
                    _reglesEnExecution.Remove(regle.Id);
                }
            }

            // Recharger les données pour mettre à jour l'affichage
            await ChargerDonnees();

            // Afficher le résumé
            string message = $"Exécution terminée: {successes} succès, {failures} échecs sur {reglesActives.Count} règles.";

            if (failures == 0)
            {
                await ExceptionHandlingService.HandleSuccessAsync(message);
            }
            else
            {
                await ExceptionHandlingService.HandleErrorAsync(message);
            }

            Console.WriteLine($"📊 Résumé d'exécution: {message}");
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"❌ Erreur générale lors de l'exécution des règles: {ex.Message}");
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de l'exécution des règles de qualité");
        }
        finally
        {
            _executionEnCours = false;
            StateHasChanged();
        }
    }

    private void OuvrirProgrammation()
    {
        _showProgrammationModal = true;
    }

    private void FermerProgrammation()
    {
        _showProgrammationModal = false;
    }

    private async Task SauvegarderProgrammation()
    {
        try
        {
            // Construire l'expression cron selon la fréquence choisie
            string cronExpression = _frequenceProgrammee switch
            {
                "Quotidienne" => $"0 {_heureProgrammee.Minute} {_heureProgrammee.Hour} * * ?",
                "Hebdomadaire" => $"0 {_heureProgrammee.Minute} {_heureProgrammee.Hour} ? * {_jourSemaine}",
                "Mensuelle" => $"0 {_heureProgrammee.Minute} {_heureProgrammee.Hour} {_jourMois} * ?",
                "Personnalisee" => _cronExpression,
                _ => "0 0 2 * * ?"
            };

            Console.WriteLine($"📅 Programmation de l'exécution des règles de qualité:");
            Console.WriteLine($"   - Fréquence: {_frequenceProgrammee}");
            Console.WriteLine($"   - Expression Cron: {cronExpression}");
            Console.WriteLine($"   - Notification: {(_envoyerNotification ? "Oui" : "Non")}");

            // TODO: Implémenter l'appel au service de programmation
            // await ProgrammationService.ProgrammerExecutionReglesAsync(cronExpression, _envoyerNotification);

            _showProgrammationModal = false;
            await ExceptionHandlingService.HandleSuccessAsync($"Exécution programmée avec succès ({_frequenceProgrammee.ToLower()})");
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de la programmation de l'exécution");
        }
    }

    private string GetActifNom(Guid actifId)
    {
        return _actifsNoms.ContainsKey(actifId) ? _actifsNoms[actifId] : "Inconnu";
    }

    private string GetTypeRegleLabel(TypeRegleQualite type)
    {
        return type switch
        {
            TypeRegleQualite.NonDefini => "Non défini",
            TypeRegleQualite.Completude => "Complétude",
            TypeRegleQualite.Unicite => "Unicité",
            TypeRegleQualite.Coherence => "Cohérence",
            TypeRegleQualite.Precision => "Précision",
            TypeRegleQualite.Validite => "Validité",
            TypeRegleQualite.Fraicheur => "Fraîcheur",
            TypeRegleQualite.Personnalisee => "Personnalisée",
            TypeRegleQualite.ValidationDonnees => "Validation de données",
            TypeRegleQualite.IntegriteReferentielle => "Intégrité référentielle",
            TypeRegleQualite.PlageValeurs => "Plage de valeurs",
            TypeRegleQualite.FormatDonnees => "Format de données",
            TypeRegleQualite.RegleMetier => "Règle métier",
            TypeRegleQualite.Exactitude => "Exactitude",
            TypeRegleQualite.Integrite => "Intégrité",
            TypeRegleQualite.Autre => "Autre",
            _ => type.ToString()
        };
    }

    private string GetSeveriteLabel(SeveriteRegleQualite severite)
    {
        return severite switch
        {
            SeveriteRegleQualite.Critique => "Critique",
            SeveriteRegleQualite.Elevee => "Élevée",
            SeveriteRegleQualite.Moyenne => "Moyenne",
            SeveriteRegleQualite.Faible => "Faible",

            _ => severite.ToString()
        };
    }

    private string GetSeveriteColorClass(SeveriteRegleQualite severite)
    {
        return severite switch
        {
            SeveriteRegleQualite.Critique => "bg-danger",
            SeveriteRegleQualite.Elevee => "bg-danger",
            SeveriteRegleQualite.Moyenne => "bg-warning",
            SeveriteRegleQualite.Faible => "bg-info",

            _ => "bg-secondary"
        };
    }
}
