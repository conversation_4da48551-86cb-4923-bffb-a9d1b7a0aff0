@using DataHubGatineau.Web.Models.RiskManagement
@using DataHubGatineau.Web.Services.Interfaces
@inject IRisqueActifDonneesService RisqueService
@inject IExceptionHandlingService ExceptionHandlingService
@inject NavigationManager NavigationManager

<div class="container-fluid p-0">
    <!-- En-tête avec actions -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0">
            <i class="bi bi-shield-exclamation text-danger me-2"></i>
            Risques Identifiés (@_risques.Count)
        </h6>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary" @onclick="EvaluerRisquesAutomatiquement">
                <i class="bi bi-cpu me-1"></i> Évaluation Auto
            </button>
            <button class="btn btn-sm btn-primary" @onclick="AjouterRisque">
                <i class="bi bi-plus-circle me-1"></i> Nouveau Risque
            </button>
        </div>
    </div>

    <!-- Résumé des risques -->
    @if (_risques.Any())
    {
        <div class="row g-2 mb-4">
            <div class="col-md-3">
                <div class="card border-0 bg-danger bg-opacity-10 h-100">
                    <div class="card-body text-center py-2">
                        <div class="text-danger">
                            <i class="bi bi-exclamation-triangle-fill"></i>
                            <strong class="ms-1">@_statistiques.NombreCritiques</strong>
                        </div>
                        <small class="text-muted">Critiques</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 bg-warning bg-opacity-10 h-100">
                    <div class="card-body text-center py-2">
                        <div class="text-warning">
                            <i class="bi bi-exclamation-circle-fill"></i>
                            <strong class="ms-1">@_statistiques.NombreEleves</strong>
                        </div>
                        <small class="text-muted">Élevés</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 bg-info bg-opacity-10 h-100">
                    <div class="card-body text-center py-2">
                        <div class="text-info">
                            <i class="bi bi-info-circle-fill"></i>
                            <strong class="ms-1">@_statistiques.ScoreGlobal</strong>
                        </div>
                        <small class="text-muted">Score Global</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-0 bg-success bg-opacity-10 h-100">
                    <div class="card-body text-center py-2">
                        <div class="text-success">
                            <i class="bi bi-check-circle-fill"></i>
                            <strong class="ms-1">@_statistiques.NombreResolus</strong>
                        </div>
                        <small class="text-muted">Résolus</small>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Liste des risques -->
    @if (_loading)
    {
        <div class="text-center py-4">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
        </div>
    }
    else if (!_risques.Any())
    {
        <div class="text-center py-5">
            <i class="bi bi-shield-check text-success" style="font-size: 3rem;"></i>
            <p class="text-muted mt-3 mb-2">Aucun risque identifié pour cet actif</p>
            <button class="btn btn-sm btn-outline-primary" @onclick="EvaluerRisquesAutomatiquement">
                <i class="bi bi-cpu me-1"></i> Lancer une évaluation automatique
            </button>
        </div>
    }
    else
    {
        <div class="row g-3">
            @foreach (var risque in _risques.OrderByDescending(r => r.ScoreRisque))
            {
                <div class="col-12">
                    <div class="card border-0 shadow-sm @GetRisqueCardClass(risque)">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-start">
                                        <div class="me-3">
                                            <span class="badge @GetTypeRisqueBadgeClass(risque.TypeRisque) fs-6">
                                                @GetTypeRisqueLabel(risque.TypeRisque)
                                            </span>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">@risque.Description</h6>
                                            @if (!string.IsNullOrEmpty(risque.Impact))
                                            {
                                                <p class="text-muted small mb-0">
                                                    <strong>Impact:</strong> @risque.Impact
                                                </p>
                                            }
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2 text-center">
                                    <span class="badge @GetNiveauRisqueBadgeClass(risque.NiveauRisque) fs-6">
                                        @GetNiveauRisqueLabel(risque.NiveauRisque)
                                    </span>
                                    <div class="mt-1">
                                        <small class="text-muted">Score: </small>
                                        <strong class="@GetScoreClass(risque.ScoreRisque)">@risque.ScoreRisque</strong>
                                    </div>
                                </div>
                                <div class="col-md-2 text-center">
                                    <span class="badge @GetStatutBadgeClass(risque.Statut)">
                                        @GetStatutRisqueLabel(risque.Statut)
                                    </span>
                                    @if (risque.DateLimite.HasValue)
                                    {
                                        <div class="mt-1">
                                            <small class="@GetDateLimiteClass(risque.DateLimite.Value)">
                                                @risque.DateLimite.Value.ToString("dd/MM/yyyy")
                                            </small>
                                        </div>
                                    }
                                </div>
                                <div class="col-md-2 text-end">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" @onclick="() => VoirDetailsRisque(risque.Id)" title="Voir détails">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary" @onclick="() => ModifierRisque(risque.Id)" title="Modifier">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-success" @onclick="() => ChangerStatutRisque(risque.Id)" title="Changer statut">
                                            <i class="bi bi-arrow-repeat"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            @if (!string.IsNullOrEmpty(risque.MesuresMitigation))
                            {
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="bg-light rounded p-2">
                                            <small class="text-muted">
                                                <strong>Mesures de mitigation:</strong> @risque.MesuresMitigation
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>

@code {
    [Parameter]
    public ActifDonnees? ActifDonnees { get; set; }

    private List<RisqueActifDonnees> _risques = new();
    private bool _loading = true;
    private StatistiquesRisquesActif _statistiques = new();

    protected override async Task OnParametersSetAsync()
    {
        if (ActifDonnees != null)
        {
            await ChargerRisques();
        }
    }

    private async Task ChargerRisques()
    {
        if (ActifDonnees == null) return;

        try
        {
            _loading = true;
            var risques = await RisqueService.ObtenirParActifDonneesAsync(ActifDonnees.Id);
            _risques = risques.ToList();
            
            await CalculerStatistiques();
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement des risques");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task CalculerStatistiques()
    {
        if (ActifDonnees == null) return;

        var scoreGlobal = await RisqueService.CalculerScoreRisqueGlobalAsync(ActifDonnees.Id);
        
        _statistiques = new StatistiquesRisquesActif
        {
            NombreCritiques = _risques.Count(r => r.NiveauRisque == NiveauRisque.Critique),
            NombreEleves = _risques.Count(r => r.NiveauRisque == NiveauRisque.Eleve),
            ScoreGlobal = scoreGlobal,
            NombreResolus = _risques.Count(r => r.Statut == StatutRisque.Resolu)
        };
    }

    private async Task EvaluerRisquesAutomatiquement()
    {
        if (ActifDonnees == null) return;

        try
        {
            var nouveauxRisques = await RisqueService.EvaluerRisquesAutomatiquementAsync(ActifDonnees.Id);
            
            foreach (var risque in nouveauxRisques)
            {
                await RisqueService.AjouterAsync(risque);
            }

            await ChargerRisques();
            
            if (nouveauxRisques.Any())
            {
                // TODO: Afficher notification de succès
                Console.WriteLine($"{nouveauxRisques.Count()} nouveaux risques détectés automatiquement");
            }
            else
            {
                // TODO: Afficher notification d'information
                Console.WriteLine("Aucun nouveau risque détecté");
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de l'évaluation automatique des risques");
        }
    }

    private void AjouterRisque()
    {
        if (ActifDonnees != null)
        {
            NavigationManager.NavigateTo($"/gestion-risques/nouveau?actifId={ActifDonnees.Id}");
        }
    }

    private void VoirDetailsRisque(Guid risqueId)
    {
        NavigationManager.NavigateTo($"/gestion-risques/details/{risqueId}");
    }

    private void ModifierRisque(Guid risqueId)
    {
        NavigationManager.NavigateTo($"/gestion-risques/modifier/{risqueId}");
    }

    private async Task ChangerStatutRisque(Guid risqueId)
    {
        // TODO: Implémenter modal de changement de statut
        NavigationManager.NavigateTo($"/gestion-risques/statut/{risqueId}");
    }

    // Méthodes utilitaires pour l'affichage
    private string GetTypeRisqueLabel(TypeRisque type) => type switch
    {
        TypeRisque.Confidentialite => "Confidentialité",
        TypeRisque.Integrite => "Intégrité",
        TypeRisque.Disponibilite => "Disponibilité",
        TypeRisque.Conformite => "Conformité",
        TypeRisque.Qualite => "Qualité",
        TypeRisque.AccesNonAutorise => "Accès non autorisé",
        TypeRisque.FuiteDonnees => "Fuite de données",
        TypeRisque.PerteDonnees => "Perte de données",
        TypeRisque.Operationnel => "Opérationnel",
        TypeRisque.Reputation => "Réputation",
        _ => type.ToString()
    };

    private string GetNiveauRisqueLabel(NiveauRisque niveau) => niveau switch
    {
        NiveauRisque.TresFaible => "Très Faible",
        NiveauRisque.Faible => "Faible",
        NiveauRisque.Modere => "Modéré",
        NiveauRisque.Eleve => "Élevé",
        NiveauRisque.Critique => "Critique",
        _ => niveau.ToString()
    };

    private string GetStatutRisqueLabel(StatutRisque statut) => statut switch
    {
        StatutRisque.Identifie => "Identifié",
        StatutRisque.EnEvaluation => "En évaluation",
        StatutRisque.EnTraitement => "En traitement",
        StatutRisque.Mitige => "Mitigé",
        StatutRisque.Resolu => "Résolu",
        StatutRisque.Accepte => "Accepté",
        StatutRisque.Transfere => "Transféré",
        _ => statut.ToString()
    };

    private string GetTypeRisqueBadgeClass(TypeRisque type) => type switch
    {
        TypeRisque.Confidentialite => "bg-danger",
        TypeRisque.Integrite => "bg-warning",
        TypeRisque.Disponibilite => "bg-info",
        TypeRisque.Conformite => "bg-primary",
        TypeRisque.FuiteDonnees => "bg-danger",
        TypeRisque.PerteDonnees => "bg-danger",
        _ => "bg-secondary"
    };

    private string GetNiveauRisqueBadgeClass(NiveauRisque niveau) => niveau switch
    {
        NiveauRisque.TresFaible => "bg-success",
        NiveauRisque.Faible => "bg-success",
        NiveauRisque.Modere => "bg-warning",
        NiveauRisque.Eleve => "bg-danger",
        NiveauRisque.Critique => "bg-danger",
        _ => "bg-secondary"
    };

    private string GetStatutBadgeClass(StatutRisque statut) => statut switch
    {
        StatutRisque.Identifie => "bg-secondary",
        StatutRisque.EnEvaluation => "bg-info",
        StatutRisque.EnTraitement => "bg-warning",
        StatutRisque.Mitige => "bg-success",
        StatutRisque.Resolu => "bg-success",
        StatutRisque.Accepte => "bg-primary",
        StatutRisque.Transfere => "bg-info",
        _ => "bg-secondary"
    };

    private string GetRisqueCardClass(RisqueActifDonnees risque)
    {
        if (risque.NiveauRisque == NiveauRisque.Critique)
            return "border-danger";
        if (risque.NiveauRisque == NiveauRisque.Eleve)
            return "border-warning";
        return "";
    }

    private string GetScoreClass(int score) => score switch
    {
        >= 20 => "text-danger",
        >= 15 => "text-warning",
        >= 10 => "text-info",
        _ => "text-success"
    };

    private string GetDateLimiteClass(DateTime dateLimite)
    {
        if (dateLimite < DateTime.Now)
            return "text-danger";
        if (dateLimite < DateTime.Now.AddDays(7))
            return "text-warning";
        return "text-muted";
    }

    private class StatistiquesRisquesActif
    {
        public int NombreCritiques { get; set; }
        public int NombreEleves { get; set; }
        public int ScoreGlobal { get; set; }
        public int NombreResolus { get; set; }
    }
}
