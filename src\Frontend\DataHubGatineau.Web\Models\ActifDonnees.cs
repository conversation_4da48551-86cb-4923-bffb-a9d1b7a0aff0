using System.ComponentModel.DataAnnotations;
using DataHubGatineau.Web.Models.Policy;
using System.Text.Json.Serialization;

namespace DataHubGatineau.Web.Models;

/// <summary>
/// Représente un actif de données dans le système.
/// </summary>
public class ActifDonnees
{
    /// <summary>
    /// Obtient ou définit l'identifiant unique de l'actif de données.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Obtient ou définit le nom de l'actif de données.
    /// </summary>
    [Required(ErrorMessage = "Le nom est obligatoire")]
    [StringLength(100, ErrorMessage = "Le nom ne peut pas dépasser 100 caractères")]
    public string Nom { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit la description de l'actif de données.
    /// </summary>
    [StringLength(500, ErrorMessage = "La description ne peut pas dépasser 500 caractères")]
    public string? Description { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant du type de l'actif de données.
    /// </summary>
    [Required(ErrorMessage = "Le type est obligatoire")]
    public Guid? TypeActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit le type de l'actif de données.
    /// </summary>
    public TypeActifDonneesItem? TypeActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit le type de l'actif de données (chaîne).
    /// </summary>
    [JsonPropertyName("type")]
    public string? Type { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant du format de l'actif de données.
    /// </summary>
    public Guid? FormatActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit le format de l'actif de données.
    /// </summary>
    public FormatActifDonneesItem? FormatActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit le format de l'actif de données (chaîne).
    /// </summary>
    [JsonPropertyName("format")]
    public string? Format { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de la source de l'actif de données.
    /// </summary>
    public Guid? SourceActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit la source de l'actif de données.
    /// </summary>
    public SourceActifDonneesItem? SourceActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit la source de l'actif de données (chaîne).
    /// </summary>
    [JsonPropertyName("source")]
    public string? Source { get; set; }

    /// <summary>
    /// Obtient ou définit le propriétaire de l'actif de données.
    /// </summary>
    [StringLength(100, ErrorMessage = "Le propriétaire ne peut pas dépasser 100 caractères")]
    public string? Proprietaire { get; set; }

    /// <summary>
    /// Obtient ou définit la classification de sensibilité de l'actif de données.
    /// </summary>
    [Required(ErrorMessage = "La classification de sensibilité est obligatoire")]
    public ClassificationSensibilite ClassificationSensibilite { get; set; }

    /// <summary>
    /// Obtient ou définit la date de dernière mise à jour de l'actif de données.
    /// </summary>
    public DateTime? DateDerniereMiseAJour { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de la fréquence de mise à jour de l'actif de données.
    /// </summary>
    public Guid? FrequenceMiseAJourId { get; set; }

    /// <summary>
    /// Obtient ou définit la fréquence de mise à jour de l'actif de données.
    /// </summary>
    public FrequenceMiseAJour? FrequenceMiseAJour { get; set; }

    /// <summary>
    /// Obtient ou définit le chemin d'accès à l'actif de données.
    /// </summary>
    [StringLength(255, ErrorMessage = "Le chemin d'accès ne peut pas dépasser 255 caractères")]
    public string? CheminAcces { get; set; }

    /// <summary>
    /// Obtient ou définit la date de création de l'actif de données.
    /// </summary>
    public DateTime DateCreation { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit la date de dernière modification de l'actif de données.
    /// </summary>
    public DateTime DateModification { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a créé l'actif de données.
    /// </summary>
    public string CreePar { get; set; } = "Système";

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a modifié l'actif de données.
    /// </summary>
    public string ModifiePar { get; set; } = "Système";

    /// <summary>
    /// Obtient ou définit la liste des métadonnées associées à cet actif de données.
    /// </summary>
    public ICollection<Metadonnee>? Metadonnees { get; set; }

    /// <summary>
    /// Obtient ou définit la liste des règles de qualité associées à cet actif de données.
    /// </summary>
    public ICollection<RegleQualite>? ReglesQualite { get; set; }

    /// <summary>
    /// Obtient ou définit les termes du glossaire associés à cet actif de données.
    /// </summary>
    public ICollection<TermeGlossaire>? TermesGlossaire { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant du domaine de gouvernance associé.
    /// </summary>
    public Guid? DomaineGouvernanceId { get; set; }

    /// <summary>
    /// Obtient ou définit le domaine de gouvernance associé.
    /// </summary>
    public DomaineGouvernance? DomaineGouvernance { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de la connexion à la source de données.
    /// </summary>
    public Guid? ConnexionSourceDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit la connexion à la source de données.
    /// </summary>
    public ConnexionSourceDonnees? ConnexionSourceDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit si cet actif est un élément critique.
    /// </summary>
    public bool EstElementCritique { get; set; } = false;

    /// <summary>
    /// Obtient ou définit l'identifiant du statut de l'actif de données.
    /// </summary>
    public Guid? StatutActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit le statut de l'actif de données.
    /// </summary>
    public StatutActifDonnees? StatutActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit le statut de l'actif de données (chaîne).
    /// </summary>
    [JsonPropertyName("statut")]
    public string? Statut { get; set; }

    /// <summary>
    /// Obtient ou définit les workflows d'approbation associés à cet actif.
    /// </summary>
    public ICollection<WorkflowApprobation>? WorkflowsApprobation { get; set; }

    /// <summary>
    /// Obtient ou définit les produits de données associés à cet actif.
    /// </summary>
    public List<ProduitDonnees>? ProduitsDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit les politiques associées à cet actif.
    /// </summary>
    public List<Politique>? Politiques { get; set; }
}
