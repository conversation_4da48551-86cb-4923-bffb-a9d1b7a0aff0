using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Web.Models.Auditoria
{
    /// <summary>
    /// Modèle pour une entrée d'audit dans le système de gouvernance des données
    /// Inspiré des meilleures pratiques de Collibra et Microsoft Purview
    /// </summary>
    public class EntreeAudit
    {
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Action effectuée (ex: "Création utilisateur", "Modification actif données")
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// Type d'entité concernée (ex: "Utilisateur", "ActifDonnees", "Politique")
        /// </summary>
        [Required]
        [StringLength(100)]
        public string TypeEntite { get; set; } = string.Empty;

        /// <summary>
        /// Identifiant de l'entité concernée
        /// </summary>
        public Guid? EntiteId { get; set; }

        /// <summary>
        /// Nom ou titre de l'entité pour faciliter la lecture
        /// </summary>
        [StringLength(500)]
        public string? NomEntite { get; set; }

        /// <summary>
        /// Identifiant de l'utilisateur qui a effectué l'action
        /// </summary>
        [Required]
        [StringLength(100)]
        public string UtilisateurId { get; set; } = string.Empty;

        /// <summary>
        /// Nom d'utilisateur pour faciliter la lecture
        /// </summary>
        [Required]
        [StringLength(200)]
        public string NomUtilisateur { get; set; } = string.Empty;

        /// <summary>
        /// Nom complet de l'utilisateur
        /// </summary>
        [StringLength(300)]
        public string? NomCompletUtilisateur { get; set; }

        /// <summary>
        /// Adresse IP de l'utilisateur
        /// </summary>
        [StringLength(45)] // IPv6 max length
        public string? AdresseIP { get; set; }

        /// <summary>
        /// User Agent du navigateur
        /// </summary>
        [StringLength(1000)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// Identifiant de session
        /// </summary>
        [StringLength(100)]
        public string? SessionId { get; set; }

        /// <summary>
        /// Date et heure de l'action (UTC)
        /// </summary>
        public DateTime DateAction { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Valeurs avant modification (JSON)
        /// </summary>
        public string? ValeursAvant { get; set; }

        /// <summary>
        /// Valeurs après modification (JSON)
        /// </summary>
        public string? ValeursApres { get; set; }

        /// <summary>
        /// Commentaire ou description additionnelle
        /// </summary>
        [StringLength(2000)]
        public string? Commentaire { get; set; }

        /// <summary>
        /// Type d'action selon la classification Collibra/Purview
        /// </summary>
        public TypeActionAudit TypeAction { get; set; }

        /// <summary>
        /// Niveau de gravité de l'action
        /// </summary>
        public NiveauGraviteAudit NiveauGravite { get; set; }

        /// <summary>
        /// Catégorie de l'audit selon les domaines métier
        /// </summary>
        public CategorieAudit Categorie { get; set; }

        /// <summary>
        /// Contexte métier ou module de l'application
        /// </summary>
        [StringLength(100)]
        public string? Contexte { get; set; }

        /// <summary>
        /// Indique si l'action a réussi
        /// </summary>
        public bool Succes { get; set; } = true;

        /// <summary>
        /// Message d'erreur en cas d'échec
        /// </summary>
        [StringLength(2000)]
        public string? MessageErreur { get; set; }

        /// <summary>
        /// Durée d'exécution de l'opération
        /// </summary>
        public TimeSpan? DureeExecution { get; set; }

        /// <summary>
        /// Métadonnées additionnelles (JSON)
        /// </summary>
        public string? Metadonnees { get; set; }

        /// <summary>
        /// Identifiant de corrélation pour grouper les actions liées
        /// </summary>
        [StringLength(100)]
        public string? CorrelationId { get; set; }

        /// <summary>
        /// Domaine d'affaires concerné
        /// </summary>
        [StringLength(100)]
        public string? DomaineAffaires { get; set; }

        /// <summary>
        /// Classification de sensibilité des données
        /// </summary>
        public ClassificationSensibilite? ClassificationSensibilite { get; set; }

        /// <summary>
        /// Indique si cette action nécessite une conformité réglementaire
        /// </summary>
        public bool RequiertConformite { get; set; }

        /// <summary>
        /// Réglementations applicables (GDPR, SOX, etc.)
        /// </summary>
        [StringLength(500)]
        public string? ReglementationsApplicables { get; set; }
    }

    /// <summary>
    /// Types d'actions auditées selon les standards Collibra/Purview
    /// </summary>
    public enum TypeActionAudit
    {
        // Actions de base CRUD
        Creation,
        Lecture,
        Modification,
        Suppression,

        // Actions d'authentification et autorisation
        Connexion,
        Deconnexion,
        EchecConnexion,
        ChangementMotDePasse,
        AttributionRole,
        RevocationRole,

        // Actions de gouvernance des données
        ApprobationWorkflow,
        RejetWorkflow,
        ValidationQualite,
        EvaluationConformite,
        CertificationDonnees,

        // Actions d'administration
        ConfigurationSysteme,
        Sauvegarde,
        Restauration,
        MaintenanceSysteme,

        // Actions de données
        ExportDonnees,
        ImportDonnees,
        PartageActif,
        PublicationActif,
        ArchivageActif,

        // Actions de sécurité
        TentativeAccesNonAutorise,
        ModificationPermissions,
        DetectionAnomalie,
        ViolationPolitique
    }

    /// <summary>
    /// Niveaux de gravité pour l'audit
    /// </summary>
    public enum NiveauGraviteAudit
    {
        Trace,      // Actions de routine très détaillées
        Debug,      // Informations de débogage
        Information, // Actions normales d'information
        Avertissement, // Actions qui méritent attention
        Erreur,     // Erreurs non critiques
        Critique,   // Erreurs critiques affectant le système
        Securite,   // Événements de sécurité
        Conformite  // Événements liés à la conformité réglementaire
    }

    /// <summary>
    /// Catégories d'audit selon les domaines métier
    /// </summary>
    public enum CategorieAudit
    {
        // Gouvernance des données
        GouvernanceDonnees,
        QualiteDonnees,
        LineageDonnees,
        CatalogueDonnees,

        // Sécurité et conformité
        Securite,
        ConformiteReglementaire,
        GestionAcces,
        ProtectionDonnees,

        // Administration système
        AdministrationSysteme,
        GestionUtilisateurs,
        Configuration,
        Maintenance,

        // Processus métier
        WorkflowApprobation,
        GestionPolitiques,
        EvaluationRisques,
        Certification,

        // Intégration et données
        IntegrationDonnees,
        TransformationDonnees,
        PartageCollaboration,
        ArchivageRetention
    }

    /// <summary>
    /// Classification de sensibilité des données
    /// </summary>
    public enum ClassificationSensibilite
    {
        Public,
        Interne,
        Confidentiel,
        Restreint,
        Secret
    }
}
