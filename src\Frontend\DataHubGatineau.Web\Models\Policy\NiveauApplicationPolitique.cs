namespace DataHubGatineau.Web.Models.Policy;

/// <summary>
/// Énumération des niveaux d'application possibles pour une politique.
/// </summary>
public enum NiveauApplicationPolitique
{
    /// <summary>
    /// Politique applicable à toute l'organisation.
    /// </summary>
    Organisation,

    /// <summary>
    /// Politique applicable à un département spécifique.
    /// </summary>
    Departement,

    /// <summary>
    /// Politique applicable à un projet spécifique.
    /// </summary>
    Projet,

    /// <summary>
    /// Politique applicable à un domaine de données spécifique.
    /// </summary>
    Domaine,

    /// <summary>
    /// Politique applicable à un actif de données spécifique.
    /// </summary>
    Actif,

    /// <summary>
    /// Politique applicable à un système spécifique.
    /// </summary>
    Systeme
}
