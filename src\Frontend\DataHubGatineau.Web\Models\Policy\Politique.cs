using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace DataHubGatineau.Web.Models.Policy;

/// <summary>
/// Représente une politique ou un standard dans le système.
/// </summary>
public class Politique
{
    /// <summary>
    /// Obtient ou définit l'identifiant unique de la politique.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Obtient ou définit le code de référence de la politique.
    /// </summary>
    [Required(ErrorMessage = "Le code est obligatoire")]
    [StringLength(50, ErrorMessage = "Le code ne peut pas dépasser 50 caractères")]
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit le titre de la politique.
    /// </summary>
    [Required(ErrorMessage = "Le titre est obligatoire")]
    [StringLength(200, ErrorMessage = "Le titre ne peut pas dépasser 200 caractères")]
    public string Titre { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit la description de la politique.
    /// </summary>
    [StringLength(500, ErrorMessage = "La description ne peut pas dépasser 500 caractères")]
    public string? Description { get; set; }

    /// <summary>
    /// Obtient ou définit le type de politique.
    /// </summary>
    [Required(ErrorMessage = "Le type est obligatoire")]
    public TypePolitique Type { get; set; } = TypePolitique.Gouvernance;

    /// <summary>
    /// Obtient ou définit le statut de la politique.
    /// </summary>
    [Required(ErrorMessage = "Le statut est obligatoire")]
    public StatutPolitique Statut { get; set; } = StatutPolitique.Brouillon;

    /// <summary>
    /// Obtient ou définit le niveau d'application de la politique.
    /// </summary>
    [Required(ErrorMessage = "Le niveau d'application est obligatoire")]
    public NiveauApplicationPolitique NiveauApplication { get; set; }

    /// <summary>
    /// Obtient ou définit la catégorie de la politique.
    /// </summary>
    [Required(ErrorMessage = "La catégorie est obligatoire")]
    [StringLength(100, ErrorMessage = "La catégorie ne peut pas dépasser 100 caractères")]
    public string Categorie { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit le propriétaire de la politique.
    /// </summary>
    [Required(ErrorMessage = "Le propriétaire est obligatoire")]
    [StringLength(100, ErrorMessage = "Le propriétaire ne peut pas dépasser 100 caractères")]
    public string Proprietaire { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit l'identifiant de l'entité à laquelle la politique s'applique (département, projet, actif, etc.).
    /// </summary>
    public int? EntiteApplicationId { get; set; }

    /// <summary>
    /// Obtient ou définit le nom de l'entité à laquelle la politique s'applique.
    /// </summary>
    public string? EntiteApplicationNom { get; set; }

    /// <summary>
    /// Obtient ou définit le contenu complet de la politique.
    /// </summary>
    [Required(ErrorMessage = "Le contenu est obligatoire")]
    public string Contenu { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit les mots-clés associés à la politique.
    /// </summary>
    [StringLength(200, ErrorMessage = "Les mots-clés ne peuvent pas dépasser 200 caractères")]
    public string? MotsCles { get; set; }

    /// <summary>
    /// Obtient ou définit la version de la politique.
    /// </summary>
    [Required(ErrorMessage = "La version est obligatoire")]
    [StringLength(20, ErrorMessage = "La version ne peut pas dépasser 20 caractères")]
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// Obtient ou définit l'identifiant de la politique parente, si cette politique est une version d'une politique existante.
    /// </summary>
    public int? PolitiqueParenteId { get; set; }

    /// <summary>
    /// Obtient ou définit la date d'entrée en vigueur de la politique.
    /// </summary>
    public DateTime? DateEntreeVigueur { get; set; }

    /// <summary>
    /// Obtient ou définit la date d'expiration de la politique, si applicable.
    /// </summary>
    public DateTime? DateExpiration { get; set; }

    /// <summary>
    /// Obtient ou définit la date de la prochaine révision prévue.
    /// </summary>
    public DateTime? DateProchaineRevision { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de l'utilisateur qui a créé la politique.
    /// </summary>
    public string? CreePar { get; set; }

    /// <summary>
    /// Obtient ou définit le nom de l'utilisateur qui a créé la politique.
    /// </summary>
    public string? CreeParNom { get; set; }

    /// <summary>
    /// Obtient ou définit la date de création de la politique.
    /// </summary>
    public DateTime DateCreation { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit l'identifiant de l'utilisateur qui a modifié la politique en dernier.
    /// </summary>
    public string? ModifiePar { get; set; }

    /// <summary>
    /// Obtient ou définit le nom de l'utilisateur qui a modifié la politique en dernier.
    /// </summary>
    public string? ModifieParNom { get; set; }

    /// <summary>
    /// Obtient ou définit la date de dernière modification de la politique.
    /// </summary>
    public DateTime DateModification { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit l'identifiant de l'utilisateur qui a approuvé la politique, si applicable.
    /// </summary>
    public string? ApprouvePar { get; set; }

    /// <summary>
    /// Obtient ou définit le nom de l'utilisateur qui a approuvé la politique, si applicable.
    /// </summary>
    public string? ApprouveParNom { get; set; }

    /// <summary>
    /// Obtient ou définit la date d'approbation de la politique, si applicable.
    /// </summary>
    public DateTime? DateApprobation { get; set; }

    /// <summary>
    /// Obtient ou définit les pièces jointes associées à la politique au format JSON.
    /// </summary>
    public string? PiecesJointesJson { get; set; }

    /// <summary>
    /// Obtient ou définit les références à d'autres politiques ou documents au format JSON.
    /// </summary>
    public string? ReferencesJson { get; set; }

    /// <summary>
    /// Obtient ou définit l'historique des modifications de la politique au format JSON.
    /// </summary>
    public string? HistoriqueModificationsJson { get; set; }

    /// <summary>
    /// Obtient ou définit les commentaires sur la politique au format JSON.
    /// </summary>
    public string? CommentairesJson { get; set; }

    /// <summary>
    /// Obtient ou définit si la politique est active.
    /// </summary>
    public bool EstActive { get; set; } = true;

    /// <summary>
    /// Obtient ou définit les actifs de données auxquels cette politique s'applique.
    /// </summary>
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<ActifDonnees>? ActifsDonnees { get; set; }
}

