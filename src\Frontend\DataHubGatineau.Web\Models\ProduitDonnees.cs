using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Web.Models;

/// <summary>
/// Représente un produit de données dans le système.
/// </summary>
public class ProduitDonnees
{
    /// <summary>
    /// Obtient ou définit l'identifiant du produit de données.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Obtient ou définit le nom du produit de données.
    /// </summary>
    [Required(ErrorMessage = "Le nom est obligatoire")]
    [StringLength(100, ErrorMessage = "Le nom ne peut pas dépasser 100 caractères")]
    public string Nom { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit la description du produit de données.
    /// </summary>
    [StringLength(500, ErrorMessage = "La description ne peut pas dépasser 500 caractères")]
    public string? Description { get; set; }

    /// <summary>
    /// Obtient ou définit le propriétaire du produit de données.
    /// </summary>
    [StringLength(100, ErrorMessage = "Le propriétaire ne peut pas dépasser 100 caractères")]
    public string? Proprietaire { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant du domaine de gouvernance associé.
    /// </summary>
    [Required(ErrorMessage = "Le domaine de gouvernance est obligatoire")]
    public Guid DomaineGouvernanceId { get; set; }

    /// <summary>
    /// Obtient ou définit le nom du domaine de gouvernance associé.
    /// </summary>
    public string DomaineGouvernanceNom { get; set; }

    /// <summary>
    /// Obtient ou définit les actifs de données associés à ce produit.
    /// </summary>
    public List<ActifDonnees>? ActifsDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit les identifiants des actifs de données inclus dans ce produit.
    /// </summary>
    public List<Guid> ActifsDonneesIds { get; set; } = new List<Guid>();

    /// <summary>
    /// Obtient ou définit la date de création du produit de données.
    /// </summary>
    public DateTime DateCreation { get; set; }

    /// <summary>
    /// Obtient ou définit la date de dernière modification du produit de données.
    /// </summary>
    public DateTime DateModification { get; set; }

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a créé le produit de données.
    /// </summary>
    public string CreePar { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a modifié le produit de données.
    /// </summary>
    public string ModifiePar { get; set; } = string.Empty;
}
