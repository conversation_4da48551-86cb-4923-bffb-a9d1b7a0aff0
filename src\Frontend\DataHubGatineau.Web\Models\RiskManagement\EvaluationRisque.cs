using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Web.Models.RiskManagement;

/// <summary>
/// Représente une évaluation de risque pour un actif de données.
/// </summary>
public class EvaluationRisque
{
    /// <summary>
    /// Obtient ou définit l'identifiant unique de l'évaluation.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de l'actif de données évalué.
    /// </summary>
    [Required(ErrorMessage = "L'actif de données est obligatoire")]
    public Guid ActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit l'actif de données évalué.
    /// </summary>
    public ActifDonnees? ActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit le titre de l'évaluation.
    /// </summary>
    [Required(ErrorMessage = "Le titre est obligatoire")]
    [StringLength(200, ErrorMessage = "Le titre ne peut pas dépasser 200 caractères")]
    public string Titre { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit la description de l'évaluation.
    /// </summary>
    [StringLength(1000, ErrorMessage = "La description ne peut pas dépasser 1000 caractères")]
    public string? Description { get; set; }

    /// <summary>
    /// Obtient ou définit le type d'évaluation.
    /// </summary>
    public TypeEvaluation TypeEvaluation { get; set; }

    /// <summary>
    /// Obtient ou définit le statut de l'évaluation.
    /// </summary>
    public StatutEvaluation Statut { get; set; } = StatutEvaluation.Planifiee;

    /// <summary>
    /// Obtient ou définit la date de début de l'évaluation.
    /// </summary>
    public DateTime DateDebut { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit la date de fin prévue de l'évaluation.
    /// </summary>
    public DateTime? DateFinPrevue { get; set; }

    /// <summary>
    /// Obtient ou définit la date de fin réelle de l'évaluation.
    /// </summary>
    public DateTime? DateFinReelle { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de l'évaluateur principal.
    /// </summary>
    public string? EvaluateurId { get; set; }

    /// <summary>
    /// Obtient ou définit le nom de l'évaluateur principal.
    /// </summary>
    public string? EvaluateurNom { get; set; }

    /// <summary>
    /// Obtient ou définit la liste des risques identifiés.
    /// </summary>
    public List<RisqueActifDonnees> RisquesIdentifies { get; set; } = new List<RisqueActifDonnees>();

    /// <summary>
    /// Obtient ou définit le score global de risque.
    /// </summary>
    [Range(0, 25, ErrorMessage = "Le score global doit être entre 0 et 25")]
    public int ScoreGlobalRisque { get; set; }

    /// <summary>
    /// Obtient ou définit le niveau de risque global.
    /// </summary>
    public NiveauRisque NiveauRisqueGlobal { get; set; }

    /// <summary>
    /// Obtient ou définit les recommandations de l'évaluation.
    /// </summary>
    [StringLength(2000, ErrorMessage = "Les recommandations ne peuvent pas dépasser 2000 caractères")]
    public string? Recommandations { get; set; }

    /// <summary>
    /// Obtient ou définit les actions correctives proposées.
    /// </summary>
    [StringLength(2000, ErrorMessage = "Les actions correctives ne peuvent pas dépasser 2000 caractères")]
    public string? ActionsCorrectives { get; set; }

    /// <summary>
    /// Obtient ou définit la date de la prochaine évaluation.
    /// </summary>
    public DateTime? DateProchaineEvaluation { get; set; }

    /// <summary>
    /// Obtient ou définit la fréquence d'évaluation recommandée.
    /// </summary>
    public FrequenceEvaluation? FrequenceRecommandee { get; set; }

    /// <summary>
    /// Obtient ou définit les critères d'évaluation utilisés.
    /// </summary>
    public List<CritereEvaluation> CriteresEvaluation { get; set; } = new List<CritereEvaluation>();

    /// <summary>
    /// Obtient ou définit les documents de support de l'évaluation.
    /// </summary>
    public List<DocumentEvaluation> DocumentsSupport { get; set; } = new List<DocumentEvaluation>();

    /// <summary>
    /// Obtient ou définit la date de création de l'enregistrement.
    /// </summary>
    public DateTime DateCreation { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit la date de dernière modification.
    /// </summary>
    public DateTime DateModification { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a créé l'enregistrement.
    /// </summary>
    public string CreePar { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a modifié l'enregistrement.
    /// </summary>
    public string ModifiePar { get; set; } = string.Empty;

    /// <summary>
    /// Calcule le niveau de risque global basé sur le score.
    /// </summary>
    public void CalculerNiveauRisqueGlobal()
    {
        NiveauRisqueGlobal = ScoreGlobalRisque switch
        {
            <= 5 => NiveauRisque.TresFaible,
            <= 10 => NiveauRisque.Faible,
            <= 15 => NiveauRisque.Modere,
            <= 20 => NiveauRisque.Eleve,
            _ => NiveauRisque.Critique
        };
    }
}

/// <summary>
/// Énumération des types d'évaluation.
/// </summary>
public enum TypeEvaluation
{
    /// <summary>
    /// Évaluation initiale.
    /// </summary>
    Initiale = 1,

    /// <summary>
    /// Évaluation périodique.
    /// </summary>
    Periodique = 2,

    /// <summary>
    /// Évaluation suite à un incident.
    /// </summary>
    PostIncident = 3,

    /// <summary>
    /// Évaluation suite à un changement.
    /// </summary>
    PostChangement = 4,

    /// <summary>
    /// Évaluation de conformité.
    /// </summary>
    Conformite = 5
}

/// <summary>
/// Énumération des statuts d'évaluation.
/// </summary>
public enum StatutEvaluation
{
    /// <summary>
    /// Évaluation planifiée.
    /// </summary>
    Planifiee = 1,

    /// <summary>
    /// Évaluation en cours.
    /// </summary>
    EnCours = 2,

    /// <summary>
    /// Évaluation en attente de validation.
    /// </summary>
    EnAttenteValidation = 3,

    /// <summary>
    /// Évaluation terminée.
    /// </summary>
    Terminee = 4,

    /// <summary>
    /// Évaluation approuvée.
    /// </summary>
    Approuvee = 5,

    /// <summary>
    /// Évaluation rejetée.
    /// </summary>
    Rejetee = 6
}

/// <summary>
/// Énumération des fréquences d'évaluation.
/// </summary>
public enum FrequenceEvaluation
{
    /// <summary>
    /// Évaluation mensuelle.
    /// </summary>
    Mensuelle = 1,

    /// <summary>
    /// Évaluation trimestrielle.
    /// </summary>
    Trimestrielle = 2,

    /// <summary>
    /// Évaluation semestrielle.
    /// </summary>
    Semestrielle = 3,

    /// <summary>
    /// Évaluation annuelle.
    /// </summary>
    Annuelle = 4,

    /// <summary>
    /// Évaluation sur demande.
    /// </summary>
    SurDemande = 5
}

/// <summary>
/// Représente un critère d'évaluation.
/// </summary>
public class CritereEvaluation
{
    public Guid Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Poids { get; set; } = 1;
    public int Score { get; set; }
    public string? Commentaire { get; set; }
}

/// <summary>
/// Représente un document de support pour l'évaluation.
/// </summary>
public class DocumentEvaluation
{
    public Guid Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string CheminFichier { get; set; } = string.Empty;
    public DateTime DateAjout { get; set; } = DateTime.Now;
    public string AjoutePar { get; set; } = string.Empty;
}
