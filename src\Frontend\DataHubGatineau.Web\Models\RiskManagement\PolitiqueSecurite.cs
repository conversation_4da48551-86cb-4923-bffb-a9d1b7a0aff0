using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Web.Models.RiskManagement;

/// <summary>
/// Représente une politique de sécurité pour la gestion des données.
/// </summary>
public class PolitiqueSecurite
{
    /// <summary>
    /// Obtient ou définit l'identifiant unique de la politique.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Obtient ou définit le nom de la politique.
    /// </summary>
    [Required(ErrorMessage = "Le nom est obligatoire")]
    [StringLength(200, ErrorMessage = "Le nom ne peut pas dépasser 200 caractères")]
    public string Nom { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit la description de la politique.
    /// </summary>
    [StringLength(1000, ErrorMessage = "La description ne peut pas dépasser 1000 caractères")]
    public string? Description { get; set; }

    /// <summary>
    /// Obtient ou définit le type de politique.
    /// </summary>
    public TypePolitique TypePolitique { get; set; }

    /// <summary>
    /// Obtient ou définit le niveau de classification requis.
    /// </summary>
    public string? ClassificationRequise { get; set; }

    /// <summary>
    /// Obtient ou définit les règles d'accès.
    /// </summary>
    public List<RegleAcces> ReglesAcces { get; set; } = new List<RegleAcces>();

    /// <summary>
    /// Obtient ou définit les contrôles de sécurité requis.
    /// </summary>
    public List<ControleSecurite> ControlesSecurite { get; set; } = new List<ControleSecurite>();

    /// <summary>
    /// Obtient ou définit la durée de rétention des données.
    /// </summary>
    public int? DureeRetentionJours { get; set; }

    /// <summary>
    /// Obtient ou définit les exigences de chiffrement.
    /// </summary>
    public ExigencesChiffrement? ExigencesChiffrement { get; set; }

    /// <summary>
    /// Obtient ou définit les exigences d'audit.
    /// </summary>
    public ExigencesAudit? ExigencesAudit { get; set; }

    /// <summary>
    /// Obtient ou définit si la politique est active.
    /// </summary>
    public bool EstActive { get; set; } = true;

    /// <summary>
    /// Obtient ou définit la date d'entrée en vigueur.
    /// </summary>
    public DateTime DateEntreeVigueur { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit la date d'expiration de la politique.
    /// </summary>
    public DateTime? DateExpiration { get; set; }

    /// <summary>
    /// Obtient ou définit la version de la politique.
    /// </summary>
    [Required(ErrorMessage = "La version est obligatoire")]
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// Obtient ou définit l'identifiant du propriétaire de la politique.
    /// </summary>
    public string? ProprietaireId { get; set; }

    /// <summary>
    /// Obtient ou définit le nom du propriétaire de la politique.
    /// </summary>
    public string? ProprietaireNom { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de l'approbateur.
    /// </summary>
    public string? ApprovateurId { get; set; }

    /// <summary>
    /// Obtient ou définit le nom de l'approbateur.
    /// </summary>
    public string? ApprovateurNom { get; set; }

    /// <summary>
    /// Obtient ou définit la date d'approbation.
    /// </summary>
    public DateTime? DateApprobation { get; set; }

    /// <summary>
    /// Obtient ou définit les références réglementaires.
    /// </summary>
    public List<ReferenceReglementaire> ReferencesReglementaires { get; set; } = new List<ReferenceReglementaire>();

    /// <summary>
    /// Obtient ou définit les exceptions autorisées.
    /// </summary>
    public List<ExceptionPolitique> Exceptions { get; set; } = new List<ExceptionPolitique>();

    /// <summary>
    /// Obtient ou définit la date de création.
    /// </summary>
    public DateTime DateCreation { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit la date de dernière modification.
    /// </summary>
    public DateTime DateModification { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a créé l'enregistrement.
    /// </summary>
    public string CreePar { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a modifié l'enregistrement.
    /// </summary>
    public string ModifiePar { get; set; } = string.Empty;
}

/// <summary>
/// Énumération des types de politique.
/// </summary>
public enum TypePolitique
{
    /// <summary>
    /// Politique d'accès aux données.
    /// </summary>
    AccesDonnees = 1,

    /// <summary>
    /// Politique de classification des données.
    /// </summary>
    ClassificationDonnees = 2,

    /// <summary>
    /// Politique de rétention des données.
    /// </summary>
    RetentionDonnees = 3,

    /// <summary>
    /// Politique de chiffrement.
    /// </summary>
    Chiffrement = 4,

    /// <summary>
    /// Politique de sauvegarde.
    /// </summary>
    Sauvegarde = 5,

    /// <summary>
    /// Politique de conformité.
    /// </summary>
    Conformite = 6,

    /// <summary>
    /// Politique de partage de données.
    /// </summary>
    PartageDonnees = 7
}

/// <summary>
/// Représente une règle d'accès.
/// </summary>
public class RegleAcces
{
    public Guid Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TypeAcces TypeAcces { get; set; }
    public List<string> RolesAutorises { get; set; } = new List<string>();
    public List<string> UtilisateursAutorises { get; set; } = new List<string>();
    public string? Conditions { get; set; }
    public bool EstActive { get; set; } = true;
}

/// <summary>
/// Énumération des types d'accès.
/// </summary>
public enum TypeAcces
{
    Lecture = 1,
    Ecriture = 2,
    Modification = 3,
    Suppression = 4,
    Administration = 5
}

/// <summary>
/// Représente un contrôle de sécurité.
/// </summary>
public class ControleSecurite
{
    public Guid Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TypeControle TypeControle { get; set; }
    public NiveauControle NiveauControle { get; set; }
    public string? Parametres { get; set; }
    public bool EstObligatoire { get; set; } = true;
}

/// <summary>
/// Énumération des types de contrôle.
/// </summary>
public enum TypeControle
{
    Authentification = 1,
    Autorisation = 2,
    Chiffrement = 3,
    Audit = 4,
    Monitoring = 5,
    Masquage = 6,
    Anonymisation = 7
}

/// <summary>
/// Énumération des niveaux de contrôle.
/// </summary>
public enum NiveauControle
{
    Basique = 1,
    Standard = 2,
    Renforce = 3,
    Maximum = 4
}

/// <summary>
/// Représente les exigences de chiffrement.
/// </summary>
public class ExigencesChiffrement
{
    public bool ChiffrementEnTransit { get; set; } = true;
    public bool ChiffrementAuRepos { get; set; } = true;
    public string AlgorithmeChiffrement { get; set; } = "AES-256";
    public string GestionCles { get; set; } = string.Empty;
}

/// <summary>
/// Représente les exigences d'audit.
/// </summary>
public class ExigencesAudit
{
    public bool AuditAcces { get; set; } = true;
    public bool AuditModifications { get; set; } = true;
    public bool AuditExportations { get; set; } = true;
    public int DureeRetentionLogsJours { get; set; } = 365;
    public string NiveauDetail { get; set; } = "Standard";
}

/// <summary>
/// Représente une référence réglementaire.
/// </summary>
public class ReferenceReglementaire
{
    public Guid Id { get; set; }
    public string Nom { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Reference { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
}

/// <summary>
/// Représente une exception à la politique.
/// </summary>
public class ExceptionPolitique
{
    public Guid Id { get; set; }
    public string Justification { get; set; } = string.Empty;
    public DateTime DateDebut { get; set; }
    public DateTime DateFin { get; set; }
    public string ApprouvePar { get; set; } = string.Empty;
    public DateTime DateApprobation { get; set; }
    public bool EstActive { get; set; } = true;
}
