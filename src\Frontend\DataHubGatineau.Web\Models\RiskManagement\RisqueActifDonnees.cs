using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Web.Models.RiskManagement;

/// <summary>
/// Représente un risque associé à un actif de données.
/// </summary>
public class RisqueActifDonnees
{
    /// <summary>
    /// Obtient ou définit l'identifiant unique du risque.
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Obtient ou définit l'identifiant de l'actif de données concerné.
    /// </summary>
    [Required(ErrorMessage = "L'actif de données est obligatoire")]
    public Guid ActifDonneesId { get; set; }

    /// <summary>
    /// Obtient ou définit l'actif de données concerné.
    /// </summary>
    public ActifDonnees? ActifDonnees { get; set; }

    /// <summary>
    /// Obtient ou définit le type de risque.
    /// </summary>
    [Required(ErrorMessage = "Le type de risque est obligatoire")]
    public TypeRisque TypeRisque { get; set; }

    /// <summary>
    /// Obtient ou définit le niveau de risque.
    /// </summary>
    [Required(ErrorMessage = "Le niveau de risque est obligatoire")]
    public NiveauRisque NiveauRisque { get; set; }

    /// <summary>
    /// Obtient ou définit la description du risque.
    /// </summary>
    [Required(ErrorMessage = "La description est obligatoire")]
    [StringLength(1000, ErrorMessage = "La description ne peut pas dépasser 1000 caractères")]
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit l'impact potentiel du risque.
    /// </summary>
    [StringLength(500, ErrorMessage = "L'impact ne peut pas dépasser 500 caractères")]
    public string? Impact { get; set; }

    /// <summary>
    /// Obtient ou définit la probabilité d'occurrence du risque.
    /// </summary>
    [Range(1, 5, ErrorMessage = "La probabilité doit être entre 1 et 5")]
    public int Probabilite { get; set; }

    /// <summary>
    /// Obtient ou définit l'impact du risque sur une échelle de 1 à 5.
    /// </summary>
    [Range(1, 5, ErrorMessage = "L'impact doit être entre 1 et 5")]
    public int ImpactScore { get; set; }

    /// <summary>
    /// Obtient le score de risque calculé (Probabilité × Impact).
    /// </summary>
    public int ScoreRisque => Probabilite * ImpactScore;

    /// <summary>
    /// Obtient ou définit les mesures de mitigation proposées.
    /// </summary>
    [StringLength(1000, ErrorMessage = "Les mesures de mitigation ne peuvent pas dépasser 1000 caractères")]
    public string? MesuresMitigation { get; set; }

    /// <summary>
    /// Obtient ou définit le statut du risque.
    /// </summary>
    public StatutRisque Statut { get; set; } = StatutRisque.Identifie;

    /// <summary>
    /// Obtient ou définit l'identifiant du responsable du risque.
    /// </summary>
    public string? ResponsableId { get; set; }

    /// <summary>
    /// Obtient ou définit le nom du responsable du risque.
    /// </summary>
    public string? ResponsableNom { get; set; }

    /// <summary>
    /// Obtient ou définit la date d'identification du risque.
    /// </summary>
    public DateTime DateIdentification { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit la date limite pour traiter le risque.
    /// </summary>
    public DateTime? DateLimite { get; set; }

    /// <summary>
    /// Obtient ou définit la date de résolution du risque.
    /// </summary>
    public DateTime? DateResolution { get; set; }

    /// <summary>
    /// Obtient ou définit la date de création de l'enregistrement.
    /// </summary>
    public DateTime DateCreation { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit la date de dernière modification.
    /// </summary>
    public DateTime DateModification { get; set; } = DateTime.Now;

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a créé l'enregistrement.
    /// </summary>
    public string CreePar { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a modifié l'enregistrement.
    /// </summary>
    public string ModifiePar { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit les commentaires additionnels.
    /// </summary>
    public string? Commentaires { get; set; }

    /// <summary>
    /// Obtient ou définit si le risque est archivé.
    /// </summary>
    public bool EstArchive { get; set; } = false;
}

/// <summary>
/// Énumération des types de risques.
/// </summary>
public enum TypeRisque
{
    /// <summary>
    /// Risque de confidentialité des données.
    /// </summary>
    Confidentialite = 1,

    /// <summary>
    /// Risque d'intégrité des données.
    /// </summary>
    Integrite = 2,

    /// <summary>
    /// Risque de disponibilité des données.
    /// </summary>
    Disponibilite = 3,

    /// <summary>
    /// Risque de conformité réglementaire.
    /// </summary>
    Conformite = 4,

    /// <summary>
    /// Risque de qualité des données.
    /// </summary>
    Qualite = 5,

    /// <summary>
    /// Risque d'accès non autorisé.
    /// </summary>
    AccesNonAutorise = 6,

    /// <summary>
    /// Risque de fuite de données.
    /// </summary>
    FuiteDonnees = 7,

    /// <summary>
    /// Risque de perte de données.
    /// </summary>
    PerteDonnees = 8,

    /// <summary>
    /// Risque opérationnel.
    /// </summary>
    Operationnel = 9,

    /// <summary>
    /// Risque de réputation.
    /// </summary>
    Reputation = 10
}

/// <summary>
/// Énumération des niveaux de risque.
/// </summary>
public enum NiveauRisque
{
    /// <summary>
    /// Risque très faible.
    /// </summary>
    TresFaible = 1,

    /// <summary>
    /// Risque faible.
    /// </summary>
    Faible = 2,

    /// <summary>
    /// Risque modéré.
    /// </summary>
    Modere = 3,

    /// <summary>
    /// Risque élevé.
    /// </summary>
    Eleve = 4,

    /// <summary>
    /// Risque critique.
    /// </summary>
    Critique = 5
}

/// <summary>
/// Énumération des statuts de risque.
/// </summary>
public enum StatutRisque
{
    /// <summary>
    /// Risque identifié mais pas encore traité.
    /// </summary>
    Identifie = 1,

    /// <summary>
    /// Risque en cours d'évaluation.
    /// </summary>
    EnEvaluation = 2,

    /// <summary>
    /// Risque en cours de traitement.
    /// </summary>
    EnTraitement = 3,

    /// <summary>
    /// Risque mitigé.
    /// </summary>
    Mitige = 4,

    /// <summary>
    /// Risque résolu.
    /// </summary>
    Resolu = 5,

    /// <summary>
    /// Risque accepté.
    /// </summary>
    Accepte = 6,

    /// <summary>
    /// Risque transféré.
    /// </summary>
    Transfere = 7
}
