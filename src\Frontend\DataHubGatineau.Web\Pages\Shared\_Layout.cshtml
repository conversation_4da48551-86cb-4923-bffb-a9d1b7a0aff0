@using Microsoft.AspNetCore.Components.Web
@namespace DataHubGatineau.Web.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="fr-CA">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="~/" />
    <link rel="stylesheet" href="lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="app.css" rel="stylesheet" />
    <link href="css/layout.css" rel="stylesheet" />
    <link href="css/smart-search.css" rel="stylesheet" />
    <link href="DataHubGatineau.Web.styles.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" />
    <link rel="icon" type="image/png" href="favicon.png"/>
    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />
</head>
<body>
    @RenderBody()

    <div id="blazor-error-ui" style="display: none;">
        <environment include="Staging,Production">
            Une erreur s'est produite. Cette application pourrait ne plus répondre jusqu'à ce qu'elle soit rechargée.
        </environment>
        <environment include="Development">
            Une exception non gérée s'est produite. Voir les outils de développement du navigateur pour plus de détails.
        </environment>
        <a href="" class="reload">Recharger</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.server.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="js/notification-badge.js"></script>
    <script src="js/notifications.js?v=2"></script>
    <script src="js/search-bar.js"></script>
    <script src="js/real-time-search.js"></script>
    <script src="js/dashboard-charts.js"></script>
    <script src="js/fileUtils.js"></script>
    <script src="js/custom-modals.js?v=3"></script>
    <script>
        // Inicializar todos los dropdowns de Bootstrap
        document.addEventListener('DOMContentLoaded', function() {
            var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            var dropdownList = dropdownElementList.map(function(dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
        });
    </script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
