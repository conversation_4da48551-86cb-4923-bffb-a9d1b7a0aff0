using Blazored.LocalStorage;
using DataHubGatineau.Web.Components;
using DataHubGatineau.Web.Services;
using DataHubGatineau.Web.Services.Auth;
using DataHubGatineau.Web.Services.Implementations;
using DataHubGatineau.Web.Services.Interfaces;
using DataHubGatineau.Web.Services.MockServices;
using DataHubGatineau.Web.Services.Mock;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.JSInterop;
using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddServerSideBlazor(options =>
{
    options.DetailedErrors = builder.Environment.IsDevelopment();
});
builder.Services.AddRazorPages();
builder.Services.AddControllersWithViews()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
        options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles;
    });

// Add authentication services
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddAuthorizationCore();
builder.Services.AddScoped<AuthenticationStateProvider, CustomAuthStateProvider>();
builder.Services.AddScoped<IAuthService, AuthService>();

// Add cookie authentication for hybrid login
builder.Services.AddAuthentication("Cookies")
    .AddCookie("Cookies", options =>
    {
        options.LoginPath = "/AuthentificationHybride/Connexion";
        options.LogoutPath = "/AuthentificationHybride/Deconnexion";
        options.AccessDeniedPath = "/AuthentificationHybride/AccesRefuse";
        options.ExpireTimeSpan = TimeSpan.FromHours(8);
        options.SlidingExpiration = true;
        options.Cookie.Name = "DataHubGatineau.Auth";
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
        options.Cookie.SameSite = SameSiteMode.Lax;
    });

// Configure HttpClient and HttpClientFactory
var apiBaseUrl = builder.Configuration["ApiBaseUrl"] ?? "http://localhost:5187";
Console.WriteLine($"Utilisation de l'URL de l'API: {apiBaseUrl}");

// Add HttpClientFactory for dependency injection
builder.Services.AddHttpClient();

// Configure default HttpClient for Blazor components
builder.Services.AddScoped(sp => new HttpClient
{
    BaseAddress = new Uri(apiBaseUrl),
    Timeout = TimeSpan.FromSeconds(30) // Timeout optimizado para consultas rápidas
});

// Register API services
// Déterminer si on utilise les services réels ou les services simulés
bool useRealServices = builder.Configuration.GetValue<bool>("UseRealServices", true); // Utiliser les services réels par défaut
Console.WriteLine($"Configuration UseRealServices: {useRealServices}");

if (useRealServices)
{
    Console.WriteLine("Utilisation des services réels (API)");
    // Utiliser les services réels qui communiquent avec l'API
    builder.Services.AddScoped<IActifDonneesService>(sp =>
    new ActifDonneesService(
        sp.GetRequiredService<HttpClient>(),
        sp.GetRequiredService<ICacheService>(),
        sp.GetService<IJSRuntime>()
    ));
    builder.Services.AddScoped<IRegleQualiteService, RegleQualiteService>();
    builder.Services.AddScoped<IResultatRegleQualiteService, ResultatRegleQualiteService>();
    builder.Services.AddScoped<ITermeGlossaireService, TermeGlossaireService>();
    builder.Services.AddScoped<IStatistiquesService, StatistiquesService>();
    builder.Services.AddScoped<ITypeMetadonneeService, TypeMetadonneeService>();
    builder.Services.AddScoped<ICategorieMetadonneeService, CategorieMetadonneeService>();
    builder.Services.AddScoped<IMetadonneeService, MetadonneeService>();
    builder.Services.AddScoped<IValidationMetadonneeService, ValidationMetadonneeService>();
    builder.Services.AddScoped<IExceptionHandlingService, DataHubGatineau.Web.Services.Implementations.ExceptionHandlingService>();
    builder.Services.AddScoped<IApiErrorHandlingService, ApiErrorHandlingService>();
    builder.Services.AddScoped<IWorkflowService, WorkflowService>();
    builder.Services.AddScoped<IMetadonneeAvanceeService, MetadonneeAvanceeService>();
    builder.Services.AddScoped<ISchemaMetadonneesService, SchemaMetadonneesService>();
    builder.Services.AddScoped<ILineageService, LineageService>();
    builder.Services.AddScoped<IAnomalieService, AnomalieService>();
    builder.Services.AddScoped<IScoreQualiteService, ScoreQualiteService>();
    builder.Services.AddScoped<IProfileDonneesService, ProfileDonneesService>();
    builder.Services.AddScoped<IPolitiqueService, PolitiqueService>();
    builder.Services.AddScoped<IConformitePolitiqueService, ConformitePolitiqueService>();
    builder.Services.AddScoped<IExportService, ExportService>();
    builder.Services.AddScoped<INotificationService, NotificationService>();
    builder.Services.AddScoped<ISearchService, SearchService>();
    builder.Services.AddScoped<IDomaineGouvernanceService, DataHubGatineau.Web.Services.Implementations.DomaineGouvernanceService>();
    builder.Services.AddScoped<IDomaineAffairesService, DataHubGatineau.Web.Services.Implementations.DomaineAffairesService>();
    builder.Services.AddScoped<IProduitDonneesService, DataHubGatineau.Web.Services.Implementations.ProduitDonneesService>();
    builder.Services.AddScoped<IConnexionSourceDonneesService, DataHubGatineau.Web.Services.Implementations.ConnexionSourceDonneesService>();
    builder.Services.AddScoped<IConfigurationScanService, DataHubGatineau.Web.Services.Implementations.ConfigurationScanService>();
    builder.Services.AddScoped<IHistoriqueScanService, HistoriqueScanService>();
    builder.Services.AddScoped<IWorkflowApprobationService, DataHubGatineau.Web.Services.Implementations.WorkflowApprobationService>();
    builder.Services.AddScoped<IPreloadService, PreloadService>();
    builder.Services.AddScoped<IConfirmationService, ConfirmationService>();
    builder.Services.AddScoped<IServiceGestionUtilisateurs, ServiceGestionUtilisateurs>();

    // Services pour les tables de référence
    builder.Services.AddScoped<ITypeActifDonneesService, TypeActifDonneesService>();
    builder.Services.AddScoped<ICategorieTypeActifDonneesService, CategorieTypeActifDonneesService>();
    builder.Services.AddScoped<IFormatActifDonneesService, FormatActifDonneesService>();
    builder.Services.AddScoped<ISourceActifDonneesService, SourceActifDonneesService>();
    builder.Services.AddScoped<IFrequenceMiseAJourService, FrequenceMiseAJourService>();
    builder.Services.AddScoped<IStatutActifDonneesService, StatutActifDonneesService>();

    // Services pour le Scanner
    builder.Services.AddScoped<IConnexionSourceDonneesService, ConnexionSourceDonneesService>();
    builder.Services.AddScoped<IConfigurationScanService, ConfigurationScanService>();
    builder.Services.AddScoped<IHistoriqueScanService, HistoriqueScanService>();
}
else
{
    Console.WriteLine("Utilisation des services simulés (Mock)");
    // Utiliser les services simulés pour le développement
    builder.Services.AddScoped<IActifDonneesService, MockActifDonneesService>();
    builder.Services.AddScoped<IResultatRegleQualiteService, ResultatRegleQualiteService>();
    builder.Services.AddScoped<IRegleQualiteService, RegleQualiteService>();
    builder.Services.AddScoped<ITermeGlossaireService, TermeGlossaireService>();
    builder.Services.AddScoped<IStatistiquesService, StatistiquesService>();
    builder.Services.AddScoped<ITypeMetadonneeService, TypeMetadonneeService>();
    builder.Services.AddScoped<ICategorieMetadonneeService, CategorieMetadonneeService>();
    builder.Services.AddScoped<IMetadonneeService, MetadonneeService>();
    builder.Services.AddScoped<IExceptionHandlingService, DataHubGatineau.Web.Services.Implementations.ExceptionHandlingService>();
    builder.Services.AddScoped<IWorkflowService, WorkflowService>();
    builder.Services.AddScoped<IMetadonneeAvanceeService, MetadonneeAvanceeService>();
    builder.Services.AddScoped<ISchemaMetadonneesService, SchemaMetadonneesService>();
    builder.Services.AddScoped<ILineageService, LineageService>();
    builder.Services.AddScoped<IAnomalieService, AnomalieService>();
    builder.Services.AddScoped<IScoreQualiteService, ScoreQualiteService>();
    builder.Services.AddScoped<IProfileDonneesService, ProfileDonneesService>();
    builder.Services.AddScoped<IPolitiqueService, PolitiqueService>();
    builder.Services.AddScoped<IConformitePolitiqueService, ConformitePolitiqueService>();
    builder.Services.AddScoped<IExportService, ExportService>();
    builder.Services.AddScoped<INotificationService, NotificationService>();
    builder.Services.AddScoped<ISearchService, SearchService>();
    builder.Services.AddScoped<IConfirmationService, ConfirmationService>();
    builder.Services.AddScoped<IDomaineAffairesService, DataHubGatineau.Web.Services.Implementations.DomaineAffairesService>();
    builder.Services.AddScoped<IHistoriqueScanService, MockHistoriqueScanService>();

    // Services pour les tables de référence
    builder.Services.AddScoped<ITypeActifDonneesService, MockTypeActifDonneesService>();
    builder.Services.AddScoped<ICategorieTypeActifDonneesService, MockCategorieTypeActifDonneesService>();
    builder.Services.AddScoped<IFormatActifDonneesService, MockFormatActifDonneesService>();
    builder.Services.AddScoped<ISourceActifDonneesService, MockSourceActifDonneesService>();
    builder.Services.AddScoped<IFrequenceMiseAJourService, MockFrequenceMiseAJourService>();
    builder.Services.AddScoped<IStatutActifDonneesService, MockStatutActifDonneesService>();

    // Services Mock pour le Scanner
    builder.Services.AddScoped<IConnexionSourceDonneesService, MockConnexionSourceDonneesService>();
    builder.Services.AddScoped<IConfigurationScanService, MockConfigurationScanService>();
    builder.Services.AddScoped<IHistoriqueScanService, MockHistoriqueScanService>();
}

// Ajouter le service de cache pour améliorer les performances et réduire les appels API
builder.Services.AddMemoryCache();
builder.Services.AddSingleton<ICacheService, MemoryCacheService>();

// Ajouter le service de notifications toast
builder.Services.AddSingleton<IToastService, DataHubGatineau.Web.Services.ToastService>();

// Ajouter le service d'arrière-plan pour les notifications
builder.Services.AddHostedService<NotificationBackgroundService>();

// Ajouter le service d'initialisation des données de référence
builder.Services.AddScoped<IInitialisationDonneesService, InitialisationDonneesService>();

// Ajouter le service d'audit pour la gouvernance et la conformité
builder.Services.AddScoped<IServiceAudit, DataHubGatineau.Web.Services.Implementations.ServiceAudit>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();
app.UseAntiforgery();

// Configurar endpoints para Blazor Server
app.MapBlazorHub();
app.MapControllers();

// Configurar rutas específicas para l'authentification hybride
app.MapControllerRoute(
    name: "authentification-hybride",
    pattern: "AuthentificationHybride/{action=Connexion}/{id?}",
    defaults: new { controller = "AuthentificationHybride" });

// Route par défaut pour les autres contrôleurs
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapFallbackToPage("/_Host");

// Eliminamos el mapeo redundante que podría causar conflictos

// No necesitamos inicializar el servicio de autenticación aquí
// ya que esto causa problemas durante la prerenderización
app.Use(async (context, next) =>
{
    await next.Invoke();
});

app.Run();
