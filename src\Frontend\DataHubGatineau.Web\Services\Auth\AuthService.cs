using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using Blazored.LocalStorage;
using DataHubGatineau.Web.Models.Auth;
using Microsoft.AspNetCore.Components.Authorization;

namespace DataHubGatineau.Web.Services.Auth;

/// <summary>
/// Service d'authentification.
/// </summary>
public class AuthService : IAuthService
{
    private readonly HttpClient _httpClient;
    private readonly AuthenticationStateProvider _authStateProvider;
    private readonly ILocalStorageService _localStorage;
    private readonly ILogger<AuthService> _logger;
    private UserInfo? _currentUser;

    private const string TokenKey = "authToken";
    private const string RefreshTokenKey = "refreshToken";
    private const string ExpirationKey = "tokenExpiration";

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="AuthService"/>.
    /// </summary>
    /// <param name="httpClient">Le client HTTP.</param>
    /// <param name="authStateProvider">Le fournisseur d'état d'authentification.</param>
    /// <param name="localStorage">Le service de stockage local.</param>
    /// <param name="logger">Le logger.</param>
    public AuthService(
        HttpClient httpClient,
        AuthenticationStateProvider authStateProvider,
        ILocalStorageService localStorage,
        ILogger<AuthService> logger)
    {
        _httpClient = httpClient;
        _authStateProvider = authStateProvider;
        _localStorage = localStorage;
        _logger = logger;
    }

    /// <inheritdoc/>
    public UserInfo? CurrentUser => _currentUser;

    /// <inheritdoc/>
    public bool IsAuthenticated => _currentUser != null;

    /// <inheritdoc/>
    public bool IsAdmin => (_currentUser?.Roles.Contains("Administrator") ?? false) ||
                          (_currentUser?.Roles.Contains("SuperAdministrateur") ?? false);

    /// <inheritdoc/>
    public async Task<LoginResponse> LoginAsync(LoginRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("api/auth/login", request);
            var result = await response.Content.ReadFromJsonAsync<LoginResponse>();

            if (response.IsSuccessStatusCode && result?.Success == true)
            {
                await SaveTokensAsync(result);
                await ((CustomAuthStateProvider)_authStateProvider).NotifyAuthenticationStateChangedAsync();
                await GetCurrentUserAsync();
                return result;
            }

            return result ?? new LoginResponse { Success = false, Message = "Une erreur est survenue lors de la connexion" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la connexion");
            return new LoginResponse { Success = false, Message = "Une erreur est survenue lors de la connexion" };
        }
    }

    /// <inheritdoc/>
    public async Task LogoutAsync()
    {
        try
        {
            // Appeler l'API pour révoquer les jetons
            await _httpClient.PostAsync("api/auth/logout", null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la déconnexion");
        }
        finally
        {
            // Supprimer les jetons du stockage local
            await _localStorage.RemoveItemAsync(TokenKey);
            await _localStorage.RemoveItemAsync(RefreshTokenKey);
            await _localStorage.RemoveItemAsync(ExpirationKey);

            // Réinitialiser l'état d'authentification
            _currentUser = null;
            await ((CustomAuthStateProvider)_authStateProvider).NotifyAuthenticationStateChangedAsync();

            // Réinitialiser l'en-tête d'autorisation
            _httpClient.DefaultRequestHeaders.Authorization = null;
        }
    }

    /// <inheritdoc/>
    public async Task<LoginResponse> RegisterAsync(RegisterRequest request)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync("api/auth/register", request);
            var result = await response.Content.ReadFromJsonAsync<LoginResponse>();

            if (response.IsSuccessStatusCode && result?.Success == true)
            {
                await SaveTokensAsync(result);
                await ((CustomAuthStateProvider)_authStateProvider).NotifyAuthenticationStateChangedAsync();
                await GetCurrentUserAsync();
                return result;
            }

            return result ?? new LoginResponse { Success = false, Message = "Une erreur est survenue lors de l'inscription" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'inscription");
            return new LoginResponse { Success = false, Message = "Une erreur est survenue lors de l'inscription" };
        }
    }

    /// <inheritdoc/>
    public async Task<bool> RefreshTokenAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>(TokenKey);
            var refreshToken = await _localStorage.GetItemAsync<string>(RefreshTokenKey);

            if (string.IsNullOrEmpty(token) || string.IsNullOrEmpty(refreshToken))
            {
                return false;
            }

            var request = new RefreshTokenRequest
            {
                Token = token,
                RefreshToken = refreshToken
            };

            var response = await _httpClient.PostAsJsonAsync("api/auth/refresh-token", request);
            var result = await response.Content.ReadFromJsonAsync<LoginResponse>();

            if (response.IsSuccessStatusCode && result?.Success == true)
            {
                await SaveTokensAsync(result);
                await ((CustomAuthStateProvider)_authStateProvider).NotifyAuthenticationStateChangedAsync();
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du rafraîchissement du token");
            return false;
        }
    }

    /// <inheritdoc/>
    public async Task<UserInfo?> GetCurrentUserAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>(TokenKey);
            if (string.IsNullOrEmpty(token))
            {
                _currentUser = null;
                return null;
            }

            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            var response = await _httpClient.GetAsync("api/auth/me");

            if (response.IsSuccessStatusCode)
            {
                _currentUser = await response.Content.ReadFromJsonAsync<UserInfo>();
                return _currentUser;
            }

            // Si la réponse est 401 ou 403, essayer de rafraîchir le token
            if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized ||
                response.StatusCode == System.Net.HttpStatusCode.Forbidden)
            {
                var refreshed = await RefreshTokenAsync();
                if (refreshed)
                {
                    // Réessayer avec le nouveau token
                    token = await _localStorage.GetItemAsync<string>(TokenKey);
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    response = await _httpClient.GetAsync("api/auth/me");

                    if (response.IsSuccessStatusCode)
                    {
                        _currentUser = await response.Content.ReadFromJsonAsync<UserInfo>();
                        return _currentUser;
                    }
                }

                // Si le rafraîchissement a échoué ou la deuxième tentative a échoué, déconnecter l'utilisateur
                await LogoutAsync();
            }

            _currentUser = null;
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'utilisateur actuel");
            _currentUser = null;
            return null;
        }
    }

    /// <inheritdoc/>
    public bool HasRole(string role)
    {
        return _currentUser?.Roles?.Contains(role) ?? false;
    }



    /// <inheritdoc/>
    public async Task InitializeAsync()
    {
        try
        {
            // Evitamos operaciones de JS durante la prerenderización
            // Solo configuramos el encabezado de autorización si hay un token
            var token = await _localStorage.GetItemAsync<string>(TokenKey);
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

                // Intentamos obtener el usuario actual solo si hay un token
                await GetCurrentUserAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'initialisation du service d'authentification");
            // No llamamos a LogoutAsync aquí para evitar operaciones de JS durante la prerenderización
            _currentUser = null;
            _httpClient.DefaultRequestHeaders.Authorization = null;
        }
    }

    private async Task SaveTokensAsync(LoginResponse response)
    {
        if (response.Token != null)
        {
            await _localStorage.SetItemAsync(TokenKey, response.Token);
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", response.Token);
        }

        if (response.RefreshToken != null)
        {
            await _localStorage.SetItemAsync(RefreshTokenKey, response.RefreshToken);
        }

        if (response.Expiration.HasValue)
        {
            await _localStorage.SetItemAsync(ExpirationKey, response.Expiration.Value);
        }
    }
}
