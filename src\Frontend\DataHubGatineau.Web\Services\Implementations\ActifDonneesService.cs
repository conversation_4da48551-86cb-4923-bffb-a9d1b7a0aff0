using DataHubGatineau.Web.Models;
using DataHubGatineau.Web.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using Microsoft.JSInterop;
using System.Net.Http.Json;
using System.Text.Json;
using System.Text;

namespace DataHubGatineau.Web.Services.Implementations;

/// <summary>
/// Service pour les opérations sur les actifs de données.
/// </summary>
public class ActifDonneesService : ServiceBaseGuid<ActifDonnees>, IActifDonneesService
{
    // Nous n'utilisons plus de données simulées, tout est récupéré depuis l'API
    private readonly IJSRuntime? _jsRuntime;
    private readonly ICacheService _cacheService;
    private const string LocalStorageKey = "actifs_donnees_local";
    private const string SyncQueueKey = "actifs_donnees_sync_queue";

    // Clés de cache
    private const string CACHE_KEY_ALL_ACTIFS = "actifs_donnees_all";
    private const string CACHE_KEY_ACTIF_PREFIX = "actif_donnees_";
    private const string CACHE_KEY_TYPES = "types_actif_donnees";
    private const string CACHE_KEY_FORMATS = "formats_actif_donnees";
    private const string CACHE_KEY_SOURCES = "sources_actif_donnees";
    private const string CACHE_KEY_FREQUENCES = "frequences_mise_a_jour";
    private const string CACHE_KEY_STATUTS = "statuts_actif_donnees";
    private const string CACHE_KEY_TYPES_METADONNEES = "types_metadonnees";
    private const string CACHE_KEY_CATEGORIES_METADONNEES = "categories_metadonnees";
    private const string CACHE_GROUP_REFERENCE_DATA = "reference_data";
    private const string CACHE_GROUP_ACTIFS = "actifs_donnees";

    // Durées d'expiration du cache
    private static readonly TimeSpan CACHE_EXPIRATION_REFERENCE = TimeSpan.FromMinutes(30); // Les données de référence changent rarement
    private static readonly TimeSpan CACHE_EXPIRATION_ACTIFS = TimeSpan.FromMinutes(5);     // Les actifs peuvent changer plus souvent

    // Indique si le composant a été rendu et si les appels JavaScript sont sécuritaires
    private bool _isRendered = false;
    private bool _debugMode = true;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ActifDonneesService"/>.
    /// </summary>
    /// <param name="httpClient">Client HTTP.</param>
    /// <param name="cacheService">Service de cache.</param>
    /// <param name="jsRuntime">Runtime JavaScript pour l'accès au localStorage.</param>
    public ActifDonneesService(HttpClient httpClient, ICacheService cacheService, IJSRuntime? jsRuntime = null)
        : base(httpClient, "api/v2/ActifDonnees") // Utiliser la version 2 de l'API (plus robuste)
    {
        _jsRuntime = jsRuntime;
        _cacheService = cacheService;
    }

    /// <inheritdoc/>
    public override async Task<IEnumerable<ActifDonnees>> ObtenirTousAsync()
    {
        return await _cacheService.GetOrCreateAsync<IEnumerable<ActifDonnees>>(
            CACHE_KEY_ALL_ACTIFS,
            async () =>
            {
                try
                {
                    if (_debugMode) Console.WriteLine("Récupération des actifs de données depuis l'API (cache expiré ou non existant)");

                    // Appelle l'API avec des paramètres pour optimiser la requête
                    var url = $"{_baseUrl}"; // Simplifier temporairement

                    // Utiliser HttpClient.GetAsync pour avoir plus de contrôle sur la désérialisation
                    var httpResponse = await _httpClient.GetAsync(url);
                    httpResponse.EnsureSuccessStatusCode();

                    var jsonContent = await httpResponse.Content.ReadAsStringAsync();
                    if (_debugMode) Console.WriteLine($"Réponse JSON reçue: {jsonContent.Substring(0, Math.Min(500, jsonContent.Length))}...");

                    // Essayer de désérialiser comme ActifDonnees directement
                    var response = JsonSerializer.Deserialize<IEnumerable<ActifDonnees>>(jsonContent, _jsonOptions);

                    // Fusionner avec les actifs locaux
                    var actifs = response?.ToList() ?? new List<ActifDonnees>();

                    // Récupérer les actifs locaux si le JSRuntime est disponible
                    if (_jsRuntime != null)
                    {
                        var actifsLocaux = await ObtenirActifsLocalStorageAsync();

                        // Ajouter les actifs locaux qui ne sont pas déjà dans la liste
                        foreach (var actifLocal in actifsLocaux)
                        {
                            if (!actifs.Any(a => a.Id == actifLocal.Id))
                            {
                                actifs.Add(actifLocal);
                            }
                        }

                        // Afficher un message si des actifs locaux ont été ajoutés
                        if (actifsLocaux.Any() && _debugMode)
                        {
                            Console.WriteLine($"{actifsLocaux.Count} actifs de données locaux récupérés");
                        }
                    }

                    // Mettre en cache chaque actif individuellement pour les requêtes par ID
                    foreach (var actif in actifs)
                    {
                        var actifKey = $"{CACHE_KEY_ACTIF_PREFIX}{actif.Id}";
                        _cacheService.SetValue(actifKey, actif, CACHE_EXPIRATION_ACTIFS);
                        _cacheService.AddToGroup(CACHE_GROUP_ACTIFS, actifKey);

                        // Ajouter une relation entre la liste complète et chaque actif
                        _cacheService.AddRelation(CACHE_KEY_ALL_ACTIFS, actifKey);
                    }

                    if (_debugMode) Console.WriteLine($"{actifs.Count} actifs de données mis en cache");
                    return actifs;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'obtention des actifs de données: {ex.Message}");

                    // En cas d'erreur, essayer de récupérer les actifs locaux
                    if (_jsRuntime != null)
                    {
                        try
                        {
                            var actifsLocaux = await ObtenirActifsLocalStorageAsync();
                            if (actifsLocaux.Any())
                            {
                                Console.WriteLine($"Utilisation des actifs locaux: {actifsLocaux.Count} actifs trouvés");
                                return actifsLocaux;
                            }
                        }
                        catch (Exception localEx)
                        {
                            Console.Error.WriteLine($"Erreur lors de la récupération des actifs locaux: {localEx.Message}");
                        }
                    }

                    // Si tout échoue, retourner une liste vide
                    return Enumerable.Empty<ActifDonnees>();
                }
            },
            CACHE_EXPIRATION_ACTIFS
        );
    }

    /// <summary>
    /// Charge les données de référence pour une liste d'actifs.
    /// </summary>
    /// <param name="actifs">La liste d'actifs à compléter.</param>
    private async Task ChargerDonneesReferenceAsync(List<ActifDonnees> actifs)
    {
        try
        {
            // Charger toutes les données de référence une seule fois
            var types = await ObtenirTypesAsync();
            var formats = await ObtenirFormatsAsync();
            var sources = await ObtenirSourcesAsync();
            var frequences = await ObtenirFrequencesAsync();
            var statuts = await ObtenirStatutsAsync();

            // Afficher les données de référence disponibles pour le débogage
            Console.WriteLine($"Types disponibles: {types.Count()}");
            Console.WriteLine($"Sources disponibles: {sources.Count()}");

            // Afficher les types et sources disponibles pour le débogage
            Console.WriteLine("Types disponibles:");
            foreach (var type in types)
            {
                Console.WriteLine($"  ID: {type.Id}, Nom: {type.Nom}");
            }

            Console.WriteLine("Sources disponibles:");
            foreach (var source in sources)
            {
                Console.WriteLine($"  ID: {source.Id}, Nom: {source.Nom}");
            }

            foreach (var actif in actifs)
            {
                Console.WriteLine($"Traitement de l'actif {actif.Id} - {actif.Nom}");
                Console.WriteLine($"  TypeActifDonneesId: {actif.TypeActifDonneesId}");
                Console.WriteLine($"  Type: {actif.Type}");
                Console.WriteLine($"  SourceActifDonneesId: {actif.SourceActifDonneesId}");
                Console.WriteLine($"  Source: {actif.Source}");

                // Si Type est défini mais pas TypeActifDonnees, chercher par nom
                if (!string.IsNullOrEmpty(actif.Type) && (actif.TypeActifDonnees == null || string.IsNullOrEmpty(actif.TypeActifDonnees.Nom)))
                {
                    var type = types.FirstOrDefault(t => t.Nom.Equals(actif.Type, StringComparison.OrdinalIgnoreCase));
                    if (type != null)
                    {
                        actif.TypeActifDonnees = type;
                        actif.TypeActifDonneesId = type.Id;
                        Console.WriteLine($"Type trouvé pour {actif.Nom}: {type.Nom}");
                    }
                }
                // Si TypeActifDonneesId est défini mais pas TypeActifDonnees, chercher par ID
                else if (actif.TypeActifDonneesId.HasValue && actif.TypeActifDonneesId != Guid.Empty &&
                        (actif.TypeActifDonnees == null || string.IsNullOrEmpty(actif.TypeActifDonnees.Nom)))
                {
                    var type = types.FirstOrDefault(t => t.Id == actif.TypeActifDonneesId);
                    if (type != null)
                    {
                        actif.TypeActifDonnees = type;
                        actif.Type = type.Nom;
                        Console.WriteLine($"Type trouvé par ID pour {actif.Nom}: {type.Nom}");
                    }
                }
                // No asignamos un tipo por defecto para todos los activos
                // Solo mostramos un mensaje de advertencia si no hay tipo
                else if (actif.TypeActifDonnees == null && !actif.TypeActifDonneesId.HasValue && string.IsNullOrEmpty(actif.Type))
                {
                    Console.WriteLine($"ADVERTENCIA: El activo {actif.Nom} no tiene tipo asignado");
                }

                // Si Source est défini mais pas SourceActifDonnees, chercher par nom
                if (!string.IsNullOrEmpty(actif.Source) && (actif.SourceActifDonnees == null || string.IsNullOrEmpty(actif.SourceActifDonnees.Nom)))
                {
                    var source = sources.FirstOrDefault(s => s.Nom.Equals(actif.Source, StringComparison.OrdinalIgnoreCase));
                    if (source != null)
                    {
                        actif.SourceActifDonnees = source;
                        actif.SourceActifDonneesId = source.Id;
                        Console.WriteLine($"Source trouvée pour {actif.Nom}: {source.Nom}");
                    }
                }
                // Si SourceActifDonneesId est défini mais pas SourceActifDonnees, chercher par ID
                else if (actif.SourceActifDonneesId.HasValue && actif.SourceActifDonneesId != Guid.Empty &&
                        (actif.SourceActifDonnees == null || string.IsNullOrEmpty(actif.SourceActifDonnees.Nom)))
                {
                    var source = sources.FirstOrDefault(s => s.Id == actif.SourceActifDonneesId);
                    if (source != null)
                    {
                        actif.SourceActifDonnees = source;
                        actif.Source = source.Nom;
                        Console.WriteLine($"Source trouvée par ID pour {actif.Nom}: {source.Nom}");
                    }
                }
                // No asignamos una fuente por defecto para todos los activos
                // Solo mostramos un mensaje de advertencia si no hay fuente
                else if (actif.SourceActifDonnees == null && !actif.SourceActifDonneesId.HasValue && string.IsNullOrEmpty(actif.Source))
                {
                    Console.WriteLine($"ADVERTENCIA: El activo {actif.Nom} no tiene fuente asignada");
                }

                // Traitement similaire pour Format
                if (!string.IsNullOrEmpty(actif.Format) && (actif.FormatActifDonnees == null || string.IsNullOrEmpty(actif.FormatActifDonnees.Nom)))
                {
                    var format = formats.FirstOrDefault(f => f.Nom.Equals(actif.Format, StringComparison.OrdinalIgnoreCase));
                    if (format != null)
                    {
                        actif.FormatActifDonnees = format;
                        actif.FormatActifDonneesId = format.Id;
                        Console.WriteLine($"Format trouvé pour {actif.Nom}: {format.Nom}");
                    }
                }
                else if (actif.FormatActifDonneesId.HasValue && actif.FormatActifDonneesId != Guid.Empty &&
                        (actif.FormatActifDonnees == null || string.IsNullOrEmpty(actif.FormatActifDonnees.Nom)))
                {
                    var format = formats.FirstOrDefault(f => f.Id == actif.FormatActifDonneesId);
                    if (format != null)
                    {
                        actif.FormatActifDonnees = format;
                        actif.Format = format.Nom;
                        Console.WriteLine($"Format trouvé par ID pour {actif.Nom}: {format.Nom}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement des données de référence: {ex.Message}");
            Console.Error.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    /// <inheritdoc/>
    public override async Task<ActifDonnees?> ObtenirParIdAsync(Guid id)
    {
        // Vérifier si l'actif est déjà en cache
        var cacheKey = $"{CACHE_KEY_ACTIF_PREFIX}{id}";

        return await _cacheService.GetOrCreateAsync<ActifDonnees?>(
            cacheKey,
            async () =>
            {
                try
                {
                    if (_debugMode) Console.WriteLine($"Récupération de l'actif de données {id} depuis l'API (cache expiré ou non existant)");

                    // Appelle l'API avec des paramètres pour optimiser la requête et inclure les produits et métadonnées
                    var url = $"{_baseUrl}/{id}?includeType=true&includeFormat=true&includeSource=true&includeFrequence=true&includeStatut=true&includeMetadonnees=true&includeProduitsDonnees=true";

                    if (_debugMode) Console.WriteLine($"URL de la requête: {url}");

                    var response = await _httpClient.GetAsync(url);

                    if (response.IsSuccessStatusCode)
                    {
                        var jsonContent = await response.Content.ReadAsStringAsync();

                        if (_debugMode) Console.WriteLine($"Réponse reçue pour l'actif {id}");

                        var actif = JsonSerializer.Deserialize<ActifDonnees>(jsonContent, _jsonOptions);

                        if (actif != null)
                        {
                            // Debug: Vérifier les propriétés de type
                            if (_debugMode)
                            {
                                Console.WriteLine($"🔍 ActifDonneesService - Actif désérialisé:");
                                Console.WriteLine($"  - ID: {actif.Id}");
                                Console.WriteLine($"  - Nom: {actif.Nom}");
                                Console.WriteLine($"  - Type: {actif.Type}");
                                Console.WriteLine($"  - TypeActifDonneesId: {actif.TypeActifDonneesId}");
                                Console.WriteLine($"  - TypeActifDonnees: {actif.TypeActifDonnees?.Nom}");
                            }

                            // Ajouter l'actif au groupe de cache
                            _cacheService.AddToGroup(CACHE_GROUP_ACTIFS, cacheKey);

                            if (_debugMode) Console.WriteLine($"Actif {id} mis en cache");
                            return actif;
                        }
                    }
                    else
                    {
                        Console.Error.WriteLine($"Erreur HTTP {response.StatusCode} lors de la récupération de l'actif {id}");
                    }

                    // Si l'actif n'est pas trouvé dans l'API, essayer de le récupérer depuis la liste complète
                    if (_cacheService.TryGetValue<IEnumerable<ActifDonnees>>(CACHE_KEY_ALL_ACTIFS, out var allActifs))
                    {
                        var actifFromList = allActifs?.FirstOrDefault(a => a.Id == id);
                        if (actifFromList != null)
                        {
                            if (_debugMode) Console.WriteLine($"Actif {id} trouvé dans la liste complète en cache");
                            return actifFromList;
                        }
                    }

                    // Si toujours pas trouvé, essayer de récupérer tous les actifs (cela mettra à jour le cache)
                    var actifs = await ObtenirTousAsync();
                    var actif2 = actifs.FirstOrDefault(a => a.Id == id);

                    if (actif2 != null)
                    {
                        if (_debugMode) Console.WriteLine($"Actif {id} trouvé après rafraîchissement de la liste complète");
                        return actif2;
                    }

                    // Méthode de secours: créer un actif factice
                    Console.WriteLine($"Création d'un actif factice pour l'ID {id}");
                    return new ActifDonnees
                    {
                        Id = id,
                        Nom = "Actif temporaire",
                        Description = "Cet actif est temporaire en raison d'une erreur de chargement",
                        ClassificationSensibilite = ClassificationSensibilite.Public,
                        DateCreation = DateTime.Now,
                        DateModification = DateTime.Now,
                        EstElementCritique = false
                    };
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'obtention de l'actif de données {id}: {ex.Message}");

                    // Méthode de secours: créer un actif factice
                    return new ActifDonnees
                    {
                        Id = id,
                        Nom = "Actif temporaire (erreur)",
                        Description = $"Cet actif est temporaire en raison d'une erreur: {ex.Message}",
                        ClassificationSensibilite = ClassificationSensibilite.Public,
                        DateCreation = DateTime.Now,
                        DateModification = DateTime.Now,
                        EstElementCritique = false
                    };
                }
            },
            CACHE_EXPIRATION_ACTIFS
        );
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParTypeAsync(string type)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ActifDonnees>>($"{_baseUrl}/parType/{type}", _jsonOptions);
            return response ?? Enumerable.Empty<ActifDonnees>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des actifs de données par type {type}: {ex.Message}");
            return Enumerable.Empty<ActifDonnees>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> ObtenirParClassificationAsync(ClassificationSensibilite classification)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ActifDonnees>>($"{_baseUrl}/parClassification/{classification}", _jsonOptions);
            return response ?? Enumerable.Empty<ActifDonnees>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des actifs de données par classification {classification}: {ex.Message}");
            return Enumerable.Empty<ActifDonnees>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ActifDonnees>> RechercherParNomAsync(string terme)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ActifDonnees>>($"{_baseUrl}/rechercher?terme={Uri.EscapeDataString(terme)}", _jsonOptions);
            return response ?? Enumerable.Empty<ActifDonnees>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la recherche des actifs de données par nom {terme}: {ex.Message}");
            return Enumerable.Empty<ActifDonnees>();
        }
    }

    /// <inheritdoc/>
    public override async Task<ActifDonnees> AjouterAsync(ActifDonnees entite)
    {
        try
        {
            // Journaliser l'entité avant l'envoi
            Console.WriteLine($"Tentative d'ajout d'un actif de données: {JsonSerializer.Serialize(entite, _jsonOptions)}");

            // Vérifier et compléter les IDs de référence
            await CompleterReferencesAsync(entite);

            // Créer un DTO pour l'API v2 (ActifDonneesCreationDTO)
            var actifCreationDTO = new
            {
                Nom = entite.Nom,
                Description = entite.Description,
                TypeActifDonneesId = entite.TypeActifDonneesId,
                FormatActifDonneesId = entite.FormatActifDonneesId,
                SourceActifDonneesId = entite.SourceActifDonneesId,
                Proprietaire = entite.Proprietaire,
                ClassificationSensibilite = (int)entite.ClassificationSensibilite,
                DateDerniereMiseAJour = entite.DateDerniereMiseAJour,
                FrequenceMiseAJourId = entite.FrequenceMiseAJourId,
                CheminAcces = entite.CheminAcces,
                DomaineGouvernanceId = entite.DomaineGouvernanceId,
                EstElementCritique = entite.EstElementCritique,
                ConnexionSourceDonneesId = entite.ConnexionSourceDonneesId,
                StatutActifDonneesId = entite.StatutActifDonneesId
            };

            // Sérialiser le DTO
            var jsonContent = JsonSerializer.Serialize(actifCreationDTO, _jsonOptions);
            Console.WriteLine($"Envoi du DTO à l'API: {jsonContent}");

            // Utiliser l'API v2 (endpoint correct)
            var apiV2Url = "api/v2/ActifDonnees";
            Console.WriteLine($"Envoi de la requête à l'URL: {apiV2Url}");

            try
            {
                // Envoyer la requête POST
                var response = await _httpClient.PostAsJsonAsync(apiV2Url, actifCreationDTO, _jsonOptions);
                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Réponse du serveur: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    // Désérialiser la réponse
                    var result = JsonSerializer.Deserialize<ActifDonnees>(responseContent, _jsonOptions);
                    Console.WriteLine($"Actif de données ajouté avec succès: {JsonSerializer.Serialize(result, _jsonOptions)}");

                    // Invalider le cache des actifs de données
                    _cacheService.Remove(CACHE_KEY_ALL_ACTIFS);
                    _cacheService.InvalidateGroup(CACHE_GROUP_ACTIFS);
                    Console.WriteLine("Cache des actifs de données invalidé après ajout");

                    return result ?? throw new InvalidOperationException("La réponse ne contient pas d'entité.");
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    // En cas d'erreur 400, utiliser le mode de secours
                    Console.Error.WriteLine($"Erreur 400 Bad Request: {responseContent}");
                    return await UtiliserModeSecoursAsync(entite);
                }
                else
                {
                    // Autres erreurs HTTP
                    Console.Error.WriteLine($"Erreur HTTP: {response.StatusCode}");
                    Console.Error.WriteLine($"Détails de l'erreur: {responseContent}");
                    throw new HttpRequestException($"Erreur HTTP {response.StatusCode}: {responseContent}");
                }
            }
            catch (Exception apiEx)
            {
                Console.Error.WriteLine($"Exception lors de l'appel à l'API: {apiEx.Message}");
                Console.Error.WriteLine($"Stack trace: {apiEx.StackTrace}");

                // En cas d'erreur, utiliser le mode de secours
                return await UtiliserModeSecoursAsync(entite);
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'ajout de l'actif de données: {ex.Message}");

            if (ex is HttpRequestException httpEx)
            {
                Console.Error.WriteLine($"Détails de l'erreur HTTP: {httpEx.StatusCode}");
            }

            // Mode de secours
            return await UtiliserModeSecoursAsync(entite);
        }
    }

    /// <summary>
    /// Complète les références manquantes dans l'entité.
    /// </summary>
    /// <param name="entite">L'entité à compléter.</param>
    private async Task CompleterReferencesAsync(ActifDonnees entite)
    {
        // Vérifier TypeActifDonneesId
        if (entite.TypeActifDonneesId == null || entite.TypeActifDonneesId == Guid.Empty)
        {
            Console.WriteLine("ATTENTION: TypeActifDonneesId est null ou vide");
            // Chercher le type par nom si disponible
            if (!string.IsNullOrEmpty(entite.TypeActifDonnees?.Nom))
            {
                var types = await ObtenirTypesAsync();
                var type = types.FirstOrDefault(t => t.Nom.Equals(entite.TypeActifDonnees.Nom, StringComparison.OrdinalIgnoreCase));
                if (type != null)
                {
                    entite.TypeActifDonneesId = type.Id;
                    Console.WriteLine($"TypeActifDonneesId défini à partir du nom: {type.Id}");
                }
            }
        }

        // Vérifier FormatActifDonneesId
        if (entite.FormatActifDonneesId == null || entite.FormatActifDonneesId == Guid.Empty)
        {
            Console.WriteLine("ATTENTION: FormatActifDonneesId est null ou vide");
            // Chercher le format par nom si disponible
            if (!string.IsNullOrEmpty(entite.FormatActifDonnees?.Nom))
            {
                var formats = await ObtenirFormatsAsync();
                var format = formats.FirstOrDefault(f => f.Nom.Equals(entite.FormatActifDonnees.Nom, StringComparison.OrdinalIgnoreCase));
                if (format != null)
                {
                    entite.FormatActifDonneesId = format.Id;
                    Console.WriteLine($"FormatActifDonneesId défini à partir du nom: {format.Id}");
                }
            }
        }

        // Vérifier SourceActifDonneesId
        if (entite.SourceActifDonneesId == null || entite.SourceActifDonneesId == Guid.Empty)
        {
            Console.WriteLine("ATTENTION: SourceActifDonneesId est null ou vide");
            // Chercher la source par nom si disponible
            if (!string.IsNullOrEmpty(entite.SourceActifDonnees?.Nom))
            {
                var sources = await ObtenirSourcesAsync();
                var source = sources.FirstOrDefault(s => s.Nom.Equals(entite.SourceActifDonnees.Nom, StringComparison.OrdinalIgnoreCase));
                if (source != null)
                {
                    entite.SourceActifDonneesId = source.Id;
                    Console.WriteLine($"SourceActifDonneesId défini à partir du nom: {source.Id}");
                }
            }
        }

        // Vérifier FrequenceMiseAJourId
        if (entite.FrequenceMiseAJourId == null || entite.FrequenceMiseAJourId == Guid.Empty)
        {
            Console.WriteLine("ATTENTION: FrequenceMiseAJourId est null ou vide");
            // Chercher la fréquence par nom si disponible
            if (!string.IsNullOrEmpty(entite.FrequenceMiseAJour?.Nom))
            {
                var frequences = await ObtenirFrequencesAsync();
                var frequence = frequences.FirstOrDefault(f => f.Nom.Equals(entite.FrequenceMiseAJour.Nom, StringComparison.OrdinalIgnoreCase));
                if (frequence != null)
                {
                    entite.FrequenceMiseAJourId = frequence.Id;
                    Console.WriteLine($"FrequenceMiseAJourId défini à partir du nom: {frequence.Id}");
                }
            }
        }

        // Vérifier StatutActifDonneesId
        if (entite.StatutActifDonneesId == null || entite.StatutActifDonneesId == Guid.Empty)
        {
            Console.WriteLine("ATTENTION: StatutActifDonneesId est null ou vide");
            // Chercher le statut par nom si disponible
            if (!string.IsNullOrEmpty(entite.StatutActifDonnees?.Nom))
            {
                var statuts = await ObtenirStatutsAsync();
                var statut = statuts.FirstOrDefault(s => s.Nom.Equals(entite.StatutActifDonnees.Nom, StringComparison.OrdinalIgnoreCase));
                if (statut != null)
                {
                    entite.StatutActifDonneesId = statut.Id;
                    Console.WriteLine($"StatutActifDonneesId défini à partir du nom: {statut.Id}");
                }
            }
            else
            {
                // Définir le statut par défaut à "Brouillon"
                var statuts = await ObtenirStatutsAsync();
                var brouillon = statuts.FirstOrDefault(s => s.Nom.Equals("Brouillon", StringComparison.OrdinalIgnoreCase));
                if (brouillon != null)
                {
                    entite.StatutActifDonneesId = brouillon.Id;
                    Console.WriteLine($"StatutActifDonneesId défini par défaut à Brouillon: {brouillon.Id}");
                }
            }
        }
    }

    /// <summary>
    /// Utilise le mode de secours pour ajouter un actif de données localement.
    /// </summary>
    /// <param name="entite">L'actif de données à ajouter.</param>
    /// <returns>L'actif de données ajouté.</returns>
    private async Task<ActifDonnees> UtiliserModeSecoursAsync(ActifDonnees entite)
    {
        Console.WriteLine("Utilisation du mode de secours pour l'ajout d'actif de données");

        // Générer un nouvel ID si nécessaire
        if (entite.Id == Guid.Empty)
        {
            entite.Id = Guid.NewGuid();
        }

        // Mettre à jour les dates
        entite.DateCreation = DateTime.Now;
        entite.DateModification = DateTime.Now;

        // Compléter les propriétés de chaîne à partir des IDs
        if (entite.TypeActifDonneesId != null)
        {
            var type = await ObtenirTypeParIdAsync(entite.TypeActifDonneesId.Value);
            if (type != null)
            {
                entite.Type = type.Nom;
            }
        }

        if (entite.FormatActifDonneesId != null)
        {
            var format = await ObtenirFormatParIdAsync(entite.FormatActifDonneesId.Value);
            if (format != null)
            {
                entite.Format = format.Nom;
            }
        }

        if (entite.SourceActifDonneesId != null)
        {
            var source = await ObtenirSourceParIdAsync(entite.SourceActifDonneesId.Value);
            if (source != null)
            {
                entite.Source = source.Nom;
            }
        }

        if (entite.FrequenceMiseAJourId != null)
        {
            var frequence = await ObtenirFrequenceParIdAsync(entite.FrequenceMiseAJourId.Value);
            if (frequence != null)
            {
                entite.FrequenceMiseAJour = new FrequenceMiseAJour { Id = frequence.Id, Nom = frequence.Nom };
            }
        }

        if (entite.StatutActifDonneesId != null)
        {
            var statut = await ObtenirStatutParIdAsync(entite.StatutActifDonneesId.Value);
            if (statut != null)
            {
                entite.Statut = statut.Nom;
            }
        }

        // Sauvegarder dans le localStorage si disponible
        await SauvegarderDansLocalStorageAsync(entite);

        // Ajouter à la file d'attente de synchronisation
        await AjouterALaFileDeSynchronisationAsync(entite);

        return entite;
    }

    /// <summary>
    /// Marque le service comme étant prêt pour les appels JavaScript.
    /// Cette méthode doit être appelée depuis OnAfterRenderAsync.
    /// </summary>
    public void MarquerCommeRendu()
    {
        _isRendered = true;
        Console.WriteLine("Service ActifDonnees marqué comme rendu, les appels JavaScript sont maintenant sécuritaires");
    }

    /// <summary>
    /// Sauvegarde un actif de données dans le localStorage.
    /// </summary>
    /// <param name="entite">L'actif de données à sauvegarder.</param>
    private async Task SauvegarderDansLocalStorageAsync(ActifDonnees entite)
    {
        if (_jsRuntime == null)
        {
            Console.WriteLine("JSRuntime non disponible, impossible de sauvegarder dans le localStorage");
            return;
        }

        // Vérifier si le composant a été rendu
        if (!_isRendered)
        {
            Console.WriteLine("Composant non rendu, sauvegarde dans le localStorage ignorée");
            return;
        }

        try
        {
            // Récupérer les actifs existants
            var actifs = await ObtenirActifsLocalStorageAsync();

            // Ajouter ou mettre à jour l'actif
            var index = actifs.FindIndex(a => a.Id == entite.Id);
            if (index >= 0)
            {
                actifs[index] = entite;
            }
            else
            {
                actifs.Add(entite);
            }

            // Sauvegarder dans le localStorage en utilisant notre fonction JavaScript
            var json = JsonSerializer.Serialize(actifs, _jsonOptions);
            var result = await _jsRuntime.InvokeAsync<bool>("saveToLocalStorage", LocalStorageKey, json);

            if (result)
            {
                Console.WriteLine($"Actif de données sauvegardé dans le localStorage: {entite.Id}");
            }
            else
            {
                Console.Error.WriteLine("Échec de la sauvegarde dans le localStorage");
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la sauvegarde dans le localStorage: {ex.Message}");
        }
    }

    /// <summary>
    /// Récupère les actifs de données du localStorage.
    /// </summary>
    /// <returns>Les actifs de données stockés localement.</returns>
    private async Task<List<ActifDonnees>> ObtenirActifsLocalStorageAsync()
    {
        if (_jsRuntime == null)
        {
            Console.WriteLine("JSRuntime non disponible, impossible de récupérer du localStorage");
            return new List<ActifDonnees>();
        }

        // Vérifier si le composant a été rendu
        if (!_isRendered)
        {
            Console.WriteLine("Composant non rendu, récupération du localStorage ignorée");
            return new List<ActifDonnees>();
        }

        try
        {
            // Vérifier si le localStorage est disponible
            var isAvailable = await _jsRuntime.InvokeAsync<bool>("isLocalStorageAvailable");
            if (!isAvailable)
            {
                Console.Error.WriteLine("localStorage n'est pas disponible dans ce navigateur");
                return new List<ActifDonnees>();
            }

            // Utiliser notre fonction JavaScript pour récupérer les données
            var json = await _jsRuntime.InvokeAsync<string>("getFromLocalStorage", LocalStorageKey);
            if (string.IsNullOrEmpty(json))
            {
                return new List<ActifDonnees>();
            }

            Console.WriteLine($"Données récupérées du localStorage: {json.Substring(0, Math.Min(100, json.Length))}...");

            try
            {
                var actifs = JsonSerializer.Deserialize<List<ActifDonnees>>(json, _jsonOptions);
                return actifs ?? new List<ActifDonnees>();
            }
            catch (JsonException jsonEx)
            {
                Console.Error.WriteLine($"Erreur lors de la désérialisation des données du localStorage: {jsonEx.Message}");
                // En cas d'erreur de désérialisation, supprimer les données corrompues
                await _jsRuntime.InvokeVoidAsync("removeFromLocalStorage", LocalStorageKey);
                return new List<ActifDonnees>();
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la récupération du localStorage: {ex.Message}");
            return new List<ActifDonnees>();
        }
    }

    /// <summary>
    /// Ajoute un actif de données à la file d'attente de synchronisation.
    /// </summary>
    /// <param name="entite">L'actif de données à ajouter à la file d'attente.</param>
    private async Task AjouterALaFileDeSynchronisationAsync(ActifDonnees entite)
    {
        if (_jsRuntime == null)
        {
            Console.WriteLine("JSRuntime non disponible, impossible d'ajouter à la file de synchronisation");
            return;
        }

        // Vérifier si le composant a été rendu
        if (!_isRendered)
        {
            Console.WriteLine("Composant non rendu, ajout à la file de synchronisation ignoré");
            return;
        }

        try
        {
            // Vérifier si le localStorage est disponible
            var isAvailable = await _jsRuntime.InvokeAsync<bool>("isLocalStorageAvailable");
            if (!isAvailable)
            {
                Console.Error.WriteLine("localStorage n'est pas disponible dans ce navigateur");
                return;
            }

            // Récupérer la file d'attente existante
            var queue = await ObtenirFileDeSynchronisationAsync();

            // Créer un objet simple pour la file d'attente
            var queueItem = new
            {
                Id = entite.Id.ToString(),
                DateAjout = DateTime.Now.ToString("o"),
                Nom = entite.Nom
            };

            // Ajouter l'actif à la file d'attente s'il n'y est pas déjà
            if (!queue.Any(a => a.GetProperty("Id").GetString() == entite.Id.ToString()))
            {
                queue.Add(JsonSerializer.SerializeToElement(queueItem, _jsonOptions));

                // Sauvegarder la file d'attente
                var json = JsonSerializer.Serialize(queue, _jsonOptions);
                var result = await _jsRuntime.InvokeAsync<bool>("saveToLocalStorage", SyncQueueKey, json);

                if (result)
                {
                    Console.WriteLine($"Actif de données ajouté à la file de synchronisation: {entite.Id}");
                }
                else
                {
                    Console.Error.WriteLine("Échec de l'ajout à la file de synchronisation");
                }
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'ajout à la file de synchronisation: {ex.Message}");
        }
    }

    /// <summary>
    /// Récupère la file d'attente de synchronisation.
    /// </summary>
    /// <returns>La file d'attente de synchronisation.</returns>
    private async Task<List<JsonElement>> ObtenirFileDeSynchronisationAsync()
    {
        if (_jsRuntime == null)
        {
            Console.WriteLine("JSRuntime non disponible, impossible de récupérer la file de synchronisation");
            return new List<JsonElement>();
        }

        // Vérifier si le composant a été rendu
        if (!_isRendered)
        {
            Console.WriteLine("Composant non rendu, récupération de la file de synchronisation ignorée");
            return new List<JsonElement>();
        }

        try
        {
            // Vérifier si le localStorage est disponible
            var isAvailable = await _jsRuntime.InvokeAsync<bool>("isLocalStorageAvailable");
            if (!isAvailable)
            {
                Console.Error.WriteLine("localStorage n'est pas disponible dans ce navigateur");
                return new List<JsonElement>();
            }

            // Utiliser notre fonction JavaScript pour récupérer les données
            var json = await _jsRuntime.InvokeAsync<string>("getFromLocalStorage", SyncQueueKey);
            if (string.IsNullOrEmpty(json))
            {
                return new List<JsonElement>();
            }

            try
            {
                var queue = JsonSerializer.Deserialize<List<JsonElement>>(json, _jsonOptions);
                return queue ?? new List<JsonElement>();
            }
            catch (JsonException jsonEx)
            {
                Console.Error.WriteLine($"Erreur lors de la désérialisation de la file de synchronisation: {jsonEx.Message}");
                // En cas d'erreur de désérialisation, supprimer les données corrompues
                await _jsRuntime.InvokeVoidAsync("removeFromLocalStorage", SyncQueueKey);
                return new List<JsonElement>();
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la récupération de la file de synchronisation: {ex.Message}");
            return new List<JsonElement>();
        }
    }

    /// <inheritdoc/>
    public override async Task<ActifDonnees> MettreAJourAsync(Guid id, ActifDonnees entite)
    {
        try
        {
            // Journaliser l'entité avant l'envoi
            Console.WriteLine($"Tentative de mise à jour de l'actif de données {id}: {JsonSerializer.Serialize(entite, _jsonOptions)}");

            // Vérifier et compléter les IDs de référence
            await CompleterReferencesAsync(entite);

            // S'assurer que l'ID est correct
            entite.Id = id;

            // Créer un DTO pour l'API v2 (ActifDonneesMiseAJourDTO)
            var actifMiseAJourDTO = new
            {
                Nom = entite.Nom,
                Description = entite.Description,
                Type = entite.TypeActifDonnees?.Nom ?? entite.Type,
                Format = entite.FormatActifDonnees?.Nom ?? entite.Format,
                Source = entite.SourceActifDonnees?.Nom ?? entite.Source,
                Proprietaire = entite.Proprietaire,
                ClassificationSensibilite = (int)entite.ClassificationSensibilite,
                DateDerniereMiseAJour = entite.DateDerniereMiseAJour,
                FrequenceMiseAJour = entite.FrequenceMiseAJour?.Nom,
                CheminAcces = entite.CheminAcces,
                DomaineGouvernanceId = entite.DomaineGouvernanceId,
                EstElementCritique = entite.EstElementCritique,
                ConnexionSourceDonneesId = entite.ConnexionSourceDonneesId,
                Statut = entite.StatutActifDonnees?.Nom ?? entite.Statut
            };

            // Sérialiser le DTO
            var jsonContent = JsonSerializer.Serialize(actifMiseAJourDTO, _jsonOptions);
            Console.WriteLine($"Envoi du DTO à l'API: {jsonContent}");

            // Utiliser l'API v2 (endpoint correct)
            var apiV2Url = $"api/v2/ActifDonnees/{id}";
            Console.WriteLine($"Envoi de la requête à l'URL: {apiV2Url}");

            try
            {
                // Envoyer la requête PUT
                var response = await _httpClient.PutAsJsonAsync(apiV2Url, actifMiseAJourDTO, _jsonOptions);
                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Réponse du serveur: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    // Pour les réponses 204 NoContent, retourner l'entité mise à jour
                    if (response.StatusCode == System.Net.HttpStatusCode.NoContent)
                    {
                        Console.WriteLine("Mise à jour réussie (204 NoContent)");
                        return entite;
                    }

                    // Pour les autres réponses réussies, désérialiser la réponse
                    var result = JsonSerializer.Deserialize<ActifDonnees>(responseContent, _jsonOptions);
                    Console.WriteLine($"Actif de données mis à jour avec succès: {JsonSerializer.Serialize(result, _jsonOptions)}");

                    // Invalider le cache des actifs de données
                    _cacheService.Remove(CACHE_KEY_ALL_ACTIFS);
                    _cacheService.Remove($"{CACHE_KEY_ACTIF_PREFIX}{id}");
                    _cacheService.InvalidateGroup(CACHE_GROUP_ACTIFS);
                    Console.WriteLine("Cache des actifs de données invalidé après mise à jour");

                    return result ?? entite;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    // En cas d'erreur 400, utiliser le mode de secours
                    Console.Error.WriteLine($"Erreur 400 Bad Request: {responseContent}");
                    return await UtiliserModeSecoursMiseAJourAsync(id, entite);
                }
                else
                {
                    // Autres erreurs HTTP
                    Console.Error.WriteLine($"Erreur HTTP: {response.StatusCode}");
                    Console.Error.WriteLine($"Détails de l'erreur: {responseContent}");
                    throw new HttpRequestException($"Erreur HTTP {response.StatusCode}: {responseContent}");
                }
            }
            catch (Exception apiEx)
            {
                Console.Error.WriteLine($"Exception lors de l'appel à l'API: {apiEx.Message}");
                Console.Error.WriteLine($"Stack trace: {apiEx.StackTrace}");

                // En cas d'erreur, utiliser le mode de secours
                return await UtiliserModeSecoursMiseAJourAsync(id, entite);
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la mise à jour de l'actif de données {id}: {ex.Message}");

            if (ex is HttpRequestException httpEx)
            {
                Console.Error.WriteLine($"Détails de l'erreur HTTP: {httpEx.StatusCode}");
            }

            // Mode de secours
            return await UtiliserModeSecoursMiseAJourAsync(id, entite);
        }
    }

    /// <summary>
    /// Utilise le mode de secours pour mettre à jour un actif de données localement.
    /// </summary>
    /// <param name="id">L'ID de l'actif de données à mettre à jour.</param>
    /// <param name="entite">L'actif de données à mettre à jour.</param>
    /// <returns>L'actif de données mis à jour.</returns>
    private async Task<ActifDonnees> UtiliserModeSecoursMiseAJourAsync(Guid id, ActifDonnees entite)
    {
        Console.WriteLine("Utilisation du mode de secours pour la mise à jour d'actif de données");

        // S'assurer que l'ID est correct
        entite.Id = id;

        // Mettre à jour la date de modification
        entite.DateModification = DateTime.Now;

        // Compléter les propriétés de chaîne à partir des IDs
        if (entite.TypeActifDonneesId != null)
        {
            var type = await ObtenirTypeParIdAsync(entite.TypeActifDonneesId.Value);
            if (type != null)
            {
                entite.Type = type.Nom;
            }
        }

        if (entite.FormatActifDonneesId != null)
        {
            var format = await ObtenirFormatParIdAsync(entite.FormatActifDonneesId.Value);
            if (format != null)
            {
                entite.Format = format.Nom;
            }
        }

        if (entite.SourceActifDonneesId != null)
        {
            var source = await ObtenirSourceParIdAsync(entite.SourceActifDonneesId.Value);
            if (source != null)
            {
                entite.Source = source.Nom;
            }
        }

        if (entite.FrequenceMiseAJourId != null)
        {
            var frequence = await ObtenirFrequenceParIdAsync(entite.FrequenceMiseAJourId.Value);
            if (frequence != null)
            {
                entite.FrequenceMiseAJour = new FrequenceMiseAJour { Id = frequence.Id, Nom = frequence.Nom };
            }
        }

        if (entite.StatutActifDonneesId != null)
        {
            var statut = await ObtenirStatutParIdAsync(entite.StatutActifDonneesId.Value);
            if (statut != null)
            {
                entite.Statut = statut.Nom;
            }
        }

        // Sauvegarder dans le localStorage si disponible
        await SauvegarderDansLocalStorageAsync(entite);

        // Ajouter à la file d'attente de synchronisation
        await AjouterALaFileDeSynchronisationAsync(entite);

        return entite;
    }

    /// <inheritdoc/>
    public override async Task<bool> SupprimerAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/{id}");

            if (response.IsSuccessStatusCode)
            {
                // Invalider le cache des actifs de données
                _cacheService.Remove(CACHE_KEY_ALL_ACTIFS);
                _cacheService.Remove($"{CACHE_KEY_ACTIF_PREFIX}{id}");
                _cacheService.InvalidateGroup(CACHE_GROUP_ACTIFS);
                Console.WriteLine("Cache des actifs de données invalidé après suppression");
            }

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la suppression de l'actif de données {id}: {ex.Message}");

            // Mode de secours: simulation de la suppression en local
            Console.WriteLine("Utilisation du mode de secours pour la suppression d'actif de données");

            // Simuler une suppression réussie
            return true;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<StatutActifDonnees>> ObtenirStatutsAsync()
    {
        return await _cacheService.GetOrCreateAsync<IEnumerable<StatutActifDonnees>>(
            CACHE_KEY_STATUTS,
            async () =>
            {
                try
                {
                    if (_debugMode) Console.WriteLine("Récupération des statuts d'actifs de données depuis l'API (cache expiré ou non existant)");

                    // Appelle l'API
                    var response = await _httpClient.GetFromJsonAsync<IEnumerable<StatutActifDonnees>>("api/StatutActifDonnees", _jsonOptions);
                    var statuts = response ?? Enumerable.Empty<StatutActifDonnees>();

                    if (_debugMode) Console.WriteLine($"{statuts.Count()} statuts d'actifs de données mis en cache");

                    // Ajouter la clé au groupe de données de référence
                    _cacheService.AddToGroup(CACHE_GROUP_REFERENCE_DATA, CACHE_KEY_STATUTS);

                    return statuts;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'obtention des statuts d'actifs de données: {ex.Message}");

                    // En cas d'erreur, retourner une liste vide
                    return Enumerable.Empty<StatutActifDonnees>();
                }
            },
            CACHE_EXPIRATION_REFERENCE
        );
    }





    /// <summary>
    /// Obtient un type d'actif de données par son ID.
    /// </summary>
    /// <param name="id">L'ID du type d'actif de données.</param>
    /// <returns>Le type d'actif de données.</returns>
    public async Task<TypeActifDonneesItem?> ObtenirTypeParIdAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<TypeActifDonneesItem>($"api/TypeActifDonnees/{id}", _jsonOptions);
            return response;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention du type d'actif de données {id}: {ex.Message}");

            // Obtenir tous les types et trouver celui qui correspond à l'ID
            var types = await ObtenirTypesAsync();
            return types.FirstOrDefault(t => t.Id == id);
        }
    }

    /// <summary>
    /// Obtient un format d'actif de données par son ID.
    /// </summary>
    /// <param name="id">L'ID du format d'actif de données.</param>
    /// <returns>Le format d'actif de données.</returns>
    public async Task<FormatActifDonneesItem?> ObtenirFormatParIdAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<FormatActifDonneesItem>($"api/FormatActifDonnees/{id}", _jsonOptions);
            return response;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention du format d'actif de données {id}: {ex.Message}");

            // Obtenir tous les formats et trouver celui qui correspond à l'ID
            var formats = await ObtenirFormatsAsync();
            return formats.FirstOrDefault(f => f.Id == id);
        }
    }

    /// <summary>
    /// Obtient une source d'actif de données par son ID.
    /// </summary>
    /// <param name="id">L'ID de la source d'actif de données.</param>
    /// <returns>La source d'actif de données.</returns>
    public async Task<SourceActifDonneesItem?> ObtenirSourceParIdAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<SourceActifDonneesItem>($"api/SourceActifDonnees/{id}", _jsonOptions);
            return response;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention de la source d'actif de données {id}: {ex.Message}");

            // Obtenir toutes les sources et trouver celle qui correspond à l'ID
            var sources = await ObtenirSourcesAsync();
            return sources.FirstOrDefault(s => s.Id == id);
        }
    }

    /// <summary>
    /// Obtient une fréquence de mise à jour par son ID.
    /// </summary>
    /// <param name="id">L'ID de la fréquence de mise à jour.</param>
    /// <returns>La fréquence de mise à jour.</returns>
    public async Task<FrequenceMiseAJour?> ObtenirFrequenceParIdAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<FrequenceMiseAJour>($"api/FrequenceMiseAJour/{id}", _jsonOptions);
            return response;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention de la fréquence de mise à jour {id}: {ex.Message}");

            // Obtenir toutes les fréquences et trouver celle qui correspond à l'ID
            var frequences = await ObtenirFrequencesAsync();
            return frequences.FirstOrDefault(f => f.Id == id);
        }
    }

    /// <summary>
    /// Obtient un statut d'actif de données par son ID.
    /// </summary>
    /// <param name="id">L'ID du statut d'actif de données.</param>
    /// <returns>Le statut d'actif de données.</returns>
    public async Task<StatutActifDonnees?> ObtenirStatutParIdAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<StatutActifDonnees>($"api/StatutActifDonnees/{id}", _jsonOptions);
            return response;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention du statut d'actif de données {id}: {ex.Message}");

            // Obtenir tous les statuts et trouver celui qui correspond à l'ID
            var statuts = await ObtenirStatutsAsync();
            return statuts.FirstOrDefault(s => s.Id == id);
        }
    }

    /// <summary>
    /// Obtient tous les types d'actifs de données.
    /// </summary>
    /// <returns>Les types d'actifs de données.</returns>
    public async Task<IEnumerable<TypeActifDonneesItem>> ObtenirTypesAsync()
    {
        return await _cacheService.GetOrCreateAsync<IEnumerable<TypeActifDonneesItem>>(
            CACHE_KEY_TYPES,
            async () =>
            {
                try
                {
                    if (_debugMode) Console.WriteLine("Récupération des types d'actifs de données depuis l'API (cache expiré ou non existant)");

                    // Appelle l'API
                    var response = await _httpClient.GetFromJsonAsync<IEnumerable<TypeActifDonneesItem>>("api/TypeActifDonnees", _jsonOptions);
                    var types = response ?? Enumerable.Empty<TypeActifDonneesItem>();

                    if (_debugMode) Console.WriteLine($"{types.Count()} types d'actifs de données mis en cache");

                    // Ajouter la clé au groupe de données de référence
                    _cacheService.AddToGroup(CACHE_GROUP_REFERENCE_DATA, CACHE_KEY_TYPES);

                    return types;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'obtention des types d'actifs de données: {ex.Message}");

                    // En cas d'erreur, retourner une liste vide
                    return Enumerable.Empty<TypeActifDonneesItem>();
                }
            },
            CACHE_EXPIRATION_REFERENCE
        );
    }

    /// <summary>
    /// Obtient tous les formats d'actifs de données.
    /// </summary>
    /// <returns>Les formats d'actifs de données.</returns>
    public async Task<IEnumerable<FormatActifDonneesItem>> ObtenirFormatsAsync()
    {
        return await _cacheService.GetOrCreateAsync<IEnumerable<FormatActifDonneesItem>>(
            CACHE_KEY_FORMATS,
            async () =>
            {
                try
                {
                    if (_debugMode) Console.WriteLine("Récupération des formats d'actifs de données depuis l'API (cache expiré ou non existant)");

                    // Appelle l'API
                    var response = await _httpClient.GetFromJsonAsync<IEnumerable<FormatActifDonneesItem>>("api/FormatActifDonnees", _jsonOptions);
                    var formats = response ?? Enumerable.Empty<FormatActifDonneesItem>();

                    if (_debugMode) Console.WriteLine($"{formats.Count()} formats d'actifs de données mis en cache");

                    // Ajouter la clé au groupe de données de référence
                    _cacheService.AddToGroup(CACHE_GROUP_REFERENCE_DATA, CACHE_KEY_FORMATS);

                    return formats;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'obtention des formats d'actifs de données: {ex.Message}");

                    // En cas d'erreur, retourner une liste vide
                    return Enumerable.Empty<FormatActifDonneesItem>();
                }
            },
            CACHE_EXPIRATION_REFERENCE
        );
    }

    /// <summary>
    /// Obtient toutes les sources d'actifs de données.
    /// </summary>
    /// <returns>Les sources d'actifs de données.</returns>
    public async Task<IEnumerable<SourceActifDonneesItem>> ObtenirSourcesAsync()
    {
        return await _cacheService.GetOrCreateAsync<IEnumerable<SourceActifDonneesItem>>(
            CACHE_KEY_SOURCES,
            async () =>
            {
                try
                {
                    if (_debugMode) Console.WriteLine("Récupération des sources d'actifs de données depuis l'API (cache expiré ou non existant)");

                    // Appelle l'API
                    var response = await _httpClient.GetFromJsonAsync<IEnumerable<SourceActifDonneesItem>>("api/SourceActifDonnees", _jsonOptions);
                    var sources = response ?? Enumerable.Empty<SourceActifDonneesItem>();

                    if (_debugMode) Console.WriteLine($"{sources.Count()} sources d'actifs de données mises en cache");

                    // Ajouter la clé au groupe de données de référence
                    _cacheService.AddToGroup(CACHE_GROUP_REFERENCE_DATA, CACHE_KEY_SOURCES);

                    return sources;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'obtention des sources d'actifs de données: {ex.Message}");

                    // En cas d'erreur, retourner une liste vide
                    return Enumerable.Empty<SourceActifDonneesItem>();
                }
            },
            CACHE_EXPIRATION_REFERENCE
        );
    }

    /// <summary>
    /// Obtient toutes les fréquences de mise à jour.
    /// </summary>
    /// <returns>Les fréquences de mise à jour.</returns>
    public async Task<IEnumerable<FrequenceMiseAJour>> ObtenirFrequencesAsync()
    {
        return await _cacheService.GetOrCreateAsync<IEnumerable<FrequenceMiseAJour>>(
            CACHE_KEY_FREQUENCES,
            async () =>
            {
                try
                {
                    if (_debugMode) Console.WriteLine("Récupération des fréquences de mise à jour depuis l'API (cache expiré ou non existant)");

                    // Appelle l'API
                    var response = await _httpClient.GetFromJsonAsync<IEnumerable<FrequenceMiseAJour>>("api/FrequenceMiseAJour", _jsonOptions);
                    var frequences = response ?? Enumerable.Empty<FrequenceMiseAJour>();

                    if (_debugMode) Console.WriteLine($"{frequences.Count()} fréquences de mise à jour mises en cache");

                    // Ajouter la clé au groupe de données de référence
                    _cacheService.AddToGroup(CACHE_GROUP_REFERENCE_DATA, CACHE_KEY_FREQUENCES);

                    return frequences;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'obtention des fréquences de mise à jour: {ex.Message}");

                    // En cas d'erreur, retourner une liste vide
                    return Enumerable.Empty<FrequenceMiseAJour>();
                }
            },
            CACHE_EXPIRATION_REFERENCE
        );
    }

    /// <summary>
    /// Obtient tous les types de métadonnées.
    /// </summary>
    /// <returns>Les types de métadonnées.</returns>
    public async Task<IEnumerable<TypeMetadonneeItem>> ObtenirTypesMetadonneesAsync()
    {
        return await _cacheService.GetOrCreateAsync<IEnumerable<TypeMetadonneeItem>>(
            CACHE_KEY_TYPES_METADONNEES,
            async () =>
            {
                try
                {
                    if (_debugMode) Console.WriteLine("Récupération des types de métadonnées depuis l'API (cache expiré ou non existant)");

                    // Appelle l'API
                    var response = await _httpClient.GetFromJsonAsync<IEnumerable<TypeMetadonneeItem>>("api/TypeMetadonnee", _jsonOptions);
                    var types = response ?? Enumerable.Empty<TypeMetadonneeItem>();

                    if (_debugMode) Console.WriteLine($"{types.Count()} types de métadonnées mis en cache");

                    // Ajouter la clé au groupe de données de référence
                    _cacheService.AddToGroup(CACHE_GROUP_REFERENCE_DATA, CACHE_KEY_TYPES_METADONNEES);

                    return types;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'obtention des types de métadonnées: {ex.Message}");

                    // En cas d'erreur, retourner une liste vide
                    return Enumerable.Empty<TypeMetadonneeItem>();
                }
            },
            CACHE_EXPIRATION_REFERENCE
        );
    }

    /// <summary>
    /// Obtient toutes les catégories de métadonnées.
    /// </summary>
    /// <returns>Les catégories de métadonnées.</returns>
    public async Task<IEnumerable<CategorieMetadonneeItem>> ObtenirCategoriesMetadonneesAsync()
    {
        return await _cacheService.GetOrCreateAsync<IEnumerable<CategorieMetadonneeItem>>(
            CACHE_KEY_CATEGORIES_METADONNEES,
            async () =>
            {
                try
                {
                    if (_debugMode) Console.WriteLine("Récupération des catégories de métadonnées depuis l'API (cache expiré ou non existant)");

                    // Appelle l'API
                    var response = await _httpClient.GetFromJsonAsync<IEnumerable<CategorieMetadonneeItem>>("api/CategorieMetadonnee", _jsonOptions);
                    var categories = response ?? Enumerable.Empty<CategorieMetadonneeItem>();

                    if (_debugMode) Console.WriteLine($"{categories.Count()} catégories de métadonnées mises en cache");

                    // Ajouter la clé au groupe de données de référence
                    _cacheService.AddToGroup(CACHE_GROUP_REFERENCE_DATA, CACHE_KEY_CATEGORIES_METADONNEES);

                    return categories;
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors de l'obtention des catégories de métadonnées: {ex.Message}");

                    // En cas d'erreur, retourner une liste vide
                    return Enumerable.Empty<CategorieMetadonneeItem>();
                }
            },
            CACHE_EXPIRATION_REFERENCE
        );
    }
}
