using System.Text;
using DataHubGatineau.Web.Models.Policy;
using DataHubGatineau.Web.Services.Interfaces;

namespace DataHubGatineau.Web.Services.Implementations;

/// <summary>
/// Service pour l'exportation de documents.
/// </summary>
public class ExportService : IExportService
{
    private readonly IPolitiqueService _politiqueService;
    private readonly IConformitePolitiqueService _conformitePolitiqueService;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ExportService"/>.
    /// </summary>
    /// <param name="politiqueService">Service des politiques.</param>
    /// <param name="conformitePolitiqueService">Service de conformité aux politiques.</param>
    public ExportService(IPolitiqueService politiqueService, IConformitePolitiqueService conformitePolitiqueService)
    {
        _politiqueService = politiqueService;
        _conformitePolitiqueService = conformitePolitiqueService;
    }

    /// <inheritdoc/>
    public async Task<byte[]> ExporterPolitiquePdfAsync(Guid politiqueId)
    {
        var politique = await _politiqueService.ObtenirParIdAsync(politiqueId);
        if (politique == null)
        {
            throw new KeyNotFoundException($"Politique avec ID {politiqueId} non trouvée.");
        }

        var approbations = await _politiqueService.ObtenirApprobationsAsync(politiqueId);

        // Dans une implémentation réelle, on utiliserait une bibliothèque PDF comme iTextSharp ou PDFsharp
        // Pour simplifier, on génère un HTML qui pourrait être converti en PDF
        var html = GenererHtmlPolitique(politique, approbations);

        // Simuler la conversion en PDF
        return Encoding.UTF8.GetBytes(html);
    }

    /// <inheritdoc/>
    public async Task<byte[]> ExporterRapportConformitePdfAsync(Guid entiteId, string typeEntite)
    {
        var conformites = await _conformitePolitiqueService.ObtenirParEntiteAsync(entiteId, typeEntite);
        if (!conformites.Any())
        {
            throw new InvalidOperationException($"Aucune évaluation de conformité trouvée pour l'entité {entiteId} de type {typeEntite}.");
        }

        var scoreGlobal = await _conformitePolitiqueService.ObtenirScoreGlobalAsync(entiteId, typeEntite);

        // Dans une implémentation réelle, on utiliserait une bibliothèque PDF comme iTextSharp ou PDFsharp
        // Pour simplifier, on génère un HTML qui pourrait être converti en PDF
        var html = GenererHtmlRapportConformite(conformites, scoreGlobal, entiteId, typeEntite);

        // Simuler la conversion en PDF
        return Encoding.UTF8.GetBytes(html);
    }

    /// <inheritdoc/>
    public async Task<byte[]> ExporterConformitePdfAsync(Guid conformiteId)
    {
        var conformite = await _conformitePolitiqueService.ObtenirParIdAsync(conformiteId);
        if (conformite == null)
        {
            throw new KeyNotFoundException($"Évaluation de conformité avec ID {conformiteId} non trouvée.");
        }

        var politique = await _politiqueService.ObtenirParIdAsync(new Guid(conformite.PolitiqueId.ToString()));

        // Dans une implémentation réelle, on utiliserait une bibliothèque PDF comme iTextSharp ou PDFsharp
        // Pour simplifier, on génère un HTML qui pourrait être converti en PDF
        var html = GenererHtmlConformite(conformite, politique);

        // Simuler la conversion en PDF
        return Encoding.UTF8.GetBytes(html);
    }

    private string GenererHtmlPolitique(Politique politique, IEnumerable<ApprobationPolitique> approbations)
    {
        var sb = new StringBuilder();

        sb.AppendLine("<!DOCTYPE html>");
        sb.AppendLine("<html>");
        sb.AppendLine("<head>");
        sb.AppendLine("<meta charset=\"UTF-8\">");
        sb.AppendLine("<title>Politique - " + politique.Titre + "</title>");
        sb.AppendLine("<style>");
        sb.AppendLine("body { font-family: Arial, sans-serif; margin: 40px; }");
        sb.AppendLine("h1 { color: #333; }");
        sb.AppendLine("h2 { color: #555; margin-top: 20px; }");
        sb.AppendLine("h3 { color: #777; }");
        sb.AppendLine(".header { border-bottom: 1px solid #ddd; padding-bottom: 20px; margin-bottom: 20px; }");
        sb.AppendLine(".metadata { margin-bottom: 20px; }");
        sb.AppendLine(".metadata-item { margin-bottom: 5px; }");
        sb.AppendLine(".label { font-weight: bold; }");
        sb.AppendLine(".content { margin-bottom: 30px; }");
        sb.AppendLine(".footer { border-top: 1px solid #ddd; padding-top: 20px; margin-top: 20px; font-size: 0.8em; }");
        sb.AppendLine(".approbation { margin-bottom: 10px; padding: 10px; background-color: #f9f9f9; border-left: 3px solid #4285F4; }");
        sb.AppendLine("</style>");
        sb.AppendLine("</head>");
        sb.AppendLine("<body>");

        // En-tête
        sb.AppendLine("<div class=\"header\">");
        sb.AppendLine("<h1>" + politique.Titre + "</h1>");
        sb.AppendLine("<div>Code: " + politique.Code + " | Version: " + politique.Version + "</div>");
        sb.AppendLine("</div>");

        // Métadonnées
        sb.AppendLine("<div class=\"metadata\">");
        sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Type:</span> " + GetTypePolitiqueLabel(politique.Type) + "</div>");
        sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Statut:</span> " + GetStatutPolitiqueLabel(politique.Statut) + "</div>");
        sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Niveau d'application:</span> " + GetNiveauApplicationLabel(politique.NiveauApplication) + "</div>");

        if (!string.IsNullOrEmpty(politique.EntiteApplicationNom))
        {
            sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Entité d'application:</span> " + politique.EntiteApplicationNom + "</div>");
        }

        if (politique.DateEntreeVigueur.HasValue)
        {
            sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Date d'entrée en vigueur:</span> " + politique.DateEntreeVigueur.Value.ToString("dd/MM/yyyy") + "</div>");
        }

        if (politique.DateProchaineRevision.HasValue)
        {
            sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Prochaine révision:</span> " + politique.DateProchaineRevision.Value.ToString("dd/MM/yyyy") + "</div>");
        }

        if (!string.IsNullOrEmpty(politique.Description))
        {
            sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Description:</span> " + politique.Description + "</div>");
        }

        if (!string.IsNullOrEmpty(politique.MotsCles))
        {
            sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Mots-clés:</span> " + politique.MotsCles + "</div>");
        }

        sb.AppendLine("</div>");

        // Contenu
        sb.AppendLine("<div class=\"content\">");
        sb.AppendLine(ConvertirMarkdownEnHtml(politique.Contenu));
        sb.AppendLine("</div>");

        // Approbations
        if (approbations.Any())
        {
            sb.AppendLine("<h2>Approbations</h2>");
            foreach (var approbation in approbations)
            {
                sb.AppendLine("<div class=\"approbation\">");
                sb.AppendLine("<div><span class=\"label\">Approuvé par:</span> " + approbation.ApprobateurNom + "</div>");
                sb.AppendLine("<div><span class=\"label\">Rôle:</span> " + approbation.Role + "</div>");
                sb.AppendLine("<div><span class=\"label\">Date:</span> " + approbation.DateApprobation.ToString("dd/MM/yyyy") + "</div>");

                if (!string.IsNullOrEmpty(approbation.Commentaires))
                {
                    sb.AppendLine("<div><span class=\"label\">Commentaires:</span> " + approbation.Commentaires + "</div>");
                }

                sb.AppendLine("</div>");
            }
        }

        // Pied de page
        sb.AppendLine("<div class=\"footer\">");
        sb.AppendLine("<div>Document généré le " + DateTime.Now.ToString("dd/MM/yyyy HH:mm") + "</div>");
        sb.AppendLine("<div>DataHub Gatineau - Système de gouvernance des données</div>");
        sb.AppendLine("</div>");

        sb.AppendLine("</body>");
        sb.AppendLine("</html>");

        return sb.ToString();
    }

    private string GenererHtmlRapportConformite(IEnumerable<ConformitePolitique> conformites, double scoreGlobal, Guid entiteId, string typeEntite)
    {
        var sb = new StringBuilder();
        var entiteNom = conformites.First().EntiteEvalueeNom;

        sb.AppendLine("<!DOCTYPE html>");
        sb.AppendLine("<html>");
        sb.AppendLine("<head>");
        sb.AppendLine("<meta charset=\"UTF-8\">");
        sb.AppendLine("<title>Rapport de conformité - " + entiteNom + "</title>");
        sb.AppendLine("<style>");
        sb.AppendLine("body { font-family: Arial, sans-serif; margin: 40px; }");
        sb.AppendLine("h1 { color: #333; }");
        sb.AppendLine("h2 { color: #555; margin-top: 20px; }");
        sb.AppendLine("h3 { color: #777; }");
        sb.AppendLine(".header { border-bottom: 1px solid #ddd; padding-bottom: 20px; margin-bottom: 20px; }");
        sb.AppendLine(".score-global { font-size: 2em; font-weight: bold; margin: 20px 0; }");
        sb.AppendLine(".score-high { color: #34A853; }");
        sb.AppendLine(".score-medium { color: #FBBC05; }");
        sb.AppendLine(".score-low { color: #EA4335; }");
        sb.AppendLine("table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }");
        sb.AppendLine("th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }");
        sb.AppendLine("th { background-color: #f2f2f2; }");
        sb.AppendLine(".footer { border-top: 1px solid #ddd; padding-top: 20px; margin-top: 20px; font-size: 0.8em; }");
        sb.AppendLine("</style>");
        sb.AppendLine("</head>");
        sb.AppendLine("<body>");

        // En-tête
        sb.AppendLine("<div class=\"header\">");
        sb.AppendLine("<h1>Rapport de conformité</h1>");
        sb.AppendLine("<div>Entité: " + entiteNom + " (" + GetTypeEntiteLabel(typeEntite) + ")</div>");
        sb.AppendLine("<div>Date du rapport: " + DateTime.Now.ToString("dd/MM/yyyy") + "</div>");
        sb.AppendLine("</div>");

        // Score global
        sb.AppendLine("<h2>Score global de conformité</h2>");
        sb.AppendLine("<div class=\"score-global " + GetScoreClass(scoreGlobal) + "\">" + scoreGlobal.ToString("F1") + "%</div>");

        // Tableau des évaluations
        sb.AppendLine("<h2>Évaluations de conformité</h2>");
        sb.AppendLine("<table>");
        sb.AppendLine("<tr>");
        sb.AppendLine("<th>Politique</th>");
        sb.AppendLine("<th>Statut</th>");
        sb.AppendLine("<th>Score</th>");
        sb.AppendLine("<th>Date d'évaluation</th>");
        sb.AppendLine("</tr>");

        foreach (var conformite in conformites.OrderByDescending(c => c.Score))
        {
            sb.AppendLine("<tr>");
            sb.AppendLine("<td>" + conformite.PolitiqueTitre + "</td>");
            sb.AppendLine("<td>" + conformite.Statut + "</td>");
            sb.AppendLine("<td class=\"" + GetScoreClass(conformite.Score) + "\">" + (conformite.Score.HasValue ? conformite.Score.Value.ToString("F1") + "%" : "N/A") + "</td>");
            sb.AppendLine("<td>" + conformite.DateEvaluation.ToString("dd/MM/yyyy") + "</td>");
            sb.AppendLine("</tr>");
        }

        sb.AppendLine("</table>");

        // Résumé des non-conformités
        var nonConformites = conformites.Where(c => c.Statut != "Conforme").ToList();
        if (nonConformites.Any())
        {
            sb.AppendLine("<h2>Résumé des non-conformités</h2>");

            foreach (var nonConformite in nonConformites)
            {
                sb.AppendLine("<h3>" + nonConformite.PolitiqueTitre + "</h3>");
                sb.AppendLine("<p>" + nonConformite.Details + "</p>");

                if (!string.IsNullOrEmpty(nonConformite.ActionsRecommandees))
                {
                    sb.AppendLine("<p><strong>Actions recommandées:</strong> " + nonConformite.ActionsRecommandees + "</p>");
                }
            }
        }

        // Pied de page
        sb.AppendLine("<div class=\"footer\">");
        sb.AppendLine("<div>Document généré le " + DateTime.Now.ToString("dd/MM/yyyy HH:mm") + "</div>");
        sb.AppendLine("<div>DataHub Gatineau - Système de gouvernance des données</div>");
        sb.AppendLine("</div>");

        sb.AppendLine("</body>");
        sb.AppendLine("</html>");

        return sb.ToString();
    }

    private string GenererHtmlConformite(ConformitePolitique conformite, Politique? politique)
    {
        var sb = new StringBuilder();

        sb.AppendLine("<!DOCTYPE html>");
        sb.AppendLine("<html>");
        sb.AppendLine("<head>");
        sb.AppendLine("<meta charset=\"UTF-8\">");
        sb.AppendLine("<title>Évaluation de conformité - " + conformite.EntiteEvalueeNom + "</title>");
        sb.AppendLine("<style>");
        sb.AppendLine("body { font-family: Arial, sans-serif; margin: 40px; }");
        sb.AppendLine("h1 { color: #333; }");
        sb.AppendLine("h2 { color: #555; margin-top: 20px; }");
        sb.AppendLine("h3 { color: #777; }");
        sb.AppendLine(".header { border-bottom: 1px solid #ddd; padding-bottom: 20px; margin-bottom: 20px; }");
        sb.AppendLine(".metadata { margin-bottom: 20px; }");
        sb.AppendLine(".metadata-item { margin-bottom: 5px; }");
        sb.AppendLine(".label { font-weight: bold; }");
        sb.AppendLine(".score { font-size: 2em; font-weight: bold; margin: 20px 0; }");
        sb.AppendLine(".score-high { color: #34A853; }");
        sb.AppendLine(".score-medium { color: #FBBC05; }");
        sb.AppendLine(".score-low { color: #EA4335; }");
        sb.AppendLine(".details { margin-bottom: 30px; }");
        sb.AppendLine(".footer { border-top: 1px solid #ddd; padding-top: 20px; margin-top: 20px; font-size: 0.8em; }");
        sb.AppendLine("</style>");
        sb.AppendLine("</head>");
        sb.AppendLine("<body>");

        // En-tête
        sb.AppendLine("<div class=\"header\">");
        sb.AppendLine("<h1>Évaluation de conformité</h1>");
        sb.AppendLine("<div>Entité: " + conformite.EntiteEvalueeNom + " (" + GetTypeEntiteLabel(conformite.TypeEntite) + ")</div>");
        sb.AppendLine("<div>Politique: " + conformite.PolitiqueTitre + "</div>");
        sb.AppendLine("</div>");

        // Métadonnées
        sb.AppendLine("<div class=\"metadata\">");
        sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Statut:</span> " + conformite.Statut + "</div>");
        sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Date d'évaluation:</span> " + conformite.DateEvaluation.ToString("dd/MM/yyyy") + "</div>");

        if (conformite.DateProchaineEvaluation.HasValue)
        {
            sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Prochaine évaluation:</span> " + conformite.DateProchaineEvaluation.Value.ToString("dd/MM/yyyy") + "</div>");
        }

        sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Évalué par:</span> " + (conformite.EvalueParNom ?? "Inconnu") + (conformite.EstAutomatique ? " (Automatique)" : "") + "</div>");
        sb.AppendLine("</div>");

        // Score
        if (conformite.Score.HasValue)
        {
            sb.AppendLine("<h2>Score de conformité</h2>");
            sb.AppendLine("<div class=\"score " + GetScoreClass(conformite.Score) + "\">" + conformite.Score.Value.ToString("F1") + "%</div>");
        }

        // Détails
        sb.AppendLine("<h2>Détails de l'évaluation</h2>");
        sb.AppendLine("<div class=\"details\">");
        sb.AppendLine("<p>" + (conformite.Details ?? "Aucun détail disponible") + "</p>");

        if (!string.IsNullOrEmpty(conformite.ActionsRecommandees))
        {
            sb.AppendLine("<h3>Actions recommandées</h3>");
            sb.AppendLine("<p>" + conformite.ActionsRecommandees + "</p>");
        }

        sb.AppendLine("</div>");

        // Politique
        if (politique != null)
        {
            sb.AppendLine("<h2>Résumé de la politique</h2>");
            sb.AppendLine("<div class=\"metadata\">");
            sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Code:</span> " + politique.Code + "</div>");
            sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Version:</span> " + politique.Version + "</div>");
            sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Type:</span> " + GetTypePolitiqueLabel(politique.Type) + "</div>");

            if (!string.IsNullOrEmpty(politique.Description))
            {
                sb.AppendLine("<div class=\"metadata-item\"><span class=\"label\">Description:</span> " + politique.Description + "</div>");
            }

            sb.AppendLine("</div>");
        }

        // Pied de page
        sb.AppendLine("<div class=\"footer\">");
        sb.AppendLine("<div>Document généré le " + DateTime.Now.ToString("dd/MM/yyyy HH:mm") + "</div>");
        sb.AppendLine("<div>DataHub Gatineau - Système de gouvernance des données</div>");
        sb.AppendLine("</div>");

        sb.AppendLine("</body>");
        sb.AppendLine("</html>");

        return sb.ToString();
    }

    private string ConvertirMarkdownEnHtml(string markdown)
    {
        // Dans une implémentation réelle, on utiliserait une bibliothèque Markdown
        // Pour simplifier, on fait une conversion basique
        var html = markdown
            .Replace("\n\n", "<br><br>")
            .Replace("\n", "<br>")
            .Replace("# ", "<h2>").Replace(" #", "</h2>")
            .Replace("## ", "<h3>").Replace(" ##", "</h3>")
            .Replace("### ", "<h4>").Replace(" ###", "</h4>")
            .Replace("- ", "<li>").Replace(" -", "</li>");

        return html;
    }

    private string GetTypePolitiqueLabel(TypePolitique type)
    {
        return type switch
        {
            TypePolitique.Gouvernance => "Gouvernance",
            TypePolitique.Qualite => "Qualité",
            TypePolitique.Securite => "Sécurité",
            TypePolitique.Confidentialite => "Confidentialité",
            TypePolitique.Conservation => "Conservation",
            TypePolitique.Acces => "Accès",
            TypePolitique.Partage => "Partage",
            TypePolitique.StandardTechnique => "Standard technique",
            TypePolitique.StandardMetier => "Standard métier",
            TypePolitique.Autre => "Autre",
            _ => type.ToString()
        };
    }

    private string GetStatutPolitiqueLabel(StatutPolitique statut)
    {
        return statut switch
        {
            StatutPolitique.Brouillon => "Brouillon",
            StatutPolitique.EnRevision => "En révision",
            StatutPolitique.Active => "Active",
            StatutPolitique.Archivee => "Archivée",
            StatutPolitique.Obsolete => "Obsolète",
            _ => statut.ToString()
        };
    }

    private string GetNiveauApplicationLabel(NiveauApplicationPolitique niveau)
    {
        return niveau switch
        {
            NiveauApplicationPolitique.Organisation => "Organisation",
            NiveauApplicationPolitique.Departement => "Département",
            NiveauApplicationPolitique.Projet => "Projet",
            NiveauApplicationPolitique.Domaine => "Domaine",
            NiveauApplicationPolitique.Actif => "Actif de données",
            NiveauApplicationPolitique.Systeme => "Système",
            _ => niveau.ToString()
        };
    }

    private string GetTypeEntiteLabel(string typeEntite)
    {
        return typeEntite switch
        {
            "ActifDonnees" => "Actif de données",
            "Departement" => "Département",
            "Projet" => "Projet",
            _ => typeEntite
        };
    }

    private string GetScoreClass(double? score)
    {
        if (!score.HasValue)
        {
            return "";
        }

        return score.Value switch
        {
            >= 90 => "score-high",
            >= 70 => "score-medium",
            _ => "score-low"
        };
    }
}

