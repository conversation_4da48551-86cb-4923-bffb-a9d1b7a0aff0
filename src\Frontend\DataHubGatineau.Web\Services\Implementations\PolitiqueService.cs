using System.Text.Json;
using DataHubGatineau.Web.Models.Policy;
using DataHubGatineau.Web.Services.Interfaces;

namespace DataHubGatineau.Web.Services.Implementations;

/// <summary>
/// Service pour les opérations sur les politiques.
/// </summary>
public class PolitiqueService : ServiceBaseGuid<Politique>, IPolitiqueService
{
    // Nous n'utilisons plus de données simulées, tout est récupéré depuis l'API

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="PolitiqueService"/>.
    /// </summary>
    /// <param name="httpClient">Client HTTP.</param>
    public PolitiqueService(HttpClient httpClient)
        : base(httpClient, "api/v1/Politiques")
    {
    }

    /// <inheritdoc/>
    public override async Task<IEnumerable<Politique>> ObtenirTousAsync()
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<Politique>>(_baseUrl, _jsonOptions);
            return response ?? Enumerable.Empty<Politique>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des politiques: {ex.Message}");
            return Enumerable.Empty<Politique>();
        }
    }

    /// <inheritdoc/>
    public override async Task<Politique?> ObtenirParIdAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            return await _httpClient.GetFromJsonAsync<Politique>($"{_baseUrl}/{id}", _jsonOptions);
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention de la politique {id}: {ex.Message}");
            return null;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<Politique>> ObtenirParTypeAsync(TypePolitique type)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<Politique>>($"{_baseUrl}/parType/{type}", _jsonOptions);
            return response ?? Enumerable.Empty<Politique>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des politiques par type {type}: {ex.Message}");
            return Enumerable.Empty<Politique>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<Politique>> ObtenirParStatutAsync(StatutPolitique statut)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<Politique>>($"{_baseUrl}/parStatut/{statut}", _jsonOptions);
            return response ?? Enumerable.Empty<Politique>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des politiques par statut {statut}: {ex.Message}");
            return Enumerable.Empty<Politique>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<Politique>> ObtenirParNiveauApplicationAsync(NiveauApplication niveau)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<Politique>>($"{_baseUrl}/parNiveau/{niveau}", _jsonOptions);
            return response ?? Enumerable.Empty<Politique>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des politiques par niveau {niveau}: {ex.Message}");
            return Enumerable.Empty<Politique>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<Politique>> ObtenirParEntiteApplicationAsync(Guid entiteId, string typeEntite)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<Politique>>($"{_baseUrl}/parEntite/{entiteId}/{typeEntite}", _jsonOptions);
            return response ?? Enumerable.Empty<Politique>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des politiques par entité {entiteId}/{typeEntite}: {ex.Message}");
            return Enumerable.Empty<Politique>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<Politique>> ObtenirParMotCleAsync(string motCle)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<Politique>>($"{_baseUrl}/parMotCle/{Uri.EscapeDataString(motCle)}", _jsonOptions);
            return response ?? Enumerable.Empty<Politique>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des politiques par mot-clé {motCle}: {ex.Message}");
            return Enumerable.Empty<Politique>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<Politique>> ObtenirVersionsAsync(Guid politiqueId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<Politique>>($"{_baseUrl}/{politiqueId}/versions", _jsonOptions);
            return response ?? Enumerable.Empty<Politique>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des versions de la politique {politiqueId}: {ex.Message}");
            return Enumerable.Empty<Politique>();
        }
    }

    /// <inheritdoc/>
    public async Task<Politique> CreerNouvelleVersionAsync(Guid politiqueId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsync($"{_baseUrl}/{politiqueId}/versions", null);
            response.EnsureSuccessStatusCode();

            var nouvelleVersion = await response.Content.ReadFromJsonAsync<Politique>(_jsonOptions);
            if (nouvelleVersion == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de politique.");
            }

            return nouvelleVersion;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la création d'une nouvelle version de la politique {politiqueId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<Politique> ApprouverPolitiqueAsync(Guid politiqueId, ApprobationPolitique approbation)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/{politiqueId}/approuver", approbation, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var politiqueApprouvee = await response.Content.ReadFromJsonAsync<Politique>(_jsonOptions);
            if (politiqueApprouvee == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de politique.");
            }

            return politiqueApprouvee;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'approbation de la politique {politiqueId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<Politique> ArchiverPolitiqueAsync(Guid politiqueId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsync($"{_baseUrl}/{politiqueId}/archiver", null);
            response.EnsureSuccessStatusCode();

            var politiqueArchivee = await response.Content.ReadFromJsonAsync<Politique>(_jsonOptions);
            if (politiqueArchivee == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de politique.");
            }

            return politiqueArchivee;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'archivage de la politique {politiqueId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ApprobationPolitique>> ObtenirApprobationsAsync(Guid politiqueId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ApprobationPolitique>>($"{_baseUrl}/{politiqueId}/approbations", _jsonOptions);
            return response ?? Enumerable.Empty<ApprobationPolitique>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des approbations de la politique {politiqueId}: {ex.Message}");
            return Enumerable.Empty<ApprobationPolitique>();
        }
    }

    /// <inheritdoc/>
    public async Task<ApprobationPolitique> AjouterApprobationAsync(Guid politiqueId, ApprobationPolitique approbation)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/{politiqueId}/approbations", approbation, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var nouvelleApprobation = await response.Content.ReadFromJsonAsync<ApprobationPolitique>(_jsonOptions);
            if (nouvelleApprobation == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas d'approbation.");
            }

            return nouvelleApprobation;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'ajout d'une approbation à la politique {politiqueId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<Politique>> RechercherAsync(string texte)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<Politique>>($"{_baseUrl}/rechercher/{Uri.EscapeDataString(texte)}", _jsonOptions);
            return response ?? Enumerable.Empty<Politique>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la recherche de politiques avec le texte '{texte}': {ex.Message}");
            return Enumerable.Empty<Politique>();
        }
    }

    /// <inheritdoc/>
    public async Task<string> ExporterPdfAsync(Guid politiqueId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetAsync($"{_baseUrl}/{politiqueId}/exporterPdf");
            response.EnsureSuccessStatusCode();

            var cheminPdf = await response.Content.ReadAsStringAsync();
            return cheminPdf;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'exportation de la politique {politiqueId} en PDF: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public override async Task<Politique> AjouterAsync(Politique entite)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsJsonAsync(_baseUrl, entite, _jsonOptions);

            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.Error.WriteLine($"Erreur HTTP {(int)response.StatusCode}: {errorContent}");
                throw new HttpRequestException($"Erreur lors de l'ajout de la politique: {response.StatusCode} - {errorContent}");
            }

            var nouvellePolitique = await response.Content.ReadFromJsonAsync<Politique>(_jsonOptions);
            if (nouvellePolitique == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de politique.");
            }

            return nouvellePolitique;
        }
        catch (HttpRequestException)
        {
            throw; // Re-lancer les erreurs HTTP avec le message détaillé
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'ajout d'une politique: {ex.Message}");
            throw new InvalidOperationException($"Erreur lors de l'ajout de la politique: {ex.Message}", ex);
        }
    }

    /// <inheritdoc/>
    public override async Task<Politique> MettreAJourAsync(Guid id, Politique entite)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/{id}", entite, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var politiqueMiseAJour = await response.Content.ReadFromJsonAsync<Politique>(_jsonOptions);
            if (politiqueMiseAJour == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de politique.");
            }

            return politiqueMiseAJour;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la mise à jour de la politique {id}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public override async Task<bool> SupprimerAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la suppression de la politique {id}: {ex.Message}");
            return false;
        }
    }

    // Cette méthode est conservée car elle peut être utile pour le client
    private static string IncrementVersion(string version)
    {
        if (Version.TryParse(version, out var v))
        {
            return new Version(v.Major, v.Minor + 1).ToString();
        }

        // Si le format n'est pas standard, ajouter simplement .1
        return $"{version}.1";
    }
}

