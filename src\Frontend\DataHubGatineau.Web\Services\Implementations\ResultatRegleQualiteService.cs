using DataHubGatineau.Web.Models;
using DataHubGatineau.Web.Services.Interfaces;
using DataHubGatineau.Domain.Enums;
using System.Text.Json;

namespace DataHubGatineau.Web.Services.Implementations;

/// <summary>
/// Service pour les opérations sur les résultats de règles de qualité.
/// </summary>
public class ResultatRegleQualiteService : ServiceBaseGuid<ResultatRegleQualite>, IResultatRegleQualiteService
{
    // Nous n'utilisons plus de données simulées, tout est récupéré depuis l'API

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ResultatRegleQualiteService"/>.
    /// </summary>
    /// <param name="httpClient">Client HTTP.</param>
    public ResultatRegleQualiteService(HttpClient httpClient)
        : base(httpClient, "api/v1/ResultatsRegleQualite")
    {
    }

    /// <inheritdoc/>
    public override async Task<IEnumerable<ResultatRegleQualite>> ObtenirTousAsync()
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ResultatRegleQualite>>(_baseUrl, _jsonOptions);
            return response ?? Enumerable.Empty<ResultatRegleQualite>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des résultats de règles de qualité: {ex.Message}");
            return Enumerable.Empty<ResultatRegleQualite>();
        }
    }

    /// <inheritdoc/>
    public override async Task<ResultatRegleQualite?> ObtenirParIdAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            return await _httpClient.GetFromJsonAsync<ResultatRegleQualite>($"{_baseUrl}/{id}", _jsonOptions);
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention du résultat de règle de qualité {id}: {ex.Message}");
            return null;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ResultatRegleQualite>> ObtenirParRegleQualiteAsync(Guid regleQualiteId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ResultatRegleQualite>>($"{_baseUrl}/parRegleQualite/{regleQualiteId}", _jsonOptions);
            return response ?? Enumerable.Empty<ResultatRegleQualite>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des résultats par règle de qualité {regleQualiteId}: {ex.Message}");
            return Enumerable.Empty<ResultatRegleQualite>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ResultatRegleQualite>> ObtenirParActifDonneesAsync(Guid actifDonneesId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ResultatRegleQualite>>($"{_baseUrl}/parActifDonnees/{actifDonneesId}", _jsonOptions);
            return response ?? Enumerable.Empty<ResultatRegleQualite>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des résultats par actif de données {actifDonneesId}: {ex.Message}");
            return Enumerable.Empty<ResultatRegleQualite>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ResultatRegleQualite>> ObtenirParStatutAsync(StatutResultatRegleQualite statut)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ResultatRegleQualite>>($"{_baseUrl}/parStatut/{statut}", _jsonOptions);
            return response ?? Enumerable.Empty<ResultatRegleQualite>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des résultats par statut {statut}: {ex.Message}");
            return Enumerable.Empty<ResultatRegleQualite>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ResultatRegleQualite>> ObtenirDerniersResultatsAsync(int nombre = 10)
    {
        try
        {
            // Appelle l'API - le backend n'utilise pas le paramètre nombre dans l'URL
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<ResultatRegleQualite>>($"{_baseUrl}/derniers", _jsonOptions);
            var resultats = response ?? Enumerable.Empty<ResultatRegleQualite>();

            // Limiter le nombre de résultats côté client
            return resultats.Take(nombre);
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des derniers résultats: {ex.Message}");
            return Enumerable.Empty<ResultatRegleQualite>();
        }
    }

    /// <inheritdoc/>
    public override async Task<ResultatRegleQualite> AjouterAsync(ResultatRegleQualite entite)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsJsonAsync(_baseUrl, entite, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var resultat = await response.Content.ReadFromJsonAsync<ResultatRegleQualite>(_jsonOptions);
            if (resultat == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de résultat de règle de qualité.");
            }

            return resultat;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'ajout du résultat de règle de qualité: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public override async Task<ResultatRegleQualite> MettreAJourAsync(Guid id, ResultatRegleQualite entite)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/{id}", entite, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var resultat = await response.Content.ReadFromJsonAsync<ResultatRegleQualite>(_jsonOptions);
            if (resultat == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de résultat de règle de qualité.");
            }

            return resultat;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la mise à jour du résultat de règle de qualité {id}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public override async Task<bool> SupprimerAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la suppression du résultat de règle de qualité {id}: {ex.Message}");
            return false;
        }
    }

    /// <inheritdoc/>
    public async Task<ResultatRegleQualite> ExecuterRegleAsync(Guid regleQualiteId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsync($"api/v1/ReglesQualite/{regleQualiteId}/executer", null);
            response.EnsureSuccessStatusCode();

            var resultat = await response.Content.ReadFromJsonAsync<ResultatRegleQualite>(_jsonOptions);
            if (resultat == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de résultat de règle de qualité.");
            }

            return resultat;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'exécution de la règle de qualité {regleQualiteId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ResultatRegleQualite>> ExecuterReglesParActifDonneesAsync(Guid actifDonneesId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsync($"api/v1/ReglesQualite/actif/{actifDonneesId}/executer", null);
            response.EnsureSuccessStatusCode();

            var resultats = await response.Content.ReadFromJsonAsync<IEnumerable<ResultatRegleQualite>>(_jsonOptions);
            return resultats ?? Enumerable.Empty<ResultatRegleQualite>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'exécution des règles de qualité pour l'actif de données {actifDonneesId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<Dictionary<string, int>> ObtenirStatistiquesAsync()
    {
        try
        {
            var resultats = await ObtenirTousAsync();
            var stats = new Dictionary<string, int>
            {
                { "Total", resultats.Count() },
                { "Succès", resultats.Count(r => r.Statut == StatutResultatRegleQualite.Succes) },
                { "Échecs", resultats.Count(r => r.Statut == StatutResultatRegleQualite.Echec) },
                { "Avertissements", resultats.Count(r => r.Statut == StatutResultatRegleQualite.Avertissement) },
                { "Erreurs", resultats.Count(r => r.Statut == StatutResultatRegleQualite.Erreur) },
                { "Dernières 24h", resultats.Count(r => r.DateExecution >= DateTime.Now.AddDays(-1)) },
                { "Derniers 7j", resultats.Count(r => r.DateExecution >= DateTime.Now.AddDays(-7)) },
                { "Derniers 30j", resultats.Count(r => r.DateExecution >= DateTime.Now.AddDays(-30)) }
            };
            return stats;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des statistiques des résultats de règles de qualité: {ex.Message}");
            return new Dictionary<string, int>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<ResultatRegleQualite>> ObtenirRecentsAsync(int jours)
    {
        try
        {
            var dateLimit = DateTime.Now.AddDays(-jours);
            var resultats = await ObtenirTousAsync();
            return resultats.Where(r => r.DateExecution >= dateLimit);
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des résultats récents: {ex.Message}");
            return Enumerable.Empty<ResultatRegleQualite>();
        }
    }
}
