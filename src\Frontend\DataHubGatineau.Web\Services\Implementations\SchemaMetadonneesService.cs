using DataHubGatineau.Web.Models.Metadata;
using DataHubGatineau.Web.Services.Interfaces;
using System.Text.Json;

namespace DataHubGatineau.Web.Services.Implementations;

/// <summary>
/// Service pour les opérations sur les schémas de métadonnées.
/// </summary>
public class SchemaMetadonneesService : ServiceBaseGuid<SchemaMetadonnees>, ISchemaMetadonneesService
{
    // Nous n'utilisons plus de données simulées, tout est récupéré depuis l'API
    private readonly JsonSerializerOptions _jsonOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="SchemaMetadonneesService"/>.
    /// </summary>
    /// <param name="httpClient">Client HTTP.</param>
    public SchemaMetadonneesService(HttpClient httpClient)
        : base(httpClient, "api/v1/SchemasMetadonnees")
    {
    }

    /// <inheritdoc/>
    public override async Task<IEnumerable<SchemaMetadonnees>> ObtenirTousAsync()
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<SchemaMetadonnees>>(_baseUrl, _jsonOptions);
            return response ?? Enumerable.Empty<SchemaMetadonnees>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des schémas de métadonnées: {ex.Message}");
            return Enumerable.Empty<SchemaMetadonnees>();
        }
    }

    /// <inheritdoc/>
    public override async Task<SchemaMetadonnees?> ObtenirParIdAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            return await _httpClient.GetFromJsonAsync<SchemaMetadonnees>($"{_baseUrl}/{id}", _jsonOptions);
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention du schéma de métadonnées {id}: {ex.Message}");
            return null;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<SchemaMetadonnees>> ObtenirParTypeActifAsync(string typeActif)
    {
        try
        {
            // Appelle l'API avec la bonne URL
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<SchemaMetadonnees>>($"{_baseUrl}/type-actif/{Uri.EscapeDataString(typeActif)}", _jsonOptions);
            return response ?? Enumerable.Empty<SchemaMetadonnees>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des schémas de métadonnées par type d'actif {typeActif}: {ex.Message}");
            return Enumerable.Empty<SchemaMetadonnees>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<SchemaMetadonnees>> ObtenirActifsAsync()
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<SchemaMetadonnees>>($"{_baseUrl}/actifs", _jsonOptions);
            return response ?? Enumerable.Empty<SchemaMetadonnees>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des schémas de métadonnées actifs: {ex.Message}");
            return Enumerable.Empty<SchemaMetadonnees>();
        }
    }

    /// <inheritdoc/>
    public async Task<SchemaMetadonnees?> ObtenirActifParTypeActifAsync(string typeActif)
    {
        try
        {
            Console.WriteLine($"🔍 SchemaMetadonneesService - Recherche du schéma actif pour le type: {typeActif}");
            var url = $"{_baseUrl}/actif/type-actif/{Uri.EscapeDataString(typeActif)}";
            Console.WriteLine($"🌐 SchemaMetadonneesService - URL: {url}");

            var response = await _httpClient.GetAsync(url);
            Console.WriteLine($"📡 SchemaMetadonneesService - Status: {response.StatusCode}");

            if (response.IsSuccessStatusCode)
            {
                var schema = await response.Content.ReadFromJsonAsync<SchemaMetadonnees>(_jsonOptions);
                Console.WriteLine($"✅ SchemaMetadonneesService - Schéma trouvé: {schema?.Nom ?? "null"}");
                return schema;
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"❌ SchemaMetadonneesService - Erreur: {errorContent}");
                return null;
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"❌ SchemaMetadonneesService - Exception: {ex.Message}");
            return null;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<DefinitionMetadonnee>> ObtenirDefinitionsMetadonneesAsync(Guid schemaId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.GetFromJsonAsync<IEnumerable<DefinitionMetadonnee>>($"{_baseUrl}/{schemaId}/definitions", _jsonOptions);
            return response ?? Enumerable.Empty<DefinitionMetadonnee>();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des définitions de métadonnées pour le schéma {schemaId}: {ex.Message}");
            return Enumerable.Empty<DefinitionMetadonnee>();
        }
    }

    /// <inheritdoc/>
    public async Task<DefinitionMetadonnee> AjouterDefinitionMetadonneeAsync(Guid schemaId, DefinitionMetadonnee definition)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/{schemaId}/definitions", definition, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var nouvelleDefinition = await response.Content.ReadFromJsonAsync<DefinitionMetadonnee>(_jsonOptions);
            if (nouvelleDefinition == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de définition de métadonnée.");
            }

            return nouvelleDefinition;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'ajout d'une définition de métadonnée au schéma {schemaId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<DefinitionMetadonnee> MettreAJourDefinitionMetadonneeAsync(Guid definitionId, DefinitionMetadonnee definition)
    {
        try
        {
            // Appelle l'API - Inclure le schemaId dans l'URL
            var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/{definition.SchemaMetadonneesId}/definitions/{definitionId}", definition, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var definitionMiseAJour = await response.Content.ReadFromJsonAsync<DefinitionMetadonnee>(_jsonOptions);
            if (definitionMiseAJour == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de définition de métadonnée.");
            }

            return definitionMiseAJour;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la mise à jour de la définition de métadonnée {definitionId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<bool> SupprimerDefinitionMetadonneeAsync(Guid schemaId, Guid definitionId)
    {
        try
        {
            // Appelle l'API - Inclure le schemaId dans l'URL
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/{schemaId}/definitions/{definitionId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la suppression de la définition de métadonnée {definitionId}: {ex.Message}");
            return false;
        }
    }

    /// <inheritdoc/>
    public async Task<SchemaMetadonnees> ActiverSchemaAsync(Guid schemaId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsync($"{_baseUrl}/{schemaId}/activer", null);
            response.EnsureSuccessStatusCode();

            var schemaActive = await response.Content.ReadFromJsonAsync<SchemaMetadonnees>(_jsonOptions);
            if (schemaActive == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de schéma de métadonnées.");
            }

            return schemaActive;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'activation du schéma de métadonnées {schemaId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<SchemaMetadonnees> CreerNouvelleVersionAsync(Guid schemaId)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsync($"{_baseUrl}/{schemaId}/versions", null);
            response.EnsureSuccessStatusCode();

            var nouvelleVersion = await response.Content.ReadFromJsonAsync<SchemaMetadonnees>(_jsonOptions);
            if (nouvelleVersion == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de schéma de métadonnées.");
            }

            return nouvelleVersion;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la création d'une nouvelle version du schéma de métadonnées {schemaId}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public override async Task<SchemaMetadonnees> AjouterAsync(SchemaMetadonnees entite)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PostAsJsonAsync(_baseUrl, entite, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var nouveauSchema = await response.Content.ReadFromJsonAsync<SchemaMetadonnees>(_jsonOptions);
            if (nouveauSchema == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de schéma de métadonnées.");
            }

            return nouveauSchema;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'ajout d'un schéma de métadonnées: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public override async Task<SchemaMetadonnees> MettreAJourAsync(Guid id, SchemaMetadonnees entite)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/{id}", entite, _jsonOptions);
            response.EnsureSuccessStatusCode();

            var schemaMisAJour = await response.Content.ReadFromJsonAsync<SchemaMetadonnees>(_jsonOptions);
            if (schemaMisAJour == null)
            {
                throw new InvalidOperationException("La réponse ne contient pas de schéma de métadonnées.");
            }

            return schemaMisAJour;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la mise à jour du schéma de métadonnées {id}: {ex.Message}");
            throw;
        }
    }

    /// <inheritdoc/>
    public override async Task<bool> SupprimerAsync(Guid id)
    {
        try
        {
            // Appelle l'API
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la suppression du schéma de métadonnées {id}: {ex.Message}");
            return false;
        }
    }

    // Cette méthode est conservée car elle peut être utile pour le client
    private static string IncrementVersion(string version)
    {
        if (Version.TryParse(version, out var v))
        {
            return new Version(v.Major, v.Minor + 1).ToString();
        }

        // Si le format n'est pas standard, ajouter simplement .1
        return $"{version}.1";
    }
}

