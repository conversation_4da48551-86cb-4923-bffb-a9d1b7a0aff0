using DataHubGatineau.Web.Models.Search;
using DataHubGatineau.Web.Services.Interfaces;
using System.Diagnostics;
using System.Text.RegularExpressions;

namespace DataHubGatineau.Web.Services.Implementations;

/// <summary>
/// Service pour les opérations de recherche.
/// </summary>
public class SearchService : ISearchService
{
    private readonly IActifDonneesService _actifDonneesService;
    private readonly IMetadonneeService _metadonneeService;
    private readonly IRegleQualiteService _regleQualiteService;
    private readonly IResultatRegleQualiteService _resultatRegleQualiteService;
    private readonly ITermeGlossaireService _termeGlossaireService;
    private readonly IPolitiqueService _politiqueService;
    private readonly IConformitePolitiqueService _conformitePolitiqueService;
    // private readonly IDomaineGouvernanceService _domaineGouvernanceService; // TODO: Implementar

    private readonly List<string> _historiqueRecherche = new();

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="SearchService"/>.
    /// </summary>
    /// <param name="actifDonneesService">Service des actifs de données.</param>
    /// <param name="metadonneeService">Service des métadonnées.</param>
    /// <param name="regleQualiteService">Service des règles de qualité.</param>
    /// <param name="resultatRegleQualiteService">Service des résultats de règles de qualité.</param>
    /// <param name="termeGlossaireService">Service des termes du glossaire.</param>
    /// <param name="politiqueService">Service des politiques.</param>
    /// <param name="conformitePolitiqueService">Service des conformités de politiques.</param>
    public SearchService(
        IActifDonneesService actifDonneesService,
        IMetadonneeService metadonneeService,
        IRegleQualiteService regleQualiteService,
        IResultatRegleQualiteService resultatRegleQualiteService,
        ITermeGlossaireService termeGlossaireService,
        IPolitiqueService politiqueService,
        IConformitePolitiqueService conformitePolitiqueService)
    {
        _actifDonneesService = actifDonneesService;
        _metadonneeService = metadonneeService;
        _regleQualiteService = regleQualiteService;
        _resultatRegleQualiteService = resultatRegleQualiteService;
        _termeGlossaireService = termeGlossaireService;
        _politiqueService = politiqueService;
        _conformitePolitiqueService = conformitePolitiqueService;
        // _domaineGouvernanceService = domaineGouvernanceService; // TODO: Implementar
    }

    /// <inheritdoc/>
    public async Task<ResultatsRecherche> RechercherAsync(ParametresRecherche parametres)
    {
        var stopwatch = Stopwatch.StartNew();

        var resultats = new ResultatsRecherche
        {
            Termes = parametres.Termes,
            Debut = (parametres.Page - 1) * parametres.NombreResultats,
            Limite = parametres.NombreResultats
        };

        // Vérifier si les termes de recherche sont vides
        if (string.IsNullOrWhiteSpace(parametres.Termes))
        {
            stopwatch.Stop();
            resultats.TempsExecution = stopwatch.ElapsedMilliseconds;
            return resultats;
        }

        // Déterminer les types à rechercher
        var typesRecherche = parametres.Types.Any() ? parametres.Types : Enum.GetValues<TypeResultatRecherche>().ToList();

        // Effectuer la recherche pour chaque type
        var tousResultats = new List<ResultatRecherche>();

        // Recherche dans les actifs de données
        if (typesRecherche.Contains(TypeResultatRecherche.ActifDonnees))
        {
            var actifs = await _actifDonneesService.ObtenirTousAsync();
            var resultatsActifs = actifs
                .Where(a => ContientTermes(a.Nom, parametres.Termes) ||
                           ContientTermes(a.Description, parametres.Termes))
                .Select(a => new ResultatRecherche
                {
                    Id = a.Id.ToString(),
                    Titre = a.Nom,
                    Description = a.Description,
                    Type = TypeResultatRecherche.ActifDonnees,
                    Url = $"/actifs-donnees-details/{a.Id}",
                    Date = a.DateCreation,
                    Score = CalculerScore(a.Nom, a.Description, parametres.Termes)
                })
                .ToList();

            tousResultats.AddRange(resultatsActifs);
            resultats.StatistiquesParType[TypeResultatRecherche.ActifDonnees] = resultatsActifs.Count();
        }

        // Recherche dans les métadonnées
        if (typesRecherche.Contains(TypeResultatRecherche.Metadonnee))
        {
            var metadonnees = await _metadonneeService.ObtenirTousAsync();
            var resultatsMetadonnees = metadonnees
                .Where(m => ContientTermes(m.Nom, parametres.Termes) ||
                           ContientTermes(m.Valeur, parametres.Termes))
                .Select(m => new ResultatRecherche
                {
                    Id = m.Id.ToString(),
                    Titre = m.Nom,
                    Description = m.Valeur,
                    Type = TypeResultatRecherche.Metadonnee,
                    Url = $"/metadonnees/{m.Id}",
                    Date = m.DateCreation,
                    Score = CalculerScore(m.Nom, m.Valeur, parametres.Termes)
                })
                .ToList();

            tousResultats.AddRange(resultatsMetadonnees);
            resultats.StatistiquesParType[TypeResultatRecherche.Metadonnee] = resultatsMetadonnees.Count();
        }

        // Recherche dans les règles de qualité
        if (typesRecherche.Contains(TypeResultatRecherche.RegleQualite))
        {
            var regles = await _regleQualiteService.ObtenirTousAsync();
            var resultatsRegles = regles
                .Where(r => ContientTermes(r.Nom, parametres.Termes) ||
                           ContientTermes(r.Description, parametres.Termes))
                .Select(r => new ResultatRecherche
                {
                    Id = r.Id.ToString(),
                    Titre = r.Nom,
                    Description = r.Description,
                    Type = TypeResultatRecherche.RegleQualite,
                    Url = $"/qualite-donnees/regles/modifier/{r.Id}",
                    Date = r.DateCreation,
                    Score = CalculerScore(r.Nom, r.Description, parametres.Termes)
                })
                .ToList();

            tousResultats.AddRange(resultatsRegles);
            resultats.StatistiquesParType[TypeResultatRecherche.RegleQualite] = resultatsRegles.Count();
        }

        // Recherche dans les résultats de règles de qualité
        if (typesRecherche.Contains(TypeResultatRecherche.ResultatRegleQualite))
        {
            var resultatsRegle = await _resultatRegleQualiteService.ObtenirTousAsync();
            var resultatsResultatsRegle = resultatsRegle
                .Where(r => (r.RegleQualite != null && ContientTermes(r.RegleQualite.Nom, parametres.Termes)) ||
                           ContientTermes(r.Message, parametres.Termes))
                .Select(r => new ResultatRecherche
                {
                    Id = r.Id.ToString(),
                    Titre = r.RegleQualite?.Nom ?? "Résultat de règle de qualité",
                    Description = r.Message ?? "",
                    Type = TypeResultatRecherche.ResultatRegleQualite,
                    Url = $"/resultats-regle-qualite/{r.Id}",
                    Date = r.DateExecution,
                    Score = CalculerScore(r.RegleQualite?.Nom ?? "", r.Message ?? "", parametres.Termes)
                })
                .ToList();

            tousResultats.AddRange(resultatsResultatsRegle);
            resultats.StatistiquesParType[TypeResultatRecherche.ResultatRegleQualite] = resultatsResultatsRegle.Count();
        }

        // Recherche dans les termes du glossaire
        if (typesRecherche.Contains(TypeResultatRecherche.TermeGlossaire))
        {
            var termes = await _termeGlossaireService.ObtenirTousAsync();
            var resultatsTermes = termes
                .Where(t => ContientTermes(t.Nom, parametres.Termes) ||
                           ContientTermes(t.Definition, parametres.Termes))
                .Select(t => new ResultatRecherche
                {
                    Id = t.Id.ToString(),
                    Titre = t.Nom,
                    Description = t.Definition,
                    Type = TypeResultatRecherche.TermeGlossaire,
                    Url = $"/glossaire/{t.Id}",
                    Date = t.DateCreation,
                    Score = CalculerScore(t.Nom, t.Definition, parametres.Termes)
                })
                .ToList();

            tousResultats.AddRange(resultatsTermes);
            resultats.StatistiquesParType[TypeResultatRecherche.TermeGlossaire] = resultatsTermes.Count();
        }

        // Recherche dans les politiques
        if (typesRecherche.Contains(TypeResultatRecherche.Politique))
        {
            var politiques = await _politiqueService.ObtenirTousAsync();
            var resultatsPolitiques = politiques
                .Where(p => ContientTermes(p.Titre, parametres.Termes) ||
                           ContientTermes(p.Description, parametres.Termes) ||
                           ContientTermes(p.Contenu, parametres.Termes))
                .Select(p => new ResultatRecherche
                {
                    Id = p.Id.ToString(),
                    Titre = p.Titre,
                    Description = p.Description ?? string.Empty,
                    Type = TypeResultatRecherche.Politique,
                    Url = $"/politiques/{p.Id}",
                    Date = p.DateCreation,
                    Score = CalculerScore(p.Titre, p.Description + " " + p.Contenu, parametres.Termes)
                })
                .ToList();

            tousResultats.AddRange(resultatsPolitiques);
            resultats.StatistiquesParType[TypeResultatRecherche.Politique] = resultatsPolitiques.Count();
        }

        // Recherche dans les conformités de politiques
        if (typesRecherche.Contains(TypeResultatRecherche.ConformitePolitique))
        {
            var conformites = await _conformitePolitiqueService.ObtenirTousAsync();
            var resultatsConformites = conformites
                .Where(c => ContientTermes(c.PolitiqueTitre, parametres.Termes) ||
                           ContientTermes(c.EntiteEvalueeNom, parametres.Termes))
                .Select(c => new ResultatRecherche
                {
                    Id = c.Id.ToString(),
                    Titre = c.PolitiqueTitre ?? "Conformité",
                    Description = c.EntiteEvalueeNom ?? "Entité évaluée",
                    Type = TypeResultatRecherche.ConformitePolitique,
                    Url = $"/politiques/conformite/{c.Id}",
                    Date = c.DateEvaluation,
                    Score = CalculerScore(c.PolitiqueTitre ?? "", c.EntiteEvalueeNom ?? "", parametres.Termes)
                })
                .ToList();

            tousResultats.AddRange(resultatsConformites);
            resultats.StatistiquesParType[TypeResultatRecherche.ConformitePolitique] = resultatsConformites.Count();
        }

        // Filtrer par date si nécessaire
        if (parametres.DateDebut.HasValue)
        {
            tousResultats = tousResultats.Where(r => r.Date >= parametres.DateDebut.Value).ToList();
        }

        if (parametres.DateFin.HasValue)
        {
            tousResultats = tousResultats.Where(r => r.Date <= parametres.DateFin.Value).ToList();
        }

        // Trier les résultats selon le critère spécifié
        tousResultats = parametres.TriPar switch
        {
            TriRecherche.Pertinence => tousResultats.OrderByDescending(r => r.Score).ToList(),
            TriRecherche.DateRecent => tousResultats.OrderByDescending(r => r.Date).ToList(),
            TriRecherche.DateAncien => tousResultats.OrderBy(r => r.Date).ToList(),
            TriRecherche.Alphabetique => tousResultats.OrderBy(r => r.Titre).ToList(),
            TriRecherche.AlphabetiqueInverse => tousResultats.OrderByDescending(r => r.Titre).ToList(),
            _ => tousResultats.OrderByDescending(r => r.Score).ToList()
        };

        // Appliquer la pagination
        resultats.Total = tousResultats.Count;
        resultats.Resultats = tousResultats
            .Skip(resultats.Debut)
            .Take(resultats.Limite)
            .ToList();

        // Générer des suggestions
        var suggestions = await ObtenirSuggestionsAsync(parametres.Termes);
        resultats.Suggestions = suggestions.Select(s => s.Text).ToList();

        // Ajouter à l'historique
        await AjouterHistoriqueAsync(parametres.Termes);

        stopwatch.Stop();
        resultats.TempsExecution = stopwatch.ElapsedMilliseconds;

        return resultats;
    }

    /// <inheritdoc/>
    public async Task<ResultatsRecherche> RechercheRapideAsync(string termes, int limite = 5)
    {
        var parametres = new ParametresRecherche
        {
            Termes = termes,
            NombreResultats = limite,
            Page = 1
        };

        var resultats = await RechercherAsync(parametres);

        // Ajouter le formatage des titres avec surlignage des termes
        foreach (var resultat in resultats.Resultats)
        {
            resultat.TitreFormate = HighlightTerms(resultat.Titre, termes);
        }

        return resultats;
    }

    /// <summary>
    /// Surligne les termes de recherche dans un texte.
    /// </summary>
    /// <param name="text">Texte à traiter.</param>
    /// <param name="query">Termes de recherche.</param>
    /// <returns>Texte avec les termes surlignés.</returns>
    private string HighlightTerms(string text, string query)
    {
        if (string.IsNullOrWhiteSpace(text) || string.IsNullOrWhiteSpace(query))
        {
            return text;
        }

        var terms = query.Split(' ', StringSplitOptions.RemoveEmptyEntries)
            .Where(term => term.Length > 1)
            .ToList();

        if (!terms.Any())
        {
            return text;
        }

        var result = text;

        foreach (var term in terms)
        {
            // Utiliser une expression régulière pour remplacer le terme tout en préservant la casse
            var pattern = $"({Regex.Escape(term)})";
            result = Regex.Replace(result, pattern, "<mark>$1</mark>", RegexOptions.IgnoreCase);
        }

        return result;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<SearchSuggestion>> ObtenirSuggestionsAsync(string termes, int limite = 8)
    {
        var suggestions = new List<SearchSuggestion>();

        if (string.IsNullOrWhiteSpace(termes) || termes.Length < 2)
        {
            return suggestions;
        }

        try
        {
            // Suggestions basées sur les actifs de données
            var actifs = await _actifDonneesService.ObtenirTousAsync();
            var suggestionsActifs = actifs
                .Where(a => a.Nom.Contains(termes, StringComparison.OrdinalIgnoreCase))
                .Take(3)
                .Select(a => new SearchSuggestion
                {
                    Text = a.Nom,
                    Type = TypeSuggestion.ActifDonnees,
                    Description = a.Description,
                    Count = 1,
                    Score = CalculerScoreSuggestion(a.Nom, termes),
                    Url = $"/actifs-donnees-details/{a.Id}"
                });

            suggestions.AddRange(suggestionsActifs);

            // Suggestions basées sur les termes du glossaire
            var termes_glossaire = await _termeGlossaireService.ObtenirTousAsync();
            var suggestionsGlossaire = termes_glossaire
                .Where(t => t.Nom.Contains(termes, StringComparison.OrdinalIgnoreCase) ||
                           t.Definition.Contains(termes, StringComparison.OrdinalIgnoreCase))
                .Take(2)
                .Select(t => new SearchSuggestion
                {
                    Text = t.Nom,
                    Type = TypeSuggestion.TermeGlossaire,
                    Description = t.Definition,
                    Count = 1,
                    Score = CalculerScoreSuggestion(t.Nom, termes),
                    Url = $"/glossaire/{t.Id}"
                });

            suggestions.AddRange(suggestionsGlossaire);

            // Suggestions basées sur les domaines (temporairement désactivé)
            // TODO: Implémenter le service DomaineGouvernanceService
            /*
            var domaines = await _domaineGouvernanceService.ObtenirTousAsync();
            var suggestionsDomaines = domaines
                .Where(d => d.Nom.Contains(termes, StringComparison.OrdinalIgnoreCase))
                .Take(2)
                .Select(d => new SearchSuggestion
                {
                    Text = d.Nom,
                    Type = TypeSuggestion.Domaine,
                    Description = d.Description,
                    Count = 1,
                    Score = CalculerScoreSuggestion(d.Nom, termes),
                    Url = $"/domaines/{d.Id}"
                });

            suggestions.AddRange(suggestionsDomaines);
            */

            // Suggestions basées sur l'historique
            var historique = await ObtenirHistoriqueAsync();
            var suggestionsHistorique = historique
                .Where(h => h.Contains(termes, StringComparison.OrdinalIgnoreCase) && h != termes)
                .Take(1)
                .Select(h => new SearchSuggestion
                {
                    Text = h,
                    Type = TypeSuggestion.General,
                    IsRecentSearch = true,
                    Score = CalculerScoreSuggestion(h, termes)
                });

            suggestions.AddRange(suggestionsHistorique);

            // Trier par score et limiter
            return suggestions
                .OrderByDescending(s => s.Score)
                .ThenBy(s => s.Text.Length)
                .Take(limite);
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des suggestions: {ex.Message}");
            return suggestions;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<string>> ObtenirTermesPopulairesAsync(int limite = 10)
    {
        try
        {
            // Simuler des termes populaires basés sur l'historique et les données
            var termesPopulaires = new List<string>();

            // Ajouter des termes basés sur les actifs les plus consultés
            var actifs = await _actifDonneesService.ObtenirTousAsync();
            var termesActifs = actifs
                .Take(5)
                .Select(a => a.Nom)
                .Where(nom => !string.IsNullOrEmpty(nom));

            termesPopulaires.AddRange(termesActifs);

            // Ajouter des termes de l'historique
            var historique = await ObtenirHistoriqueAsync();
            termesPopulaires.AddRange(historique.Take(5));

            return termesPopulaires
                .Distinct()
                .Take(limite);
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des termes populaires: {ex.Message}");
            return new List<string>();
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<SearchSuggestion>> ObtenirSuggestionsContextuellesAsync(string? contexte = null, int limite = 5)
    {
        var suggestions = new List<SearchSuggestion>();

        try
        {
            // Suggestions basées sur les actifs récemment créés
            var actifs = await _actifDonneesService.ObtenirTousAsync();
            var actifsRecents = actifs
                .OrderByDescending(a => a.DateCreation)
                .Take(3)
                .Select(a => new SearchSuggestion
                {
                    Text = a.Nom,
                    Type = TypeSuggestion.ActifDonnees,
                    Description = "Récemment ajouté",
                    Category = "Nouveautés",
                    Url = $"/actifs-donnees-details/{a.Id}"
                });

            suggestions.AddRange(actifsRecents);

            // Suggestions basées sur les termes du glossaire populaires
            var termes = await _termeGlossaireService.ObtenirTousAsync();
            var termesPopulaires = termes
                .Take(2)
                .Select(t => new SearchSuggestion
                {
                    Text = t.Nom,
                    Type = TypeSuggestion.TermeGlossaire,
                    Description = "Terme du glossaire",
                    Category = "Définitions",
                    Url = $"/glossaire/{t.Id}"
                });

            suggestions.AddRange(termesPopulaires);

            return suggestions.Take(limite);
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'obtention des suggestions contextuelles: {ex.Message}");
            return suggestions;
        }
    }

    /// <summary>
    /// Calcule le score de pertinence d'une suggestion.
    /// </summary>
    /// <param name="texte">Texte de la suggestion.</param>
    /// <param name="termes">Termes de recherche.</param>
    /// <returns>Score de pertinence.</returns>
    private double CalculerScoreSuggestion(string texte, string termes)
    {
        if (string.IsNullOrEmpty(texte) || string.IsNullOrEmpty(termes))
            return 0;

        var score = 0.0;

        // Correspondance exacte au début
        if (texte.StartsWith(termes, StringComparison.OrdinalIgnoreCase))
            score += 10.0;

        // Correspondance exacte n'importe où
        if (texte.Contains(termes, StringComparison.OrdinalIgnoreCase))
            score += 5.0;

        // Bonus pour les textes plus courts (plus spécifiques)
        score += Math.Max(0, 50 - texte.Length) / 10.0;

        return score;
    }

    /// <inheritdoc/>
    public Task<List<string>> ObtenirHistoriqueAsync(int limite = 10)
    {
        return Task.FromResult(_historiqueRecherche.Take(limite).ToList());
    }

    /// <inheritdoc/>
    public Task AjouterHistoriqueAsync(string termes)
    {
        if (!string.IsNullOrWhiteSpace(termes))
        {
            // Supprimer si existe déjà
            _historiqueRecherche.Remove(termes);

            // Ajouter au début
            _historiqueRecherche.Insert(0, termes);

            // Limiter la taille
            while (_historiqueRecherche.Count > 20)
            {
                _historiqueRecherche.RemoveAt(_historiqueRecherche.Count - 1);
            }
        }

        return Task.CompletedTask;
    }

    /// <inheritdoc/>
    public Task EffacerHistoriqueAsync()
    {
        _historiqueRecherche.Clear();
        return Task.CompletedTask;
    }

    /// <summary>
    /// Vérifie si le texte contient les termes de recherche.
    /// </summary>
    /// <param name="texte">Texte à vérifier.</param>
    /// <param name="termes">Termes de recherche.</param>
    /// <returns>True si le texte contient les termes de recherche, sinon false.</returns>
    private bool ContientTermes(string? texte, string termes)
    {
        if (string.IsNullOrWhiteSpace(texte))
        {
            return false;
        }

        var termesArray = termes.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        return termesArray.All(t => texte.Contains(t, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Calcule le score de pertinence d'un résultat.
    /// </summary>
    /// <param name="titre">Titre du résultat.</param>
    /// <param name="contenu">Contenu du résultat.</param>
    /// <param name="termes">Termes de recherche.</param>
    /// <returns>Le score de pertinence.</returns>
    private double CalculerScore(string titre, string contenu, string termes)
    {
        var score = 0.0;
        var termesArray = termes.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        // Le titre a plus de poids que le contenu
        foreach (var terme in termesArray)
        {
            if (titre.Contains(terme, StringComparison.OrdinalIgnoreCase))
            {
                score += 2.0;
            }

            if (contenu.Contains(terme, StringComparison.OrdinalIgnoreCase))
            {
                score += 1.0;
            }
        }

        return score;
    }
}

