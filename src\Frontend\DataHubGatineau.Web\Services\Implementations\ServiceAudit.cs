using DataHubGatineau.Web.Models.Auditoria;
using DataHubGatineau.Web.Services.Interfaces;
using Microsoft.AspNetCore.Components.Authorization;
using System.Text.Json;
using System.Security.Claims;
using System.Diagnostics;

namespace DataHubGatineau.Web.Services.Implementations
{
    /// <summary>
    /// Service d'audit pour le système de gouvernance des données
    /// Inspiré des meilleures pratiques de Collibra et Microsoft Purview
    /// </summary>
    public class ServiceAudit : IServiceAudit
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ServiceAudit> _logger;
        private readonly AuthenticationStateProvider _authStateProvider;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly string _correlationId;

        public ServiceAudit(
            HttpClient httpClient,
            ILogger<ServiceAudit> logger,
            AuthenticationStateProvider authStateProvider,
            IHttpContextAccessor httpContextAccessor)
        {
            _httpClient = httpClient;
            _logger = logger;
            _authStateProvider = authStateProvider;
            _httpContextAccessor = httpContextAccessor;
            _correlationId = Guid.NewGuid().ToString();
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true,
                WriteIndented = false
            };
        }

        public async Task EnregistrerAuditAsync(EntreeAudit entree)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                // Compléter les informations de contexte
                await CompleterInformationsContexte(entree);
                
                // Ajouter la durée d'exécution
                entree.DureeExecution = stopwatch.Elapsed;

                // Envoyer à l'API de manière asynchrone pour ne pas bloquer l'opération principale
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var response = await _httpClient.PostAsJsonAsync("api/audit", entree, _jsonOptions);
                        if (!response.IsSuccessStatusCode)
                        {
                            _logger.LogWarning("Échec de l'enregistrement de l'audit: {StatusCode} - {Action}", 
                                response.StatusCode, entree.Action);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Erreur lors de l'envoi de l'audit à l'API: {Action}", entree.Action);
                    }
                });

                // Log local pour traçabilité immédiate
                _logger.LogInformation("Audit enregistré: {Action} par {Utilisateur} sur {Entite}", 
                    entree.Action, entree.NomUtilisateur, entree.TypeEntite);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'enregistrement de l'audit: {Action}", entree.Action);
                // Ne pas propager l'erreur pour éviter d'interrompre l'opération principale
            }
            finally
            {
                stopwatch.Stop();
            }
        }

        public async Task EnregistrerActionAsync(
            string action,
            string typeEntite,
            Guid? entiteId = null,
            string? nomEntite = null,
            TypeActionAudit typeAction = TypeActionAudit.Lecture,
            NiveauGraviteAudit niveauGravite = NiveauGraviteAudit.Information,
            CategorieAudit categorie = CategorieAudit.GouvernanceDonnees,
            string? valeursAvant = null,
            string? valeursApres = null,
            string? commentaire = null,
            string? contexte = null,
            string? domaineAffaires = null,
            ClassificationSensibilite? classification = null,
            bool requiertConformite = false,
            string? reglementations = null)
        {
            var entreeAudit = new EntreeAudit
            {
                Action = action,
                TypeEntite = typeEntite,
                EntiteId = entiteId,
                NomEntite = nomEntite,
                TypeAction = typeAction,
                NiveauGravite = niveauGravite,
                Categorie = categorie,
                ValeursAvant = valeursAvant,
                ValeursApres = valeursApres,
                Commentaire = commentaire,
                Contexte = contexte,
                DomaineAffaires = domaineAffaires,
                ClassificationSensibilite = classification,
                RequiertConformite = requiertConformite,
                ReglementationsApplicables = reglementations,
                CorrelationId = _correlationId,
                DateAction = DateTime.UtcNow
            };

            await EnregistrerAuditAsync(entreeAudit);
        }

        public async Task EnregistrerConnexionAsync(string utilisateurId, string nomUtilisateur, bool succes = true, string? messageErreur = null)
        {
            await EnregistrerActionAsync(
                succes ? "Connexion utilisateur réussie" : "Échec de connexion utilisateur",
                "Utilisateur",
                Guid.TryParse(utilisateurId, out var id) ? id : null,
                nomUtilisateur,
                succes ? TypeActionAudit.Connexion : TypeActionAudit.EchecConnexion,
                succes ? NiveauGraviteAudit.Information : NiveauGraviteAudit.Avertissement,
                CategorieAudit.Securite,
                commentaire: succes ? "Connexion réussie" : $"Échec de connexion: {messageErreur}",
                contexte: "Authentification",
                requiertConformite: true,
                reglementations: "GDPR, Politique de sécurité interne");
        }

        public async Task EnregistrerDeconnexionAsync(string utilisateurId, string nomUtilisateur)
        {
            await EnregistrerActionAsync(
                "Déconnexion utilisateur",
                "Utilisateur",
                Guid.TryParse(utilisateurId, out var id) ? id : null,
                nomUtilisateur,
                TypeActionAudit.Deconnexion,
                NiveauGraviteAudit.Information,
                CategorieAudit.Securite,
                commentaire: "Déconnexion utilisateur",
                contexte: "Authentification");
        }

        public async Task EnregistrerCreationAsync(string typeEntite, Guid entiteId, string? nomEntite = null, 
            object? valeurs = null, string? commentaire = null, CategorieAudit categorie = CategorieAudit.GouvernanceDonnees, 
            ClassificationSensibilite? classification = null)
        {
            await EnregistrerActionAsync(
                $"Création {typeEntite}",
                typeEntite,
                entiteId,
                nomEntite,
                TypeActionAudit.Creation,
                NiveauGraviteAudit.Information,
                categorie,
                valeursApres: valeurs != null ? JsonSerializer.Serialize(valeurs, _jsonOptions) : null,
                commentaire: commentaire,
                classification: classification,
                requiertConformite: classification >= ClassificationSensibilite.Confidentiel);
        }

        public async Task EnregistrerModificationAsync(string typeEntite, Guid entiteId, string? nomEntite = null, 
            object? valeursAvant = null, object? valeursApres = null, string? commentaire = null, 
            CategorieAudit categorie = CategorieAudit.GouvernanceDonnees, ClassificationSensibilite? classification = null)
        {
            await EnregistrerActionAsync(
                $"Modification {typeEntite}",
                typeEntite,
                entiteId,
                nomEntite,
                TypeActionAudit.Modification,
                NiveauGraviteAudit.Information,
                categorie,
                valeursAvant: valeursAvant != null ? JsonSerializer.Serialize(valeursAvant, _jsonOptions) : null,
                valeursApres: valeursApres != null ? JsonSerializer.Serialize(valeursApres, _jsonOptions) : null,
                commentaire: commentaire,
                classification: classification,
                requiertConformite: classification >= ClassificationSensibilite.Confidentiel);
        }

        public async Task EnregistrerSuppressionAsync(string typeEntite, Guid entiteId, string? nomEntite = null, 
            object? valeurs = null, string? commentaire = null, CategorieAudit categorie = CategorieAudit.GouvernanceDonnees, 
            ClassificationSensibilite? classification = null)
        {
            await EnregistrerActionAsync(
                $"Suppression {typeEntite}",
                typeEntite,
                entiteId,
                nomEntite,
                TypeActionAudit.Suppression,
                NiveauGraviteAudit.Avertissement,
                categorie,
                valeursAvant: valeurs != null ? JsonSerializer.Serialize(valeurs, _jsonOptions) : null,
                commentaire: commentaire,
                classification: classification,
                requiertConformite: true,
                reglementations: "GDPR, Politique de rétention des données");
        }

        public async Task EnregistrerEvenementSecuriteAsync(string action, string details, NiveauGraviteAudit gravite = NiveauGraviteAudit.Securite)
        {
            await EnregistrerActionAsync(
                action,
                "Sécurité",
                typeAction: TypeActionAudit.DetectionAnomalie,
                niveauGravite: gravite,
                categorie: CategorieAudit.Securite,
                commentaire: details,
                contexte: "Sécurité",
                requiertConformite: true,
                reglementations: "Politique de sécurité, SOX, GDPR");
        }

        public async Task EnregistrerEvenementConformiteAsync(string action, string typeEntite, Guid? entiteId = null, 
            string? reglementations = null, bool conforme = true, string? details = null)
        {
            await EnregistrerActionAsync(
                action,
                typeEntite,
                entiteId,
                typeAction: conforme ? TypeActionAudit.EvaluationConformite : TypeActionAudit.ViolationPolitique,
                niveauGravite: conforme ? NiveauGraviteAudit.Conformite : NiveauGraviteAudit.Critique,
                categorie: CategorieAudit.ConformiteReglementaire,
                commentaire: details,
                contexte: "Conformité",
                requiertConformite: true,
                reglementations: reglementations);
        }

        // Méthodes de lecture - implémentation basique pour commencer
        public async Task<ResultatPagineAudit> ObtenirAuditsAsync(FiltresAudit filtres)
        {
            try
            {
                var queryParams = ConstruireParametresRequete(filtres);
                var response = await _httpClient.GetAsync($"api/audit?{queryParams}");
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ResultatPagineAudit>(json, _jsonOptions) ?? new ResultatPagineAudit();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des audits");
                return new ResultatPagineAudit();
            }
        }

        public async Task<List<EntreeAudit>> ObtenirHistoriqueEntiteAsync(string typeEntite, Guid entiteId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"api/audit/entite/{Uri.EscapeDataString(typeEntite)}/{entiteId}");
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<EntreeAudit>>(json, _jsonOptions) ?? new List<EntreeAudit>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de l'historique de l'entité");
                return new List<EntreeAudit>();
            }
        }

        public async Task<ResultatAuditUtilisateur> ObtenirAuditsUtilisateurAsync(string utilisateurId, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            // Implémentation basique - à étendre
            return new ResultatAuditUtilisateur { UtilisateurId = utilisateurId };
        }

        public async Task<StatistiquesAuditAvancees> ObtenirStatistiquesAvanceesAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            // Implémentation basique - à étendre
            return new StatistiquesAuditAvancees();
        }

        public async Task<List<EntreeAudit>> ObtenirEvenementsSecuriteAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            // Implémentation basique - à étendre
            return new List<EntreeAudit>();
        }

        public async Task<RapportConformiteAudit> ObtenirRapportConformiteAsync(string? reglementation = null, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            // Implémentation basique - à étendre
            return new RapportConformiteAudit();
        }

        public async Task<byte[]> ExporterAuditsAsync(FiltresAudit filtres, FormatExport format = FormatExport.Excel)
        {
            // Implémentation basique - à étendre
            return Array.Empty<byte>();
        }

        private async Task CompleterInformationsContexte(EntreeAudit entree)
        {
            try
            {
                // Obtenir les informations de l'utilisateur connecté
                var authState = await _authStateProvider.GetAuthenticationStateAsync();
                if (authState.User.Identity?.IsAuthenticated == true)
                {
                    entree.UtilisateurId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "Anonyme";
                    entree.NomUtilisateur = authState.User.FindFirst(ClaimTypes.Name)?.Value ?? "Utilisateur inconnu";
                    entree.NomCompletUtilisateur = authState.User.FindFirst(ClaimTypes.GivenName)?.Value + " " + 
                                                  authState.User.FindFirst(ClaimTypes.Surname)?.Value;
                }
                else
                {
                    entree.UtilisateurId = "Anonyme";
                    entree.NomUtilisateur = "Utilisateur non authentifié";
                }

                // Obtenir les informations de contexte HTTP
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext != null)
                {
                    entree.AdresseIP = httpContext.Connection.RemoteIpAddress?.ToString();
                    entree.UserAgent = httpContext.Request.Headers["User-Agent"].FirstOrDefault();
                    entree.SessionId = httpContext.Session?.Id;
                }

                // Ajouter des métadonnées système
                var metadonnees = new
                {
                    Version = "1.0",
                    Environnement = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                    MachineName = Environment.MachineName,
                    ProcessId = Environment.ProcessId,
                    ThreadId = Environment.CurrentManagedThreadId
                };
                entree.Metadonnees = JsonSerializer.Serialize(metadonnees, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erreur lors de la complétion des informations de contexte pour l'audit");
            }
        }

        private string ConstruireParametresRequete(FiltresAudit filtres)
        {
            var parametres = new List<string>
            {
                $"page={filtres.Page}",
                $"taille={filtres.Taille}"
            };

            if (!string.IsNullOrEmpty(filtres.TexteRecherche))
                parametres.Add($"recherche={Uri.EscapeDataString(filtres.TexteRecherche)}");

            if (filtres.DateDebut.HasValue)
                parametres.Add($"dateDebut={filtres.DateDebut.Value:yyyy-MM-dd}");

            if (filtres.DateFin.HasValue)
                parametres.Add($"dateFin={filtres.DateFin.Value:yyyy-MM-dd}");

            return string.Join("&", parametres);
        }
    }
}
