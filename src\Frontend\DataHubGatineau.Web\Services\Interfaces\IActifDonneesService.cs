using DataHubGatineau.Web.Models;
using DataHubGatineau.Web.Models.Policy;

namespace DataHubGatineau.Web.Services.Interfaces;

/// <summary>
/// Interface pour le service des actifs de données.
/// </summary>
public interface IActifDonneesService : IApiService<ActifDonnees>
{
    /// <summary>
    /// Obtient les actifs de données par type.
    /// </summary>
    /// <param name="type">Type d'actif de données.</param>
    /// <returns>Une collection d'actifs de données du type spécifié.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParTypeAsync(string type);

    /// <summary>
    /// Obtient les actifs de données par classification de sensibilité.
    /// </summary>
    /// <param name="classification">Classification de sensibilité.</param>
    /// <returns>Une collection d'actifs de données avec la classification spécifiée.</returns>
    Task<IEnumerable<ActifDonnees>> ObtenirParClassificationAsync(ClassificationSensibilite classification);

    /// <summary>
    /// Recherche des actifs de données par nom.
    /// </summary>
    /// <param name="terme">Terme de recherche.</param>
    /// <returns>Une collection d'actifs de données dont le nom contient le terme de recherche.</returns>
    Task<IEnumerable<ActifDonnees>> RechercherParNomAsync(string terme);

    /// <summary>
    /// Obtient tous les statuts d'actifs de données.
    /// </summary>
    /// <returns>Une collection de statuts d'actifs de données.</returns>
    Task<IEnumerable<StatutActifDonnees>> ObtenirStatutsAsync();

    /// <summary>
    /// Obtient tous les types d'actifs de données.
    /// </summary>
    /// <returns>Une collection de types d'actifs de données.</returns>
    Task<IEnumerable<TypeActifDonneesItem>> ObtenirTypesAsync();

    /// <summary>
    /// Obtient tous les formats d'actifs de données.
    /// </summary>
    /// <returns>Une collection de formats d'actifs de données.</returns>
    Task<IEnumerable<FormatActifDonneesItem>> ObtenirFormatsAsync();

    /// <summary>
    /// Obtient toutes les sources d'actifs de données.
    /// </summary>
    /// <returns>Une collection de sources d'actifs de données.</returns>
    Task<IEnumerable<SourceActifDonneesItem>> ObtenirSourcesAsync();

    /// <summary>
    /// Obtient toutes les fréquences de mise à jour.
    /// </summary>
    /// <returns>Une collection de fréquences de mise à jour.</returns>
    Task<IEnumerable<FrequenceMiseAJour>> ObtenirFrequencesAsync();

    /// <summary>
    /// Obtient tous les types de métadonnées.
    /// </summary>
    /// <returns>Une collection de types de métadonnées.</returns>
    Task<IEnumerable<TypeMetadonneeItem>> ObtenirTypesMetadonneesAsync();

    /// <summary>
    /// Obtient toutes les catégories de métadonnées.
    /// </summary>
    /// <returns>Une collection de catégories de métadonnées.</returns>
    Task<IEnumerable<CategorieMetadonneeItem>> ObtenirCategoriesMetadonneesAsync();

    /// <summary>
    /// Obtient les politiques associées à un actif de données.
    /// </summary>
    /// <param name="actifId">Identifiant de l'actif de données.</param>
    /// <returns>Une collection de politiques associées à l'actif.</returns>
    Task<IEnumerable<Politique>> ObtenirPolitiquesAssocieesAsync(Guid actifId);
}
