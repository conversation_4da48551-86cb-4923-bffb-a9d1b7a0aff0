using DataHubGatineau.Web.Models.RiskManagement;

namespace DataHubGatineau.Web.Services.Interfaces;

/// <summary>
/// Interface pour le service de gestion des évaluations de risque.
/// </summary>
public interface IEvaluationRisqueService : IServiceBaseGuid<EvaluationRisque>
{
    /// <summary>
    /// Obtient toutes les évaluations pour un actif de données spécifique.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <returns>Une collection d'évaluations pour l'actif.</returns>
    Task<IEnumerable<EvaluationRisque>> ObtenirParActifDonneesAsync(Guid actifDonneesId);

    /// <summary>
    /// Obtient toutes les évaluations par type.
    /// </summary>
    /// <param name="typeEvaluation">Le type d'évaluation à filtrer.</param>
    /// <returns>Une collection d'évaluations du type spécifié.</returns>
    Task<IEnumerable<EvaluationRisque>> ObtenirParTypeAsync(TypeEvaluation typeEvaluation);

    /// <summary>
    /// Obtient toutes les évaluations par statut.
    /// </summary>
    /// <param name="statut">Le statut à filtrer.</param>
    /// <returns>Une collection d'évaluations avec le statut spécifié.</returns>
    Task<IEnumerable<EvaluationRisque>> ObtenirParStatutAsync(StatutEvaluation statut);

    /// <summary>
    /// Obtient toutes les évaluations assignées à un évaluateur spécifique.
    /// </summary>
    /// <param name="evaluateurId">L'identifiant de l'évaluateur.</param>
    /// <returns>Une collection d'évaluations assignées à l'évaluateur.</returns>
    Task<IEnumerable<EvaluationRisque>> ObtenirParEvaluateurAsync(string evaluateurId);

    /// <summary>
    /// Obtient toutes les évaluations en retard (date fin prévue dépassée).
    /// </summary>
    /// <returns>Une collection d'évaluations en retard.</returns>
    Task<IEnumerable<EvaluationRisque>> ObtenirEvaluationsEnRetardAsync();

    /// <summary>
    /// Obtient toutes les évaluations planifiées pour une période donnée.
    /// </summary>
    /// <param name="dateDebut">Date de début de la période.</param>
    /// <param name="dateFin">Date de fin de la période.</param>
    /// <returns>Une collection d'évaluations planifiées dans la période.</returns>
    Task<IEnumerable<EvaluationRisque>> ObtenirEvaluationsPlanifieesAsync(DateTime dateDebut, DateTime dateFin);

    /// <summary>
    /// Démarre une nouvelle évaluation pour un actif de données.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <param name="typeEvaluation">Le type d'évaluation.</param>
    /// <param name="evaluateurId">L'identifiant de l'évaluateur.</param>
    /// <param name="evaluateurNom">Le nom de l'évaluateur.</param>
    /// <returns>L'évaluation créée.</returns>
    Task<EvaluationRisque> DemarrerEvaluationAsync(Guid actifDonneesId, TypeEvaluation typeEvaluation, string evaluateurId, string evaluateurNom);

    /// <summary>
    /// Termine une évaluation et calcule le score global.
    /// </summary>
    /// <param name="evaluationId">L'identifiant de l'évaluation.</param>
    /// <param name="recommandations">Les recommandations de l'évaluation.</param>
    /// <param name="actionsCorrectives">Les actions correctives proposées.</param>
    /// <returns>L'évaluation terminée.</returns>
    Task<EvaluationRisque> TerminerEvaluationAsync(Guid evaluationId, string? recommandations = null, string? actionsCorrectives = null);

    /// <summary>
    /// Approuve une évaluation.
    /// </summary>
    /// <param name="evaluationId">L'identifiant de l'évaluation.</param>
    /// <param name="approvateurId">L'identifiant de l'approbateur.</param>
    /// <param name="commentaire">Commentaire d'approbation optionnel.</param>
    /// <returns>L'évaluation approuvée.</returns>
    Task<EvaluationRisque> ApprouverEvaluationAsync(Guid evaluationId, string approvateurId, string? commentaire = null);

    /// <summary>
    /// Rejette une évaluation.
    /// </summary>
    /// <param name="evaluationId">L'identifiant de l'évaluation.</param>
    /// <param name="raisonRejet">La raison du rejet.</param>
    /// <returns>L'évaluation rejetée.</returns>
    Task<EvaluationRisque> RejeterEvaluationAsync(Guid evaluationId, string raisonRejet);

    /// <summary>
    /// Planifie la prochaine évaluation pour un actif de données.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <param name="frequence">La fréquence d'évaluation.</param>
    /// <returns>L'évaluation planifiée.</returns>
    Task<EvaluationRisque> PlanifierProchaineEvaluationAsync(Guid actifDonneesId, FrequenceEvaluation frequence);

    /// <summary>
    /// Obtient les statistiques d'évaluations par type.
    /// </summary>
    /// <returns>Un dictionnaire avec les statistiques par type d'évaluation.</returns>
    Task<Dictionary<TypeEvaluation, int>> ObtenirStatistiquesParTypeAsync();

    /// <summary>
    /// Obtient les statistiques d'évaluations par statut.
    /// </summary>
    /// <returns>Un dictionnaire avec les statistiques par statut.</returns>
    Task<Dictionary<StatutEvaluation, int>> ObtenirStatistiquesParStatutAsync();

    /// <summary>
    /// Obtient le tableau de bord des évaluations pour un évaluateur.
    /// </summary>
    /// <param name="evaluateurId">L'identifiant de l'évaluateur.</param>
    /// <returns>Le tableau de bord des évaluations.</returns>
    Task<TableauBordEvaluations> ObtenirTableauBordAsync(string evaluateurId);

    /// <summary>
    /// Génère un rapport d'évaluation.
    /// </summary>
    /// <param name="evaluationId">L'identifiant de l'évaluation.</param>
    /// <returns>Le rapport d'évaluation.</returns>
    Task<RapportEvaluation> GenererRapportEvaluationAsync(Guid evaluationId);

    /// <summary>
    /// Recherche des évaluations par mots-clés.
    /// </summary>
    /// <param name="motsCles">Les mots-clés de recherche.</param>
    /// <returns>Une collection d'évaluations correspondant aux critères de recherche.</returns>
    Task<IEnumerable<EvaluationRisque>> RechercherAsync(string motsCles);

    /// <summary>
    /// Duplique une évaluation existante pour un nouvel actif.
    /// </summary>
    /// <param name="evaluationSourceId">L'identifiant de l'évaluation source.</param>
    /// <param name="nouvelActifDonneesId">L'identifiant du nouvel actif de données.</param>
    /// <returns>La nouvelle évaluation créée.</returns>
    Task<EvaluationRisque> DupliquerEvaluationAsync(Guid evaluationSourceId, Guid nouvelActifDonneesId);
}

/// <summary>
/// Représente le tableau de bord des évaluations pour un évaluateur.
/// </summary>
public class TableauBordEvaluations
{
    public string EvaluateurId { get; set; } = string.Empty;
    public string EvaluateurNom { get; set; } = string.Empty;
    public int NombreEvaluationsEnCours { get; set; }
    public int NombreEvaluationsEnRetard { get; set; }
    public int NombreEvaluationsTerminees { get; set; }
    public int NombreEvaluationsPlanifiees { get; set; }
    public List<EvaluationRisque> EvaluationsPrioritaires { get; set; } = new List<EvaluationRisque>();
    public List<EvaluationRisque> ProchainesEcheances { get; set; } = new List<EvaluationRisque>();
    public DateTime DerniereMiseAJour { get; set; } = DateTime.Now;
}

/// <summary>
/// Représente un rapport d'évaluation.
/// </summary>
public class RapportEvaluation
{
    public Guid EvaluationId { get; set; }
    public string TitreEvaluation { get; set; } = string.Empty;
    public string NomActif { get; set; } = string.Empty;
    public TypeEvaluation TypeEvaluation { get; set; }
    public StatutEvaluation Statut { get; set; }
    public string EvaluateurNom { get; set; } = string.Empty;
    public DateTime DateDebut { get; set; }
    public DateTime? DateFin { get; set; }
    public int ScoreGlobalRisque { get; set; }
    public NiveauRisque NiveauRisqueGlobal { get; set; }
    public List<RisqueActifDonnees> RisquesIdentifies { get; set; } = new List<RisqueActifDonnees>();
    public string? Recommandations { get; set; }
    public string? ActionsCorrectives { get; set; }
    public List<CritereEvaluation> CriteresEvaluation { get; set; } = new List<CritereEvaluation>();
    public DateTime DateGeneration { get; set; } = DateTime.Now;
    public string GenerePar { get; set; } = string.Empty;
}
