using DataHubGatineau.Web.Models.RiskManagement;

namespace DataHubGatineau.Web.Services.Interfaces;

/// <summary>
/// Interface pour le service de gestion des politiques de sécurité.
/// </summary>
public interface IPolitiqueSecuriteService : IServiceBaseGuid<PolitiqueSecurite>
{
    /// <summary>
    /// Obtient toutes les politiques par type.
    /// </summary>
    /// <param name="typePolitique">Le type de politique à filtrer.</param>
    /// <returns>Une collection de politiques du type spécifié.</returns>
    Task<IEnumerable<PolitiqueSecurite>> ObtenirParTypeAsync(TypePolitique typePolitique);

    /// <summary>
    /// Obtient toutes les politiques actives.
    /// </summary>
    /// <returns>Une collection de politiques actives.</returns>
    Task<IEnumerable<PolitiqueSecurite>> ObtenirPolitiquesActivesAsync();

    /// <summary>
    /// Obtient toutes les politiques expirées ou sur le point d'expirer.
    /// </summary>
    /// <param name="joursAvantExpiration">Nombre de jours avant expiration pour considérer une politique comme "sur le point d'expirer".</param>
    /// <returns>Une collection de politiques expirées ou sur le point d'expirer.</returns>
    Task<IEnumerable<PolitiqueSecurite>> ObtenirPolitiquesExpirantAsync(int joursAvantExpiration = 30);

    /// <summary>
    /// Obtient toutes les politiques par propriétaire.
    /// </summary>
    /// <param name="proprietaireId">L'identifiant du propriétaire.</param>
    /// <returns>Une collection de politiques appartenant au propriétaire.</returns>
    Task<IEnumerable<PolitiqueSecurite>> ObtenirParProprietaireAsync(string proprietaireId);

    /// <summary>
    /// Obtient toutes les politiques applicables à une classification de données.
    /// </summary>
    /// <param name="classification">La classification de sensibilité.</param>
    /// <returns>Une collection de politiques applicables à la classification.</returns>
    Task<IEnumerable<PolitiqueSecurite>> ObtenirParClassificationAsync(string classification);

    /// <summary>
    /// Active une politique.
    /// </summary>
    /// <param name="politiqueId">L'identifiant de la politique.</param>
    /// <param name="dateEntreeVigueur">Date d'entrée en vigueur optionnelle.</param>
    /// <returns>La politique activée.</returns>
    Task<PolitiqueSecurite> ActiverPolitiqueAsync(Guid politiqueId, DateTime? dateEntreeVigueur = null);

    /// <summary>
    /// Désactive une politique.
    /// </summary>
    /// <param name="politiqueId">L'identifiant de la politique.</param>
    /// <param name="raisonDesactivation">Raison de la désactivation.</param>
    /// <returns>La politique désactivée.</returns>
    Task<PolitiqueSecurite> DesactiverPolitiqueAsync(Guid politiqueId, string raisonDesactivation);

    /// <summary>
    /// Approuve une politique.
    /// </summary>
    /// <param name="politiqueId">L'identifiant de la politique.</param>
    /// <param name="approvateurId">L'identifiant de l'approbateur.</param>
    /// <param name="approvateurNom">Le nom de l'approbateur.</param>
    /// <returns>La politique approuvée.</returns>
    Task<PolitiqueSecurite> ApprouverPolitiqueAsync(Guid politiqueId, string approvateurId, string approvateurNom);

    /// <summary>
    /// Crée une nouvelle version d'une politique existante.
    /// </summary>
    /// <param name="politiqueId">L'identifiant de la politique source.</param>
    /// <param name="nouvelleVersion">Le numéro de la nouvelle version.</param>
    /// <returns>La nouvelle version de la politique.</returns>
    Task<PolitiqueSecurite> CreerNouvelleVersionAsync(Guid politiqueId, string nouvelleVersion);

    /// <summary>
    /// Vérifie la conformité d'un actif de données aux politiques applicables.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <returns>Le résultat de la vérification de conformité.</returns>
    Task<ResultatConformite> VerifierConformiteAsync(Guid actifDonneesId);

    /// <summary>
    /// Applique automatiquement les politiques à un actif de données.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <returns>La liste des politiques appliquées.</returns>
    Task<IEnumerable<PolitiqueSecurite>> AppliquerPolitiquesAutomatiquementAsync(Guid actifDonneesId);

    /// <summary>
    /// Obtient les statistiques de conformité par type de politique.
    /// </summary>
    /// <returns>Un dictionnaire avec les statistiques de conformité par type.</returns>
    Task<Dictionary<TypePolitique, StatistiquesConformite>> ObtenirStatistiquesConformiteAsync();

    /// <summary>
    /// Génère un rapport de conformité pour un actif de données.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <returns>Le rapport de conformité.</returns>
    Task<RapportConformite> GenererRapportConformiteAsync(Guid actifDonneesId);

    /// <summary>
    /// Recherche des politiques par mots-clés.
    /// </summary>
    /// <param name="motsCles">Les mots-clés de recherche.</param>
    /// <returns>Une collection de politiques correspondant aux critères de recherche.</returns>
    Task<IEnumerable<PolitiqueSecurite>> RechercherAsync(string motsCles);

    /// <summary>
    /// Obtient l'historique des versions d'une politique.
    /// </summary>
    /// <param name="politiqueId">L'identifiant de la politique.</param>
    /// <returns>L'historique des versions de la politique.</returns>
    Task<IEnumerable<PolitiqueSecurite>> ObtenirHistoriqueVersionsAsync(Guid politiqueId);

    /// <summary>
    /// Valide une politique avant sa création ou modification.
    /// </summary>
    /// <param name="politique">La politique à valider.</param>
    /// <returns>Le résultat de la validation.</returns>
    Task<ResultatValidationPolitique> ValiderPolitiqueAsync(PolitiqueSecurite politique);
}

/// <summary>
/// Représente le résultat d'une vérification de conformité.
/// </summary>
public class ResultatConformite
{
    public Guid ActifDonneesId { get; set; }
    public string NomActif { get; set; } = string.Empty;
    public bool EstConforme { get; set; }
    public int NombrePolitiquesApplicables { get; set; }
    public int NombrePolitiquesConformes { get; set; }
    public List<ViolationConformitePolitique> Violations { get; set; } = new List<ViolationConformitePolitique>();
    public List<PolitiqueSecurite> PolitiquesApplicables { get; set; } = new List<PolitiqueSecurite>();
    public DateTime DateVerification { get; set; } = DateTime.Now;
    public string VerifiePar { get; set; } = string.Empty;
}

/// <summary>
/// Représente une violation de conformité pour les politiques.
/// </summary>
public class ViolationConformitePolitique
{
    public Guid PolitiqueId { get; set; }
    public string NomPolitique { get; set; } = string.Empty;
    public TypePolitique TypePolitique { get; set; }
    public string Description { get; set; } = string.Empty;
    public NiveauGravite NiveauGravite { get; set; }
    public string ActionRecommandee { get; set; } = string.Empty;
}

/// <summary>
/// Énumération des niveaux de gravité.
/// </summary>
public enum NiveauGravite
{
    Faible = 1,
    Moyen = 2,
    Eleve = 3,
    Critique = 4
}

/// <summary>
/// Représente les statistiques de conformité.
/// </summary>
public class StatistiquesConformite
{
    public int NombreActifsTotal { get; set; }
    public int NombreActifsConformes { get; set; }
    public int NombreActifsNonConformes { get; set; }
    public double PourcentageConformite => NombreActifsTotal > 0 ? (double)NombreActifsConformes / NombreActifsTotal * 100 : 0;
    public int NombreViolationsCritiques { get; set; }
    public int NombreViolationsElevees { get; set; }
    public int NombreViolationsMoyennes { get; set; }
    public int NombreViolationsFaibles { get; set; }
}

/// <summary>
/// Représente un rapport de conformité.
/// </summary>
public class RapportConformite
{
    public Guid ActifDonneesId { get; set; }
    public string NomActif { get; set; } = string.Empty;
    public ResultatConformite ResultatConformite { get; set; } = new ResultatConformite();
    public List<RecommandationConformite> Recommandations { get; set; } = new List<RecommandationConformite>();
    public DateTime DateGeneration { get; set; } = DateTime.Now;
    public string GenerePar { get; set; } = string.Empty;
}

/// <summary>
/// Représente une recommandation de conformité.
/// </summary>
public class RecommandationConformite
{
    public string Titre { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public NiveauPriorite Priorite { get; set; }
    public string ActionRecommandee { get; set; } = string.Empty;
    public DateTime? DateLimite { get; set; }
}

/// <summary>
/// Énumération des niveaux de priorité.
/// </summary>
public enum NiveauPriorite
{
    Faible = 1,
    Moyenne = 2,
    Elevee = 3,
    Critique = 4
}

/// <summary>
/// Représente le résultat d'une validation de politique.
/// </summary>
public class ResultatValidationPolitique
{
    public bool EstValide { get; set; }
    public List<string> Erreurs { get; set; } = new List<string>();
    public List<string> Avertissements { get; set; } = new List<string>();
    public List<string> Suggestions { get; set; } = new List<string>();
}
