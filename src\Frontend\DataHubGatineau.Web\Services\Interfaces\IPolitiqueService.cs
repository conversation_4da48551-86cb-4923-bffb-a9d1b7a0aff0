using DataHubGatineau.Web.Models.Policy;

namespace DataHubGatineau.Web.Services.Interfaces;

/// <summary>
/// Interface pour le service des politiques.
/// </summary>
public interface IPolitiqueService : IApiService<Politique>
{
    /// <summary>
    /// Obtient les politiques par type.
    /// </summary>
    /// <param name="type">Type de politique.</param>
    /// <returns>Une collection de politiques du type spécifié.</returns>
    Task<IEnumerable<Politique>> ObtenirParTypeAsync(TypePolitique type);

    /// <summary>
    /// Obtient les politiques par statut.
    /// </summary>
    /// <param name="statut">Statut de la politique.</param>
    /// <returns>Une collection de politiques avec le statut spécifié.</returns>
    Task<IEnumerable<Politique>> ObtenirParStatutAsync(StatutPolitique statut);

    /// <summary>
    /// Obtient les politiques par niveau d'application.
    /// </summary>
    /// <param name="niveau">Niveau d'application.</param>
    /// <returns>Une collection de politiques avec le niveau d'application spécifié.</returns>
    Task<IEnumerable<Politique>> ObtenirParNiveauApplicationAsync(NiveauApplicationPolitique niveau);

    /// <summary>
    /// Obtient les politiques applicables à une entité spécifique.
    /// </summary>
    /// <param name="entiteId">Identifiant de l'entité.</param>
    /// <param name="typeEntite">Type de l'entité (département, projet, actif, etc.).</param>
    /// <returns>Une collection de politiques applicables à l'entité spécifiée.</returns>
    Task<IEnumerable<Politique>> ObtenirParEntiteApplicationAsync(Guid entiteId, string typeEntite);

    /// <summary>
    /// Obtient les politiques par mot-clé.
    /// </summary>
    /// <param name="motCle">Mot-clé à rechercher.</param>
    /// <returns>Une collection de politiques contenant le mot-clé spécifié.</returns>
    Task<IEnumerable<Politique>> ObtenirParMotCleAsync(string motCle);

    /// <summary>
    /// Obtient les versions d'une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>Une collection de versions de la politique spécifiée.</returns>
    Task<IEnumerable<Politique>> ObtenirVersionsAsync(Guid politiqueId);

    /// <summary>
    /// Crée une nouvelle version d'une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique à versionner.</param>
    /// <returns>La nouvelle version de la politique.</returns>
    Task<Politique> CreerNouvelleVersionAsync(Guid politiqueId);

    /// <summary>
    /// Approuve une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique à approuver.</param>
    /// <param name="approbation">Informations d'approbation.</param>
    /// <returns>La politique approuvée.</returns>
    Task<Politique> ApprouverPolitiqueAsync(Guid politiqueId, ApprobationPolitique approbation);

    /// <summary>
    /// Archive une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique à archiver.</param>
    /// <returns>La politique archivée.</returns>
    Task<Politique> ArchiverPolitiqueAsync(Guid politiqueId);

    /// <summary>
    /// Obtient les approbations d'une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>Une collection d'approbations pour la politique spécifiée.</returns>
    Task<IEnumerable<ApprobationPolitique>> ObtenirApprobationsAsync(Guid politiqueId);

    /// <summary>
    /// Ajoute une approbation à une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <param name="approbation">Approbation à ajouter.</param>
    /// <returns>L'approbation ajoutée.</returns>
    Task<ApprobationPolitique> AjouterApprobationAsync(Guid politiqueId, ApprobationPolitique approbation);

    /// <summary>
    /// Associe des actifs de données à une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <param name="actifsIds">Liste des identifiants des actifs à associer.</param>
    /// <returns>True si l'association a réussi.</returns>
    Task<bool> AssocierActifsAsync(Guid politiqueId, IEnumerable<Guid> actifsIds);

    /// <summary>
    /// Dissocie un actif de données d'une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <param name="actifId">Identifiant de l'actif à dissocier.</param>
    /// <returns>True si la dissociation a réussi.</returns>
    Task<bool> DissocierActifAsync(Guid politiqueId, Guid actifId);

    /// <summary>
    /// Recherche des politiques par texte.
    /// </summary>
    /// <param name="texte">Texte à rechercher.</param>
    /// <returns>Une collection de politiques correspondant au texte recherché.</returns>
    Task<IEnumerable<Politique>> RechercherAsync(string texte);

    /// <summary>
    /// Exporte une politique au format PDF.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique à exporter.</param>
    /// <returns>Le chemin du fichier PDF généré.</returns>
    Task<string> ExporterPdfAsync(Guid politiqueId);
}
