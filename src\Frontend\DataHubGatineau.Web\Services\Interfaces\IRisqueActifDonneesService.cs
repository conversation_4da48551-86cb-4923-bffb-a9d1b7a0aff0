using DataHubGatineau.Web.Models.RiskManagement;

namespace DataHubGatineau.Web.Services.Interfaces;

/// <summary>
/// Interface pour le service de gestion des risques d'actifs de données.
/// </summary>
public interface IRisqueActifDonneesService : IServiceBaseGuid<RisqueActifDonnees>
{
    /// <summary>
    /// Obtient tous les risques associés à un actif de données spécifique.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <returns>Une collection de risques associés à l'actif.</returns>
    Task<IEnumerable<RisqueActifDonnees>> ObtenirParActifDonneesAsync(Guid actifDonneesId);

    /// <summary>
    /// Obtient tous les risques par type de risque.
    /// </summary>
    /// <param name="typeRisque">Le type de risque à filtrer.</param>
    /// <returns>Une collection de risques du type spécifié.</returns>
    Task<IEnumerable<RisqueActifDonnees>> ObtenirParTypeRisqueAsync(TypeRisque typeRisque);

    /// <summary>
    /// Obtient tous les risques par niveau de risque.
    /// </summary>
    /// <param name="niveauRisque">Le niveau de risque à filtrer.</param>
    /// <returns>Une collection de risques du niveau spécifié.</returns>
    Task<IEnumerable<RisqueActifDonnees>> ObtenirParNiveauRisqueAsync(NiveauRisque niveauRisque);

    /// <summary>
    /// Obtient tous les risques par statut.
    /// </summary>
    /// <param name="statut">Le statut à filtrer.</param>
    /// <returns>Une collection de risques avec le statut spécifié.</returns>
    Task<IEnumerable<RisqueActifDonnees>> ObtenirParStatutAsync(StatutRisque statut);

    /// <summary>
    /// Obtient tous les risques assignés à un responsable spécifique.
    /// </summary>
    /// <param name="responsableId">L'identifiant du responsable.</param>
    /// <returns>Une collection de risques assignés au responsable.</returns>
    Task<IEnumerable<RisqueActifDonnees>> ObtenirParResponsableAsync(string responsableId);

    /// <summary>
    /// Obtient tous les risques avec une date limite dépassée.
    /// </summary>
    /// <returns>Une collection de risques en retard.</returns>
    Task<IEnumerable<RisqueActifDonnees>> ObtenirRisquesEnRetardAsync();

    /// <summary>
    /// Obtient tous les risques critiques (niveau élevé ou critique).
    /// </summary>
    /// <returns>Une collection de risques critiques.</returns>
    Task<IEnumerable<RisqueActifDonnees>> ObtenirRisquesCritiquesAsync();

    /// <summary>
    /// Calcule le score de risque global pour un actif de données.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <returns>Le score de risque global.</returns>
    Task<int> CalculerScoreRisqueGlobalAsync(Guid actifDonneesId);

    /// <summary>
    /// Obtient les statistiques de risques par type.
    /// </summary>
    /// <returns>Un dictionnaire avec les statistiques par type de risque.</returns>
    Task<Dictionary<TypeRisque, int>> ObtenirStatistiquesParTypeAsync();

    /// <summary>
    /// Obtient les statistiques de risques par niveau.
    /// </summary>
    /// <returns>Un dictionnaire avec les statistiques par niveau de risque.</returns>
    Task<Dictionary<NiveauRisque, int>> ObtenirStatistiquesParNiveauAsync();

    /// <summary>
    /// Obtient les statistiques de risques par statut.
    /// </summary>
    /// <returns>Un dictionnaire avec les statistiques par statut.</returns>
    Task<Dictionary<StatutRisque, int>> ObtenirStatistiquesParStatutAsync();

    /// <summary>
    /// Met à jour le statut d'un risque.
    /// </summary>
    /// <param name="risqueId">L'identifiant du risque.</param>
    /// <param name="nouveauStatut">Le nouveau statut.</param>
    /// <param name="commentaire">Commentaire optionnel sur le changement de statut.</param>
    /// <returns>Le risque mis à jour.</returns>
    Task<RisqueActifDonnees> MettreAJourStatutAsync(Guid risqueId, StatutRisque nouveauStatut, string? commentaire = null);

    /// <summary>
    /// Assigne un risque à un responsable.
    /// </summary>
    /// <param name="risqueId">L'identifiant du risque.</param>
    /// <param name="responsableId">L'identifiant du responsable.</param>
    /// <param name="responsableNom">Le nom du responsable.</param>
    /// <returns>Le risque mis à jour.</returns>
    Task<RisqueActifDonnees> AssignerResponsableAsync(Guid risqueId, string responsableId, string responsableNom);

    /// <summary>
    /// Archive un risque.
    /// </summary>
    /// <param name="risqueId">L'identifiant du risque.</param>
    /// <returns>True si l'archivage a réussi, false sinon.</returns>
    Task<bool> ArchiverAsync(Guid risqueId);

    /// <summary>
    /// Recherche des risques par mots-clés.
    /// </summary>
    /// <param name="motsCles">Les mots-clés de recherche.</param>
    /// <returns>Une collection de risques correspondant aux critères de recherche.</returns>
    Task<IEnumerable<RisqueActifDonnees>> RechercherAsync(string motsCles);

    /// <summary>
    /// Génère un rapport de risques pour un actif de données.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <returns>Les données du rapport de risques.</returns>
    Task<RapportRisques> GenererRapportRisquesAsync(Guid actifDonneesId);

    /// <summary>
    /// Évalue automatiquement les risques pour un actif de données basé sur ses caractéristiques.
    /// </summary>
    /// <param name="actifDonneesId">L'identifiant de l'actif de données.</param>
    /// <returns>Une collection de risques potentiels identifiés automatiquement.</returns>
    Task<IEnumerable<RisqueActifDonnees>> EvaluerRisquesAutomatiquementAsync(Guid actifDonneesId);
}

/// <summary>
/// Représente un rapport de risques pour un actif de données.
/// </summary>
public class RapportRisques
{
    public Guid ActifDonneesId { get; set; }
    public string NomActif { get; set; } = string.Empty;
    public int NombreRisquesTotal { get; set; }
    public int NombreRisquesCritiques { get; set; }
    public int NombreRisquesEleves { get; set; }
    public int NombreRisquesMoyens { get; set; }
    public int NombreRisquesFaibles { get; set; }
    public int ScoreRisqueGlobal { get; set; }
    public NiveauRisque NiveauRisqueGlobal { get; set; }
    public List<RisqueActifDonnees> RisquesActifs { get; set; } = new List<RisqueActifDonnees>();
    public List<RisqueActifDonnees> RisquesResolus { get; set; } = new List<RisqueActifDonnees>();
    public DateTime DateGeneration { get; set; } = DateTime.Now;
    public string GenerePar { get; set; } = string.Empty;
}
