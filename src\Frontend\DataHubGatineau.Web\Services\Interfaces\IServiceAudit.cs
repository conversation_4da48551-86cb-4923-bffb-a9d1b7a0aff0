using DataHubGatineau.Web.Models.Auditoria;

namespace DataHubGatineau.Web.Services.Interfaces
{
    /// <summary>
    /// Interface pour le service d'audit du système de gouvernance des données
    /// Inspiré des meilleures pratiques de Collibra et Microsoft Purview
    /// </summary>
    public interface IServiceAudit
    {
        /// <summary>
        /// Enregistre une entrée d'audit complète
        /// </summary>
        Task EnregistrerAuditAsync(EntreeAudit entree);

        /// <summary>
        /// Enregistre une action d'audit avec les paramètres enrichis
        /// </summary>
        Task EnregistrerActionAsync(
            string action,
            string typeEntite,
            Guid? entiteId = null,
            string? nomEntite = null,
            TypeActionAudit typeAction = TypeActionAudit.Lecture,
            NiveauGraviteAudit niveauGravite = NiveauGraviteAudit.Information,
            CategorieAudit categorie = CategorieAudit.GouvernanceDonnees,
            string? valeursAvant = null,
            string? valeursApres = null,
            string? commentaire = null,
            string? contexte = null,
            string? domaineAffaires = null,
            ClassificationSensibilite? classification = null,
            bool requiertConformite = false,
            string? reglementations = null);

        /// <summary>
        /// Enregistre une connexion utilisateur avec contexte enrichi
        /// </summary>
        Task EnregistrerConnexionAsync(string utilisateurId, string nomUtilisateur, bool succes = true, string? messageErreur = null);

        /// <summary>
        /// Enregistre une déconnexion utilisateur
        /// </summary>
        Task EnregistrerDeconnexionAsync(string utilisateurId, string nomUtilisateur);

        /// <summary>
        /// Enregistre une action de création avec audit complet
        /// </summary>
        Task EnregistrerCreationAsync(string typeEntite, Guid entiteId, string? nomEntite = null, object? valeurs = null,
            string? commentaire = null, CategorieAudit categorie = CategorieAudit.GouvernanceDonnees,
            ClassificationSensibilite? classification = null);

        /// <summary>
        /// Enregistre une action de modification avec comparaison avant/après
        /// </summary>
        Task EnregistrerModificationAsync(string typeEntite, Guid entiteId, string? nomEntite = null,
            object? valeursAvant = null, object? valeursApres = null, string? commentaire = null,
            CategorieAudit categorie = CategorieAudit.GouvernanceDonnees, ClassificationSensibilite? classification = null);

        /// <summary>
        /// Enregistre une action de suppression avec sauvegarde des valeurs
        /// </summary>
        Task EnregistrerSuppressionAsync(string typeEntite, Guid entiteId, string? nomEntite = null, object? valeurs = null,
            string? commentaire = null, CategorieAudit categorie = CategorieAudit.GouvernanceDonnees,
            ClassificationSensibilite? classification = null);

        /// <summary>
        /// Enregistre une action de sécurité ou violation
        /// </summary>
        Task EnregistrerEvenementSecuriteAsync(string action, string details, NiveauGraviteAudit gravite = NiveauGraviteAudit.Securite);

        /// <summary>
        /// Enregistre une action de conformité réglementaire
        /// </summary>
        Task EnregistrerEvenementConformiteAsync(string action, string typeEntite, Guid? entiteId = null,
            string? reglementations = null, bool conforme = true, string? details = null);

        /// <summary>
        /// Obtient les entrées d'audit avec filtres avancés
        /// </summary>
        Task<ResultatPagineAudit> ObtenirAuditsAsync(FiltresAudit filtres);

        /// <summary>
        /// Obtient les entrées d'audit pour une entité spécifique avec historique complet
        /// </summary>
        Task<List<EntreeAudit>> ObtenirHistoriqueEntiteAsync(string typeEntite, Guid entiteId);

        /// <summary>
        /// Obtient les entrées d'audit pour un utilisateur avec analyse comportementale
        /// </summary>
        Task<ResultatAuditUtilisateur> ObtenirAuditsUtilisateurAsync(string utilisateurId, DateTime? dateDebut = null, DateTime? dateFin = null);

        /// <summary>
        /// Obtient les statistiques d'audit avec métriques avancées
        /// </summary>
        Task<StatistiquesAuditAvancees> ObtenirStatistiquesAvanceesAsync(DateTime? dateDebut = null, DateTime? dateFin = null);

        /// <summary>
        /// Obtient les événements de sécurité et violations
        /// </summary>
        Task<List<EntreeAudit>> ObtenirEvenementsSecuriteAsync(DateTime? dateDebut = null, DateTime? dateFin = null);

        /// <summary>
        /// Obtient le rapport de conformité réglementaire
        /// </summary>
        Task<RapportConformiteAudit> ObtenirRapportConformiteAsync(string? reglementation = null, DateTime? dateDebut = null, DateTime? dateFin = null);

        /// <summary>
        /// Exporte les données d'audit selon les exigences réglementaires
        /// </summary>
        Task<byte[]> ExporterAuditsAsync(FiltresAudit filtres, FormatExport format = FormatExport.Excel);
    }

    /// <summary>
    /// Filtres pour la recherche d'audit
    /// </summary>
    public class FiltresAudit
    {
        public int Page { get; set; } = 1;
        public int Taille { get; set; } = 50;
        public string? TexteRecherche { get; set; }
        public DateTime? DateDebut { get; set; }
        public DateTime? DateFin { get; set; }
        public List<TypeActionAudit>? TypesAction { get; set; }
        public List<NiveauGraviteAudit>? NiveauxGravite { get; set; }
        public List<CategorieAudit>? Categories { get; set; }
        public List<string>? UtilisateursIds { get; set; }
        public List<string>? TypesEntite { get; set; }
        public List<Guid>? EntitesIds { get; set; }
        public bool? Succes { get; set; }
        public List<ClassificationSensibilite>? Classifications { get; set; }
        public bool? RequiertConformite { get; set; }
        public string? DomaineAffaires { get; set; }
        public string? Reglementation { get; set; }
    }

    /// <summary>
    /// Résultat paginé d'audit
    /// </summary>
    public class ResultatPagineAudit
    {
        public List<EntreeAudit> Entrees { get; set; } = new();
        public int TotalEntrees { get; set; }
        public int Page { get; set; }
        public int Taille { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalEntrees / Taille);
        public bool APrecedent => Page > 1;
        public bool ASuivant => Page < TotalPages;
    }

    /// <summary>
    /// Résultat d'audit pour un utilisateur avec analyse comportementale
    /// </summary>
    public class ResultatAuditUtilisateur
    {
        public string UtilisateurId { get; set; } = string.Empty;
        public string NomUtilisateur { get; set; } = string.Empty;
        public List<EntreeAudit> Entrees { get; set; } = new();
        public StatistiquesComportementales Comportement { get; set; } = new();
        public List<AnomalieComportementale> Anomalies { get; set; } = new();
    }

    /// <summary>
    /// Statistiques comportementales d'un utilisateur
    /// </summary>
    public class StatistiquesComportementales
    {
        public int TotalActions { get; set; }
        public int ActionsParJour { get; set; }
        public Dictionary<TypeActionAudit, int> ActionsParType { get; set; } = new();
        public Dictionary<string, int> ActionsParHeure { get; set; } = new();
        public List<string> EntitesAccedees { get; set; } = new();
        public DateTime? DerniereConnexion { get; set; }
        public TimeSpan? DureeMoyenneSession { get; set; }
    }

    /// <summary>
    /// Anomalie comportementale détectée
    /// </summary>
    public class AnomalieComportementale
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public NiveauGraviteAudit Gravite { get; set; }
        public DateTime DateDetection { get; set; }
        public Dictionary<string, object> Metadonnees { get; set; } = new();
    }

    /// <summary>
    /// Statistiques d'audit avancées
    /// </summary>
    public class StatistiquesAuditAvancees
    {
        public int TotalEntrees { get; set; }
        public int TotalConnexions { get; set; }
        public int TotalErreurs { get; set; }
        public int TotalActionsSecurite { get; set; }
        public int TotalViolationsConformite { get; set; }

        public Dictionary<TypeActionAudit, int> ActionsParType { get; set; } = new();
        public Dictionary<NiveauGraviteAudit, int> ActionsParGravite { get; set; } = new();
        public Dictionary<CategorieAudit, int> ActionsParCategorie { get; set; } = new();
        public Dictionary<string, int> ActionsParUtilisateur { get; set; } = new();
        public Dictionary<string, int> ActionsParEntite { get; set; } = new();
        public Dictionary<string, int> ActionsParHeure { get; set; } = new();
        public Dictionary<string, int> ActionsParJour { get; set; } = new();

        public List<EntreeAudit> DernieresActions { get; set; } = new();
        public List<EntreeAudit> ActionsSecuriteCritiques { get; set; } = new();
        public List<TendanceAudit> Tendances { get; set; } = new();
        public List<UtilisateurActif> UtilisateursLesPlusActifs { get; set; } = new();
    }

    /// <summary>
    /// Tendance d'audit sur une période
    /// </summary>
    public class TendanceAudit
    {
        public DateTime Date { get; set; }
        public int NombreActions { get; set; }
        public int NombreErreurs { get; set; }
        public int NombreConnexions { get; set; }
        public Dictionary<TypeActionAudit, int> ActionsParType { get; set; } = new();
    }

    /// <summary>
    /// Utilisateur actif avec statistiques
    /// </summary>
    public class UtilisateurActif
    {
        public string UtilisateurId { get; set; } = string.Empty;
        public string NomUtilisateur { get; set; } = string.Empty;
        public int NombreActions { get; set; }
        public DateTime? DerniereActivite { get; set; }
        public List<TypeActionAudit> TypesActionsFrequentes { get; set; } = new();
    }

    /// <summary>
    /// Rapport de conformité réglementaire
    /// </summary>
    public class RapportConformiteAudit
    {
        public string? Reglementation { get; set; }
        public DateTime DateDebut { get; set; }
        public DateTime DateFin { get; set; }
        public int TotalActionsConformite { get; set; }
        public int ActionsConformes { get; set; }
        public int ActionsNonConformes { get; set; }
        public double PourcentageConformite => TotalActionsConformite > 0 ? (double)ActionsConformes / TotalActionsConformite * 100 : 0;

        public List<ViolationConformite> Violations { get; set; } = new();
        public Dictionary<string, int> ViolationsParType { get; set; } = new();
        public List<ActionCorrectiveRecommandee> ActionsCorrectivesRecommandees { get; set; } = new();
    }

    /// <summary>
    /// Violation de conformité détectée
    /// </summary>
    public class ViolationConformite
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime DateViolation { get; set; }
        public string UtilisateurId { get; set; } = string.Empty;
        public string? EntiteAffectee { get; set; }
        public NiveauGraviteAudit Gravite { get; set; }
        public string? ActionCorrectivePrise { get; set; }
        public bool Resolue { get; set; }
    }

    /// <summary>
    /// Action corrective recommandée
    /// </summary>
    public class ActionCorrectiveRecommandee
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Priorite { get; set; }
        public List<string> EtapesRecommandees { get; set; } = new();
    }

    /// <summary>
    /// Format d'export pour les données d'audit
    /// </summary>
    public enum FormatExport
    {
        Excel,
        Csv,
        Json,
        Pdf
    }
}
