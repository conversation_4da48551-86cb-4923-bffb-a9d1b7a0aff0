using DataHubGatineau.Web.Models;
using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Web.Services.Interfaces
{
    /// <summary>
    /// Interface pour la gestion des utilisateurs et rôles dans la plateforme de gouvernance des données
    /// </summary>
    public interface IServiceGestionUtilisateurs
    {
        // =============================================
        // GESTION DES UTILISATEURS
        // =============================================

        /// <summary>
        /// Obtient tous les utilisateurs avec leurs rôles
        /// </summary>
        Task<List<UtilisateurAvecRoles>> ObtenirTousUtilisateursAsync();

        /// <summary>
        /// Obtient un utilisateur par son ID avec ses rôles
        /// </summary>
        Task<UtilisateurAvecRoles?> ObtenirUtilisateurParIdAsync(Guid id);

        /// <summary>
        /// Crée un nouvel utilisateur
        /// </summary>
        Task<UtilisateurAvecRoles> CreerUtilisateurAsync(DemandeCreationUtilisateur demande);

        /// <summary>
        /// Met à jour un utilisateur existant
        /// </summary>
        Task<UtilisateurAvecRoles> MettreAJourUtilisateurAsync(Guid id, DemandeMiseAJourUtilisateur demande);

        /// <summary>
        /// Supprime un utilisateur
        /// </summary>
        Task<bool> SupprimerUtilisateurAsync(Guid id);

        /// <summary>
        /// Active ou désactive un utilisateur
        /// </summary>
        Task<bool> BasculerActivationUtilisateurAsync(Guid id);

        // =============================================
        // GESTION DES RÔLES
        // =============================================

        /// <summary>
        /// Obtient tous les rôles disponibles
        /// </summary>
        Task<List<InfoRole>> ObtenirTousRolesAsync();

        /// <summary>
        /// Obtient les rôles par niveau (Organisationnel, Catalogue, Workflow, Utilisateur)
        /// </summary>
        Task<Dictionary<string, List<InfoRole>>> ObtenirRolesParNiveauAsync();

        /// <summary>
        /// Ajoute un rôle à un utilisateur
        /// </summary>
        Task<bool> AjouterRoleUtilisateurAsync(Guid utilisateurId, Guid roleId);

        /// <summary>
        /// Supprime un rôle d'un utilisateur
        /// </summary>
        Task<bool> SupprimerRoleUtilisateurAsync(Guid utilisateurId, Guid roleId);

        /// <summary>
        /// Obtient les utilisateurs ayant un rôle spécifique
        /// </summary>
        Task<List<UtilisateurAvecRoles>> ObtenirUtilisateursParRoleAsync(Guid roleId);

        // =============================================
        // GESTION DES WORKFLOWS (Assignment automatique)
        // =============================================

        /// <summary>
        /// Obtient les approbateurs disponibles pour une étape de workflow
        /// </summary>
        Task<List<UtilisateurAvecRoles>> ObtenirApprobaiteursDisponiblesAsync(string typeEtape);

        /// <summary>
        /// Assigne automatiquement les approbateurs pour un workflow
        /// </summary>
        Task<Dictionary<string, Guid>> AssignerApprobaiteursAutomatiqueAsync(Guid domaineId);

        // =============================================
        // STATISTIQUES ET RAPPORTS
        // =============================================

        /// <summary>
        /// Obtient les statistiques des utilisateurs et rôles
        /// </summary>
        Task<StatistiquesUtilisateurs> ObtenirStatistiquesAsync();

        /// <summary>
        /// Obtient l'activité récente des utilisateurs
        /// </summary>
        Task<List<ActiviteUtilisateur>> ObtenirActiviteRecenteAsync(int nombreJours = 30);
    }

    // =============================================
    // MODÈLES DE DONNÉES
    // =============================================

    public class UtilisateurAvecRoles
    {
        public Guid Id { get; set; }
        public string NomUtilisateur { get; set; } = string.Empty;
        public string Courriel { get; set; } = string.Empty;
        public string Prenom { get; set; } = string.Empty;
        public string Nom { get; set; } = string.Empty;
        public string? Telephone { get; set; }
        public string? Poste { get; set; }
        public string NomComplet => $"{Prenom} {Nom}";
        public bool EstActif { get; set; }
        public DateTime? DerniereConnexion { get; set; }
        public DateTime DateCreation { get; set; }
        public List<InfoRole> Roles { get; set; } = new();
        public string RolesTexte => string.Join(", ", Roles.Select(r => r.Nom));
        public string NiveauPrincipal => Roles.FirstOrDefault()?.Niveau ?? "Utilisateur";
    }

    public class InfoRole
    {
        public Guid Id { get; set; }
        public string Nom { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool EstSysteme { get; set; }
        public string Niveau { get; set; } = string.Empty; // Organisationnel, Catalogue, Workflow, Utilisateur
        public string Couleur { get; set; } = "primary"; // Pour l'affichage des badges
    }

    public class DemandeCreationUtilisateur
    {
        [Required(ErrorMessage = "Le nom d'utilisateur est requis")]
        [StringLength(50, ErrorMessage = "Le nom d'utilisateur ne peut pas dépasser 50 caractères")]
        public string NomUtilisateur { get; set; } = string.Empty;

        [Required(ErrorMessage = "Le courriel est requis")]
        [EmailAddress(ErrorMessage = "Format de courriel invalide")]
        public string Courriel { get; set; } = string.Empty;

        [Required(ErrorMessage = "Le prénom est requis")]
        [StringLength(50, ErrorMessage = "Le prénom ne peut pas dépasser 50 caractères")]
        public string Prenom { get; set; } = string.Empty;

        [Required(ErrorMessage = "Le nom est requis")]
        [StringLength(50, ErrorMessage = "Le nom ne peut pas dépasser 50 caractères")]
        public string Nom { get; set; } = string.Empty;

        [StringLength(20, ErrorMessage = "Le téléphone ne peut pas dépasser 20 caractères")]
        public string? Telephone { get; set; }

        [StringLength(10, ErrorMessage = "Le poste ne peut pas dépasser 10 caractères")]
        public string? Poste { get; set; }

        public string? MotDePasse { get; set; }
        public List<Guid> IdsRoles { get; set; } = new();
        public bool EstActif { get; set; } = true;
    }

    public class DemandeMiseAJourUtilisateur
    {
        public string? Courriel { get; set; }
        public string? Prenom { get; set; }
        public string? Nom { get; set; }
        public string? Telephone { get; set; }
        public string? Poste { get; set; }
        public bool? EstActif { get; set; }
        public List<Guid>? IdsRoles { get; set; }
    }

    public class StatistiquesUtilisateurs
    {
        public int TotalUtilisateurs { get; set; }
        public int UtilisateursActifs { get; set; }
        public int UtilisateursInactifs { get; set; }
        public Dictionary<string, int> UtilisateursParRole { get; set; } = new();
        public Dictionary<string, int> UtilisateursParNiveau { get; set; } = new();
        public int ConnexionsRecentes { get; set; } // Derniers 7 jours
    }

    public class DemandeCreationRole
    {
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public class DemandeMiseAJourRole
    {
        public string Nom { get; set; } = string.Empty;
        public string? Description { get; set; }
    }

    public class ActiviteUtilisateur
    {
        public Guid UtilisateurId { get; set; }
        public string NomUtilisateur { get; set; } = string.Empty;
        public string NomComplet { get; set; } = string.Empty;
        public string Action { get; set; } = string.Empty;
        public DateTime DateAction { get; set; }
        public string? Details { get; set; }
    }
}
