using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataHubGatineau.Web.Models;
using DataHubGatineau.Web.Models.Policy;
using DataHubGatineau.Web.Services.Interfaces;

namespace DataHubGatineau.Web.Services.MockServices
{
    public class MockActifDonneesService : IActifDonneesService
    {
        private readonly List<ActifDonnees> _actifsDonnees;

        // GUIDs fixes pour les types d'actifs
        private static readonly Guid TypeBaseDonneesId = Guid.Parse("11111111-1111-1111-1111-111111111111");
        private static readonly Guid TypeFichierId = Guid.Parse("*************-2222-2222-************");
        private static readonly Guid TypeApiId = Guid.Parse("*************-3333-3333-************");
        private static readonly Guid TypeFluxId = Guid.Parse("*************-4444-4444-************");
        private static readonly Guid TypeEntrepotId = Guid.Parse("*************-5555-5555-************");

        // GUIDs fixes pour les connexions sources de données
        private static readonly Guid ConnexionSqlServerId = Guid.Parse("*************-6666-6666-************");
        private static readonly Guid ConnexionOracleId = Guid.Parse("*************-7777-7777-************");
        private static readonly Guid ConnexionPostgreSQLId = Guid.Parse("*************-8888-8888-************");

        // Méthodes supplémentaires pour implémenter l'interface IActifDonneesService
        public async Task<IEnumerable<ActifDonnees>> ObtenirParTypeAsync(string type)
        {
            await Task.Delay(300);
            // Puisque nous utilisons maintenant TypeActifDonneesId au lieu de Type
            return _actifsDonnees; // Retourner tous les actifs pour le moment
        }

        public async Task<IEnumerable<ActifDonnees>> ObtenirParClassificationAsync(ClassificationSensibilite classification)
        {
            await Task.Delay(300);
            return _actifsDonnees.Where(a => a.ClassificationSensibilite == classification);
        }

        // Méthode pour rechercher des actifs de données
        public async Task<IEnumerable<ActifDonnees>> RechercherAsync(string terme)
        {
            await Task.Delay(300);

            if (string.IsNullOrWhiteSpace(terme))
                return _actifsDonnees;

            terme = terme.ToLower();
            return _actifsDonnees.Where(a =>
                a.Nom.ToLower().Contains(terme) ||
                (a.Description != null && a.Description.ToLower().Contains(terme)) ||
                a.Proprietaire.ToLower().Contains(terme)
            );
        }

        public MockActifDonneesService()
        {
            // Données simulées
            _actifsDonnees = new List<ActifDonnees>
            {
                new ActifDonnees
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000001"),
                    Nom = "Base de données clients",
                    Description = "Base de données principale contenant toutes les informations des clients de la ville de Gatineau.",
                    TypeActifDonneesId = TypeBaseDonneesId, // Base de données
                    SourceActifDonneesId = Guid.NewGuid(),
                    ConnexionSourceDonneesId = ConnexionSqlServerId,
                    Proprietaire = "Service des technologies de l'information",
                    ClassificationSensibilite = ClassificationSensibilite.Confidentiel,
                    DateCreation = DateTime.Now.AddDays(-100),
                    DateModification = DateTime.Now.AddDays(-5),
                    ConnexionSourceDonnees = new ConnexionSourceDonnees
                    {
                        Id = ConnexionSqlServerId,
                        Nom = "SQL Server Principal",
                        TypeConnexion = "SQL Server",
                        Serveur = "srv-sql-prod-01.gatineau.ca",
                        BaseDonnees = "GatineauClients",
                        Port = 1433
                    }
                },
                new ActifDonnees
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000002"),
                    Nom = "Données de facturation",
                    Description = "Système de facturation pour les services municipaux.",
                    TypeActifDonneesId = TypeBaseDonneesId, // Base de données
                    SourceActifDonneesId = Guid.NewGuid(),
                    ConnexionSourceDonneesId = ConnexionOracleId,
                    Proprietaire = "Service des finances",
                    ClassificationSensibilite = ClassificationSensibilite.Interne,
                    DateCreation = DateTime.Now.AddDays(-200),
                    DateModification = DateTime.Now.AddDays(-10),
                    ConnexionSourceDonnees = new ConnexionSourceDonnees
                    {
                        Id = ConnexionOracleId,
                        Nom = "Oracle Finances",
                        TypeConnexion = "Oracle",
                        Serveur = "srv-oracle-fin-01.gatineau.ca",
                        BaseDonnees = "FINGATINEAU",
                        Port = 1521
                    }
                },
                new ActifDonnees
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000003"),
                    Nom = "Registre des permis",
                    Description = "Base de données des permis émis par la ville.",
                    TypeActifDonneesId = TypeBaseDonneesId, // Base de données
                    SourceActifDonneesId = Guid.NewGuid(),
                    ConnexionSourceDonneesId = ConnexionPostgreSQLId,
                    Proprietaire = "Service de l'urbanisme",
                    ClassificationSensibilite = ClassificationSensibilite.Public,
                    DateCreation = DateTime.Now.AddDays(-150),
                    DateModification = DateTime.Now.AddDays(-2),
                    ConnexionSourceDonnees = new ConnexionSourceDonnees
                    {
                        Id = ConnexionPostgreSQLId,
                        Nom = "PostgreSQL Urbanisme",
                        TypeConnexion = "PostgreSQL",
                        Serveur = "srv-postgres-urb-01.gatineau.ca",
                        BaseDonnees = "permis_urbanisme",
                        Port = 5432
                    }
                },
                new ActifDonnees
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000004"),
                    Nom = "Données géospatiales",
                    Description = "Système d'information géographique de la ville.",
                    TypeActifDonneesId = TypeFichierId, // Fichier (datos geoespaciales)
                    SourceActifDonneesId = Guid.NewGuid(),
                    Proprietaire = "Service de la géomatique",
                    ClassificationSensibilite = ClassificationSensibilite.Interne,
                    DateCreation = DateTime.Now.AddDays(-300),
                    DateModification = DateTime.Now.AddDays(-15)
                },
                new ActifDonnees
                {
                    Id = Guid.Parse("00000000-0000-0000-0000-000000000005"),
                    Nom = "Données des ressources humaines",
                    Description = "Système de gestion des ressources humaines.",
                    TypeActifDonneesId = TypeBaseDonneesId, // Base de données
                    SourceActifDonneesId = Guid.NewGuid(),
                    ConnexionSourceDonneesId = ConnexionSqlServerId,
                    Proprietaire = "Service des ressources humaines",
                    ClassificationSensibilite = ClassificationSensibilite.Confidentiel,
                    DateCreation = DateTime.Now.AddDays(-250),
                    DateModification = DateTime.Now.AddDays(-8),
                    ConnexionSourceDonnees = new ConnexionSourceDonnees
                    {
                        Id = ConnexionSqlServerId,
                        Nom = "SQL Server Principal",
                        TypeConnexion = "SQL Server",
                        Serveur = "srv-sql-prod-01.gatineau.ca",
                        BaseDonnees = "GatineauRH",
                        Port = 1433
                    }
                }
            };
        }

        public async Task<IEnumerable<ActifDonnees>> ObtenirTousAsync()
        {
            // Simuler un délai réseau
            await Task.Delay(300);
            return _actifsDonnees;
        }

        public async Task<ActifDonnees?> ObtenirParIdAsync(Guid id)
        {
            await Task.Delay(200);
            return _actifsDonnees.FirstOrDefault(a => a.Id == id);
        }

        public async Task<ActifDonnees> AjouterAsync(ActifDonnees actifDonnees)
        {
            await Task.Delay(300);

            // Générer un nouvel ID
            actifDonnees.Id = Guid.NewGuid();

            // Ajouter les métadonnées
            actifDonnees.DateCreation = DateTime.Now;
            actifDonnees.DateModification = DateTime.Now;

            _actifsDonnees.Add(actifDonnees);
            return actifDonnees;
        }

        public async Task<ActifDonnees> MettreAJourAsync(Guid id, ActifDonnees actifDonnees)
        {
            await Task.Delay(300);

            var existingActif = _actifsDonnees.FirstOrDefault(a => a.Id == id);
            if (existingActif == null)
                throw new KeyNotFoundException($"Actif de données avec ID {id} non trouvé");

            // Mettre à jour les propriétés
            existingActif.Nom = actifDonnees.Nom;
            existingActif.Description = actifDonnees.Description;
            existingActif.TypeActifDonneesId = actifDonnees.TypeActifDonneesId;
            existingActif.SourceActifDonneesId = actifDonnees.SourceActifDonneesId;
            existingActif.Proprietaire = actifDonnees.Proprietaire;
            existingActif.ClassificationSensibilite = actifDonnees.ClassificationSensibilite;
            existingActif.FrequenceMiseAJourId = actifDonnees.FrequenceMiseAJourId;
            existingActif.StatutActifDonneesId = actifDonnees.StatutActifDonneesId;
            existingActif.FormatActifDonneesId = actifDonnees.FormatActifDonneesId;
            existingActif.DomaineGouvernanceId = actifDonnees.DomaineGouvernanceId;
            existingActif.EstElementCritique = actifDonnees.EstElementCritique;

            // Mettre à jour les produits de données
            existingActif.ProduitsDonnees = actifDonnees.ProduitsDonnees;

            // Mettre à jour les métadonnées
            existingActif.DateModification = DateTime.Now;

            return existingActif;
        }

        public async Task<ActifDonnees> ModifierAsync(ActifDonnees actifDonnees)
        {
            await Task.Delay(300);

            var existingActif = _actifsDonnees.FirstOrDefault(a => a.Id == actifDonnees.Id);
            if (existingActif == null)
                throw new KeyNotFoundException($"Actif de données avec ID {actifDonnees.Id} non trouvé");

            // Mettre à jour les propriétés
            existingActif.Nom = actifDonnees.Nom;
            existingActif.Description = actifDonnees.Description;
            existingActif.TypeActifDonneesId = actifDonnees.TypeActifDonneesId;
            existingActif.SourceActifDonneesId = actifDonnees.SourceActifDonneesId;
            existingActif.Proprietaire = actifDonnees.Proprietaire;
            existingActif.ClassificationSensibilite = actifDonnees.ClassificationSensibilite;
            existingActif.FrequenceMiseAJourId = actifDonnees.FrequenceMiseAJourId;
            existingActif.StatutActifDonneesId = actifDonnees.StatutActifDonneesId;

            // Mettre à jour les métadonnées
            existingActif.DateModification = DateTime.Now;

            return existingActif;
        }

        public async Task<bool> SupprimerAsync(Guid id)
        {
            await Task.Delay(300);

            var actifToRemove = _actifsDonnees.FirstOrDefault(a => a.Id == id);
            if (actifToRemove == null)
                return false;

            _actifsDonnees.Remove(actifToRemove);
            return true;
        }

        public async Task<IEnumerable<ActifDonnees>> RechercherParNomAsync(string terme)
        {
            await Task.Delay(300);

            if (string.IsNullOrWhiteSpace(terme))
                return _actifsDonnees;

            terme = terme.ToLower();
            return _actifsDonnees.Where(a =>
                a.Nom.ToLower().Contains(terme)
            );
        }

        public async Task<IEnumerable<StatutActifDonnees>> ObtenirStatutsAsync()
        {
            await Task.Delay(300);

            // Retourner une liste de statuts simulés
            return new List<StatutActifDonnees>
            {
                new StatutActifDonnees { Id = Guid.NewGuid(), Nom = "Brouillon", Description = "Actif en cours de création" },
                new StatutActifDonnees { Id = Guid.NewGuid(), Nom = "En attente d'approbation", Description = "Actif en attente d'approbation" },
                new StatutActifDonnees { Id = Guid.NewGuid(), Nom = "Approuvé", Description = "Actif approuvé" },
                new StatutActifDonnees { Id = Guid.NewGuid(), Nom = "Rejeté", Description = "Actif rejeté" },
                new StatutActifDonnees { Id = Guid.NewGuid(), Nom = "Archivé", Description = "Actif archivé" }
            };
        }

        public async Task<IEnumerable<TypeActifDonneesItem>> ObtenirTypesAsync()
        {
            await Task.Delay(300);

            // Retourner une liste de types simulés avec GUIDs fixes
            return new List<TypeActifDonneesItem>
            {
                new TypeActifDonneesItem { Id = TypeBaseDonneesId, Nom = "Base de données", Description = "Base de données relationnelle ou NoSQL" },
                new TypeActifDonneesItem { Id = TypeFichierId, Nom = "Fichier", Description = "Fichier de données" },
                new TypeActifDonneesItem { Id = TypeApiId, Nom = "API", Description = "Interface de programmation d'application" },
                new TypeActifDonneesItem { Id = TypeFluxId, Nom = "Flux de données", Description = "Flux continu de données" },
                new TypeActifDonneesItem { Id = TypeEntrepotId, Nom = "Entrepôt de données", Description = "Entrepôt de données centralisé" }
            };
        }

        public async Task<IEnumerable<FormatActifDonneesItem>> ObtenirFormatsAsync()
        {
            await Task.Delay(300);

            // Retourner une liste de formats simulés
            return new List<FormatActifDonneesItem>
            {
                new FormatActifDonneesItem { Id = Guid.NewGuid(), Nom = "CSV", Description = "Valeurs séparées par des virgules" },
                new FormatActifDonneesItem { Id = Guid.NewGuid(), Nom = "JSON", Description = "JavaScript Object Notation" },
                new FormatActifDonneesItem { Id = Guid.NewGuid(), Nom = "XML", Description = "Extensible Markup Language" },
                new FormatActifDonneesItem { Id = Guid.NewGuid(), Nom = "SQL", Description = "Base de données SQL" },
                new FormatActifDonneesItem { Id = Guid.NewGuid(), Nom = "Excel", Description = "Fichier Microsoft Excel" }
            };
        }

        public async Task<IEnumerable<SourceActifDonneesItem>> ObtenirSourcesAsync()
        {
            await Task.Delay(300);

            // Retourner une liste de sources simulées
            return new List<SourceActifDonneesItem>
            {
                new SourceActifDonneesItem { Id = Guid.NewGuid(), Nom = "Interne", Description = "Source de données interne" },
                new SourceActifDonneesItem { Id = Guid.NewGuid(), Nom = "Externe", Description = "Source de données externe" },
                new SourceActifDonneesItem { Id = Guid.NewGuid(), Nom = "API", Description = "Interface de programmation d'application" },
                new SourceActifDonneesItem { Id = Guid.NewGuid(), Nom = "Fichier", Description = "Fichier de données" },
                new SourceActifDonneesItem { Id = Guid.NewGuid(), Nom = "Base de données", Description = "Base de données" }
            };
        }

        public async Task<IEnumerable<FrequenceMiseAJour>> ObtenirFrequencesAsync()
        {
            await Task.Delay(300);

            // Retourner une liste de fréquences simulées
            return new List<FrequenceMiseAJour>
            {
                new FrequenceMiseAJour { Id = Guid.NewGuid(), Nom = "Temps réel", Description = "Mise à jour en temps réel" },
                new FrequenceMiseAJour { Id = Guid.NewGuid(), Nom = "Horaire", Description = "Mise à jour toutes les heures" },
                new FrequenceMiseAJour { Id = Guid.NewGuid(), Nom = "Quotidienne", Description = "Mise à jour tous les jours" },
                new FrequenceMiseAJour { Id = Guid.NewGuid(), Nom = "Hebdomadaire", Description = "Mise à jour toutes les semaines" },
                new FrequenceMiseAJour { Id = Guid.NewGuid(), Nom = "Mensuelle", Description = "Mise à jour tous les mois" }
            };
        }

        public async Task<IEnumerable<TypeMetadonneeItem>> ObtenirTypesMetadonneesAsync()
        {
            await Task.Delay(300);

            // Retourner une liste de types de métadonnées simulés
            return new List<TypeMetadonneeItem>
            {
                new TypeMetadonneeItem { Id = Guid.NewGuid(), Nom = "Technique", Description = "Métadonnées techniques" },
                new TypeMetadonneeItem { Id = Guid.NewGuid(), Nom = "Affaires", Description = "Métadonnées liées aux processus d'affaires" },
                new TypeMetadonneeItem { Id = Guid.NewGuid(), Nom = "Opérationnel", Description = "Métadonnées opérationnelles" },
                new TypeMetadonneeItem { Id = Guid.NewGuid(), Nom = "Gouvernance", Description = "Métadonnées de gouvernance" },
                new TypeMetadonneeItem { Id = Guid.NewGuid(), Nom = "Autre", Description = "Autres types de métadonnées" }
            };
        }

        public async Task<IEnumerable<CategorieMetadonneeItem>> ObtenirCategoriesMetadonneesAsync()
        {
            await Task.Delay(300);

            // Retourner une liste de catégories de métadonnées simulées
            return new List<CategorieMetadonneeItem>
            {
                new CategorieMetadonneeItem { Id = Guid.NewGuid(), Nom = "Format", Description = "Format technique de l'actif" },
                new CategorieMetadonneeItem { Id = Guid.NewGuid(), Nom = "Taille", Description = "Taille de l'actif" },
                new CategorieMetadonneeItem { Id = Guid.NewGuid(), Nom = "Structure", Description = "Structure technique de l'actif" },
                new CategorieMetadonneeItem { Id = Guid.NewGuid(), Nom = "Encodage", Description = "Encodage des caractères" },
                new CategorieMetadonneeItem { Id = Guid.NewGuid(), Nom = "Processus", Description = "Processus d'affaires associé" }
            };
        }

        public async Task<bool> ExisteAsync(Guid id)
        {
            await Task.Delay(200);
            return _actifsDonnees.Any(a => a.Id == id);
        }

        public async Task<IEnumerable<Politique>> ObtenirPolitiquesAssocieesAsync(Guid actifId)
        {
            await Task.Delay(300);

            // Retourner des politiques simulées pour les tests
            return new List<Politique>
            {
                new Politique
                {
                    Id = Guid.NewGuid(),
                    Titre = "Politique de protection des données personnelles",
                    Description = "Politique régissant la protection des données personnelles des citoyens",
                    Categorie = "Sécurité",
                    Statut = StatutPolitique.Active,
                    Version = "2.1",
                    DateEntreeVigueur = DateTime.Now.AddDays(-30),
                    EstActive = true
                },
                new Politique
                {
                    Id = Guid.NewGuid(),
                    Titre = "Politique de rétention des données",
                    Description = "Politique définissant les durées de conservation des données municipales",
                    Categorie = "Gouvernance",
                    Statut = StatutPolitique.Active,
                    Version = "1.5",
                    DateEntreeVigueur = DateTime.Now.AddDays(-60),
                    EstActive = true
                }
            };
        }
    }
}
