using DataHubGatineau.Web.Models;
using DataHubGatineau.Web.Services.Interfaces;

namespace DataHubGatineau.Web.Services.MockServices;

/// <summary>
/// Service mock pour les historiques de scan.
/// </summary>
public class MockHistoriqueScanService : IHistoriqueScanService
{
    private readonly List<HistoriqueScan> _historiques;

    public MockHistoriqueScanService()
    {
        var configId1 = Guid.NewGuid();
        var configId2 = Guid.NewGuid();
        var configId3 = Guid.NewGuid();

        _historiques = new List<HistoriqueScan>
        {
            new HistoriqueScan
            {
                Id = Guid.NewGuid(),
                ConfigurationScanId = configId1,
                DateDebut = DateTime.Now.AddDays(-1),
                DateFin = DateTime.Now.AddDays(-1).AddHours(2),
                Statut = "Termine",
                EstReussi = true,
                ExecutePar = "<EMAIL>",
                NombreActifsDecouverts = 25,
                NombreActifsCrees = 15,
                NombreActifsMisAJour = 10,
                NombreErreurs = 0,
                ParametresUtilises = "Scan complet de la base de données SQL Server",
                ResultatsDetailles = "Scan terminé avec succès. 25 actifs découverts, 15 nouveaux actifs créés, 10 actifs mis à jour.",
                MessageErreur = null
            },
            new HistoriqueScan
            {
                Id = Guid.NewGuid(),
                ConfigurationScanId = configId2,
                DateDebut = DateTime.Now.AddDays(-2),
                DateFin = DateTime.Now.AddDays(-2).AddHours(1),
                Statut = "Termine",
                EstReussi = true,
                ExecutePar = "<EMAIL>",
                NombreActifsDecouverts = 30,
                NombreActifsCrees = 20,
                NombreActifsMisAJour = 8,
                NombreErreurs = 2,
                ParametresUtilises = "Scan incrémental des tables modifiées",
                ResultatsDetailles = "Scan terminé avec 2 erreurs mineures. 30 actifs découverts, 20 nouveaux actifs créés, 8 actifs mis à jour.",
                MessageErreur = "2 tables inaccessibles: temp_table1, temp_table2"
            },
            new HistoriqueScan
            {
                Id = Guid.NewGuid(),
                ConfigurationScanId = configId3,
                DateDebut = DateTime.Now.AddHours(-3),
                DateFin = null,
                Statut = "EnCours",
                EstReussi = false,
                ExecutePar = "<EMAIL>",
                NombreActifsDecouverts = 12,
                NombreActifsCrees = 5,
                NombreActifsMisAJour = 3,
                NombreErreurs = 0,
                ParametresUtilises = "Scan automatique programmé",
                ResultatsDetailles = "Scan en cours... 12 actifs découverts jusqu'à présent.",
                MessageErreur = null
            },
            new HistoriqueScan
            {
                Id = Guid.NewGuid(),
                ConfigurationScanId = configId1,
                DateDebut = DateTime.Now.AddDays(-3),
                DateFin = DateTime.Now.AddDays(-3).AddMinutes(45),
                Statut = "Erreur",
                EstReussi = false,
                ExecutePar = "<EMAIL>",
                NombreActifsDecouverts = 0,
                NombreActifsCrees = 0,
                NombreActifsMisAJour = 0,
                NombreErreurs = 1,
                ParametresUtilises = "Scan de la base de données Oracle",
                ResultatsDetailles = "Échec de la connexion à la base de données Oracle.",
                MessageErreur = "ORA-12154: TNS:could not resolve the connect identifier specified"
            },
            new HistoriqueScan
            {
                Id = Guid.NewGuid(),
                ConfigurationScanId = configId2,
                DateDebut = DateTime.Now.AddDays(-4),
                DateFin = DateTime.Now.AddDays(-4).AddHours(3),
                Statut = "Termine",
                EstReussi = true,
                ExecutePar = "<EMAIL>",
                NombreActifsDecouverts = 45,
                NombreActifsCrees = 35,
                NombreActifsMisAJour = 10,
                NombreErreurs = 0,
                ParametresUtilises = "Scan initial de la nouvelle base de données",
                ResultatsDetailles = "Premier scan de la base de données clients. 45 actifs découverts, tous nouveaux.",
                MessageErreur = null
            }
        };
    }

    public Task<HistoriqueScan> AjouterAsync(HistoriqueScan entite)
    {
        entite.Id = Guid.NewGuid();
        _historiques.Add(entite);
        return Task.FromResult(entite);
    }

    public Task<bool> ExisteAsync(Guid id)
    {
        return Task.FromResult(_historiques.Any(h => h.Id == id));
    }

    public Task<HistoriqueScan?> ObtenirParIdAsync(Guid id)
    {
        var historique = _historiques.FirstOrDefault(h => h.Id == id);
        return Task.FromResult(historique);
    }

    public Task<IEnumerable<HistoriqueScan>> ObtenirTousAsync()
    {
        return Task.FromResult(_historiques.AsEnumerable());
    }

    public Task<HistoriqueScan> MettreAJourAsync(Guid id, HistoriqueScan entite)
    {
        var index = _historiques.FindIndex(h => h.Id == id);
        if (index >= 0)
        {
            entite.Id = id;
            _historiques[index] = entite;
        }
        return Task.FromResult(entite);
    }

    public Task<bool> SupprimerAsync(Guid id)
    {
        var historique = _historiques.FirstOrDefault(h => h.Id == id);
        if (historique != null)
        {
            _historiques.Remove(historique);
            return Task.FromResult(true);
        }
        return Task.FromResult(false);
    }

    public Task<IEnumerable<HistoriqueScan>> ObtenirParConfigurationAsync(Guid configurationScanId)
    {
        var historiques = _historiques.Where(h => h.ConfigurationScanId == configurationScanId);
        return Task.FromResult(historiques);
    }

    public Task<IEnumerable<HistoriqueScan>> ObtenirParStatutAsync(string statut)
    {
        var historiques = _historiques.Where(h => h.Statut == statut);
        return Task.FromResult(historiques);
    }

    public Task<IEnumerable<HistoriqueScan>> ObtenirParPeriodeAsync(DateTime dateDebut, DateTime dateFin)
    {
        var historiques = _historiques.Where(h => h.DateDebut >= dateDebut && h.DateDebut <= dateFin);
        return Task.FromResult(historiques);
    }

    public Task<StatistiquesScan> ObtenirStatistiquesAsync()
    {
        var statistiques = new StatistiquesScan
        {
            TotalScans = _historiques.Count,
            ScansReussis = _historiques.Count(h => h.EstReussi),
            ScansEchoues = _historiques.Count(h => !h.EstReussi),
            ScansEnCours = _historiques.Count(h => h.Statut == "En cours"),
            TotalActifsDecouverts = _historiques.Sum(h => h.ActifsDecouverts),
            DureeMoyenne = 90, // 1.5 horas en minutos
            DernierScan = _historiques.Where(h => h.EstReussi).Max(h => h.DateFin)
        };
        return Task.FromResult(statistiques);
    }

    public Task<bool> AnnulerScanAsync(Guid historiqueScanId)
    {
        var historique = _historiques.FirstOrDefault(h => h.Id == historiqueScanId);
        if (historique != null && historique.Statut == "En cours")
        {
            historique.Statut = "Annulé";
            return Task.FromResult(true);
        }
        return Task.FromResult(false);
    }

    public Task<HistoriqueScan> MettreAJourAsync(HistoriqueScan entite)
    {
        if (entite.Id == Guid.Empty)
        {
            throw new ArgumentException("L'identifiant de l'entité ne peut pas être vide.", nameof(entite));
        }

        return MettreAJourAsync(entite.Id, entite);
    }
}
