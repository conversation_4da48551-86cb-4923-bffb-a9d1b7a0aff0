using DataHubGatineau.Web.Models;
using DataHubGatineau.Web.Services.Interfaces;

namespace DataHubGatineau.Web.Services.MockServices;

/// <summary>
/// Service simulé pour les produits de données.
/// </summary>
public class MockProduitDonneesService : IProduitDonneesService
{
    private static readonly List<ProduitDonnees> _produits = new();
    private static readonly List<ActifDonnees> _actifsDonnees = new();

    static MockProduitDonneesService()
    {
        InitialiserDonnees();
    }

    private static void InitialiserDonnees()
    {
        // Initialiser quelques actifs de données pour les tests
        _actifsDonnees.AddRange(new[]
        {
            new ActifDonnees
            {
                Id = Guid.Parse("306267a0-e07f-48bc-9c93-efda5e5576ae"),
                Nom = "dbo.Commandes",
                Description = "Table des commandes clients",
                DateCreation = DateTime.Now.AddDays(-30),
                DateModification = DateTime.Now.AddDays(-1)
            },
            new ActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "dbo.Clients",
                Description = "Table des clients",
                DateCreation = DateTime.Now.AddDays(-25),
                DateModification = DateTime.Now.AddDays(-2)
            },
            new ActifDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "dbo.Produits",
                Description = "Table des produits",
                DateCreation = DateTime.Now.AddDays(-20),
                DateModification = DateTime.Now.AddDays(-3)
            }
        });

        // Initialiser quelques produits de données
        _produits.AddRange(new[]
        {
            new ProduitDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Interaction citoyenne",
                Description = "Données d'interaction avec les citoyens",
                DateCreation = DateTime.Now.AddDays(-15),
                DateModification = DateTime.Now.AddDays(-1),
                ActifsDonneesIds = new List<Guid>(),
                DomaineGouvernanceId = Guid.NewGuid()
            },
            new ProduitDonnees
            {
                Id = Guid.NewGuid(),
                Nom = "Gestion commerciale",
                Description = "Données de gestion commerciale",
                DateCreation = DateTime.Now.AddDays(-10),
                DateModification = DateTime.Now.AddDays(-2),
                ActifsDonneesIds = new List<Guid>(),
                DomaineGouvernanceId = Guid.NewGuid()
            }
        });
    }

    public async Task<IEnumerable<ProduitDonnees>> ObtenirTousAsync()
    {
        await Task.Delay(200);
        return _produits;
    }

    public async Task<ProduitDonnees?> ObtenirParIdAsync(Guid id)
    {
        await Task.Delay(100);
        return _produits.FirstOrDefault(p => p.Id == id);
    }

    public async Task<ProduitDonnees> AjouterAsync(ProduitDonnees produit)
    {
        await Task.Delay(200);
        produit.Id = Guid.NewGuid();
        produit.DateCreation = DateTime.Now;
        produit.DateModification = DateTime.Now;
        if (produit.ActifsDonneesIds == null)
            produit.ActifsDonneesIds = new List<Guid>();
        
        _produits.Add(produit);
        return produit;
    }

    public async Task<ProduitDonnees> MettreAJourAsync(Guid id, ProduitDonnees produit)
    {
        await Task.Delay(200);
        var existingProduit = _produits.FirstOrDefault(p => p.Id == id);
        if (existingProduit == null)
            throw new KeyNotFoundException($"Produit de données avec ID {id} non trouvé");

        existingProduit.Nom = produit.Nom;
        existingProduit.Description = produit.Description;
        existingProduit.DomaineGouvernanceId = produit.DomaineGouvernanceId;
        existingProduit.DateModification = DateTime.Now;
        
        return existingProduit;
    }

    public async Task<bool> SupprimerAsync(Guid id)
    {
        await Task.Delay(100);
        var produit = _produits.FirstOrDefault(p => p.Id == id);
        if (produit == null)
            return false;

        _produits.Remove(produit);
        return true;
    }

    public async Task<IEnumerable<ProduitDonnees>> RechercherAsync(string motCle)
    {
        await Task.Delay(200);
        if (string.IsNullOrWhiteSpace(motCle))
            return _produits;

        return _produits.Where(p => 
            p.Nom.Contains(motCle, StringComparison.OrdinalIgnoreCase) ||
            (p.Description?.Contains(motCle, StringComparison.OrdinalIgnoreCase) ?? false));
    }

    public async Task<bool> AjouterActifDonneesAsync(Guid produitDonneesId, Guid actifDonneesId)
    {
        await Task.Delay(100);
        
        var produit = _produits.FirstOrDefault(p => p.Id == produitDonneesId);
        if (produit == null)
        {
            Console.WriteLine($"Produit avec ID {produitDonneesId} non trouvé");
            return false;
        }

        var actif = _actifsDonnees.FirstOrDefault(a => a.Id == actifDonneesId);
        if (actif == null)
        {
            Console.WriteLine($"Actif avec ID {actifDonneesId} non trouvé");
            return false;
        }

        if (produit.ActifsDonneesIds == null)
            produit.ActifsDonneesIds = new List<Guid>();

        if (!produit.ActifsDonneesIds.Contains(actifDonneesId))
        {
            produit.ActifsDonneesIds.Add(actifDonneesId);
            produit.DateModification = DateTime.Now;
            Console.WriteLine($"Actif {actifDonneesId} ajouté au produit {produitDonneesId}");
            return true;
        }

        Console.WriteLine($"Actif {actifDonneesId} déjà associé au produit {produitDonneesId}");
        return true;
    }

    public async Task<bool> SupprimerActifDonneesAsync(Guid produitDonneesId, Guid actifDonneesId)
    {
        await Task.Delay(100);
        
        var produit = _produits.FirstOrDefault(p => p.Id == produitDonneesId);
        if (produit == null || produit.ActifsDonneesIds == null)
            return false;

        var removed = produit.ActifsDonneesIds.Remove(actifDonneesId);
        if (removed)
        {
            produit.DateModification = DateTime.Now;
        }
        
        return removed;
    }

    public async Task<IEnumerable<ProduitDonnees>> ObtenirParActifDonneesAsync(Guid actifDonneesId)
    {
        await Task.Delay(100);
        return _produits.Where(p => p.ActifsDonneesIds?.Contains(actifDonneesId) ?? false);
    }

    public async Task<IEnumerable<ActifDonnees>> ObtenirActifsDonneesAsync(Guid produitDonneesId)
    {
        await Task.Delay(100);
        
        var produit = _produits.FirstOrDefault(p => p.Id == produitDonneesId);
        if (produit?.ActifsDonneesIds == null)
            return Enumerable.Empty<ActifDonnees>();

        return _actifsDonnees.Where(a => produit.ActifsDonneesIds.Contains(a.Id));
    }

    public async Task<IEnumerable<ProduitDonnees>> ObtenirParDomaineGouvernanceAsync(Guid domaineGouvernanceId)
    {
        await Task.Delay(100);
        return _produits.Where(p => p.DomaineGouvernanceId == domaineGouvernanceId);
    }

    public async Task<IEnumerable<ProduitDonnees>> ObtenirParProprietaireAsync(string proprietaire)
    {
        await Task.Delay(100);
        if (string.IsNullOrWhiteSpace(proprietaire))
            return Enumerable.Empty<ProduitDonnees>();

        return _produits.Where(p =>
            !string.IsNullOrEmpty(p.CreePar) &&
            p.CreePar.Contains(proprietaire, StringComparison.OrdinalIgnoreCase));
    }

    public async Task<ProduitDonnees> MettreAJourAsync(ProduitDonnees produit)
    {
        if (produit.Id == Guid.Empty)
            throw new ArgumentException("L'identifiant du produit ne peut pas être vide.", nameof(produit));

        return await MettreAJourAsync(produit.Id, produit);
    }
}
