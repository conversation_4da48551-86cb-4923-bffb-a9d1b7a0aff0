using DataHubGatineau.Web.Models.RiskManagement;
using DataHubGatineau.Web.Services.Interfaces;

namespace DataHubGatineau.Web.Services.MockServices;

/// <summary>
/// Service simulé pour la gestion des risques d'actifs de données.
/// </summary>
public class MockRisqueActifDonneesService : IRisqueActifDonneesService
{
    private static readonly List<RisqueActifDonnees> _risques = new();

    static MockRisqueActifDonneesService()
    {
        InitialiserDonnees();
    }

    private static void InitialiserDonnees()
    {
        var actifId1 = Guid.Parse("306267a0-e07f-48bc-9c93-efda5e5576ae");
        var actifId2 = Guid.NewGuid();
        var actifId3 = Guid.NewGuid();

        _risques.AddRange(new[]
        {
            new RisqueActifDonnees
            {
                Id = Guid.NewGuid(),
                ActifDonneesId = actifId1,
                TypeRisque = TypeRisque.Confidentialite,
                NiveauRisque = NiveauRisque.Eleve,
                Description = "Risque d'accès non autorisé aux données sensibles des clients",
                Impact = "Violation de la confidentialité, amendes RGPD potentielles",
                Probabilite = 4,
                ImpactScore = 5,
                MesuresMitigation = "Mise en place de contrôles d'accès renforcés, chiffrement des données",
                Statut = StatutRisque.EnTraitement,
                ResponsableId = "<EMAIL>",
                ResponsableNom = "Administrateur Sécurité",
                DateIdentification = DateTime.Now.AddDays(-15),
                DateLimite = DateTime.Now.AddDays(30),
                DateCreation = DateTime.Now.AddDays(-15),
                CreePar = "Système d'évaluation automatique"
            },
            new RisqueActifDonnees
            {
                Id = Guid.NewGuid(),
                ActifDonneesId = actifId1,
                TypeRisque = TypeRisque.Integrite,
                NiveauRisque = NiveauRisque.Modere,
                Description = "Risque de corruption des données lors des transferts",
                Impact = "Perte d'intégrité des données, décisions basées sur des données incorrectes",
                Probabilite = 2,
                ImpactScore = 4,
                MesuresMitigation = "Mise en place de checksums, validation des données",
                Statut = StatutRisque.Identifie,
                ResponsableId = "<EMAIL>",
                ResponsableNom = "Gestionnaire de Données",
                DateIdentification = DateTime.Now.AddDays(-10),
                DateLimite = DateTime.Now.AddDays(45),
                DateCreation = DateTime.Now.AddDays(-10),
                CreePar = "Marie Tremblay"
            },
            new RisqueActifDonnees
            {
                Id = Guid.NewGuid(),
                ActifDonneesId = actifId2,
                TypeRisque = TypeRisque.Disponibilite,
                NiveauRisque = NiveauRisque.Critique,
                Description = "Risque d'indisponibilité prolongée du système de base de données",
                Impact = "Arrêt des opérations critiques, perte de revenus",
                Probabilite = 3,
                ImpactScore = 5,
                MesuresMitigation = "Mise en place de redondance, plan de continuité d'activité",
                Statut = StatutRisque.EnEvaluation,
                ResponsableId = "<EMAIL>",
                ResponsableNom = "Administrateur Sécurité",
                DateIdentification = DateTime.Now.AddDays(-5),
                DateLimite = DateTime.Now.AddDays(15),
                DateCreation = DateTime.Now.AddDays(-5),
                CreePar = "Système de monitoring"
            },
            new RisqueActifDonnees
            {
                Id = Guid.NewGuid(),
                ActifDonneesId = actifId2,
                TypeRisque = TypeRisque.Conformite,
                NiveauRisque = NiveauRisque.Eleve,
                Description = "Non-conformité aux exigences RGPD pour la rétention des données",
                Impact = "Amendes réglementaires, atteinte à la réputation",
                Probabilite = 4,
                ImpactScore = 4,
                MesuresMitigation = "Mise en place de politiques de rétention automatisées",
                Statut = StatutRisque.Mitige,
                ResponsableId = "<EMAIL>",
                ResponsableNom = "Responsable Conformité",
                DateIdentification = DateTime.Now.AddDays(-20),
                DateLimite = DateTime.Now.AddDays(-5),
                DateResolution = DateTime.Now.AddDays(-2),
                DateCreation = DateTime.Now.AddDays(-20),
                CreePar = "Audit de conformité"
            },
            new RisqueActifDonnees
            {
                Id = Guid.NewGuid(),
                ActifDonneesId = actifId3,
                TypeRisque = TypeRisque.FuiteDonnees,
                NiveauRisque = NiveauRisque.Critique,
                Description = "Risque de fuite de données via des accès privilégiés non surveillés",
                Impact = "Exposition de données sensibles, violation de la confidentialité",
                Probabilite = 5,
                ImpactScore = 5,
                MesuresMitigation = "Surveillance des accès privilégiés, audit des activités",
                Statut = StatutRisque.Identifie,
                ResponsableId = "<EMAIL>",
                ResponsableNom = "Équipe Sécurité",
                DateIdentification = DateTime.Now.AddDays(-3),
                DateLimite = DateTime.Now.AddDays(7),
                DateCreation = DateTime.Now.AddDays(-3),
                CreePar = "Analyse de sécurité"
            },
            new RisqueActifDonnees
            {
                Id = Guid.NewGuid(),
                ActifDonneesId = actifId3,
                TypeRisque = TypeRisque.Qualite,
                NiveauRisque = NiveauRisque.Faible,
                Description = "Risque de dégradation de la qualité des données due à des processus manuels",
                Impact = "Décisions basées sur des données de mauvaise qualité",
                Probabilite = 2,
                ImpactScore = 2,
                MesuresMitigation = "Automatisation des processus, contrôles qualité",
                Statut = StatutRisque.Accepte,
                ResponsableId = "<EMAIL>",
                ResponsableNom = "Responsable Qualité",
                DateIdentification = DateTime.Now.AddDays(-30),
                DateCreation = DateTime.Now.AddDays(-30),
                CreePar = "Contrôle qualité automatique"
            }
        });
    }

    public async Task<IEnumerable<RisqueActifDonnees>> ObtenirTousAsync()
    {
        await Task.Delay(200);
        return _risques.Where(r => !r.EstArchive);
    }

    public async Task<RisqueActifDonnees?> ObtenirParIdAsync(Guid id)
    {
        await Task.Delay(100);
        return _risques.FirstOrDefault(r => r.Id == id);
    }

    public async Task<RisqueActifDonnees> AjouterAsync(RisqueActifDonnees risque)
    {
        await Task.Delay(200);
        risque.Id = Guid.NewGuid();
        risque.DateCreation = DateTime.Now;
        risque.DateModification = DateTime.Now;
        _risques.Add(risque);
        return risque;
    }

    public async Task<RisqueActifDonnees> MettreAJourAsync(Guid id, RisqueActifDonnees risque)
    {
        await Task.Delay(200);
        var existingRisque = _risques.FirstOrDefault(r => r.Id == id);
        if (existingRisque == null)
            throw new KeyNotFoundException($"Risque avec ID {id} non trouvé");

        existingRisque.TypeRisque = risque.TypeRisque;
        existingRisque.NiveauRisque = risque.NiveauRisque;
        existingRisque.Description = risque.Description;
        existingRisque.Impact = risque.Impact;
        existingRisque.Probabilite = risque.Probabilite;
        existingRisque.ImpactScore = risque.ImpactScore;
        existingRisque.MesuresMitigation = risque.MesuresMitigation;
        existingRisque.Statut = risque.Statut;
        existingRisque.ResponsableId = risque.ResponsableId;
        existingRisque.ResponsableNom = risque.ResponsableNom;
        existingRisque.DateLimite = risque.DateLimite;
        existingRisque.DateResolution = risque.DateResolution;
        existingRisque.Commentaires = risque.Commentaires;
        existingRisque.DateModification = DateTime.Now;
        existingRisque.ModifiePar = risque.ModifiePar;

        return existingRisque;
    }

    public async Task<RisqueActifDonnees> MettreAJourAsync(RisqueActifDonnees risque)
    {
        if (risque.Id == Guid.Empty)
            throw new ArgumentException("L'identifiant du risque ne peut pas être vide.", nameof(risque));

        return await MettreAJourAsync(risque.Id, risque);
    }

    public async Task<bool> SupprimerAsync(Guid id)
    {
        await Task.Delay(100);
        var risque = _risques.FirstOrDefault(r => r.Id == id);
        if (risque == null)
            return false;

        _risques.Remove(risque);
        return true;
    }

    public async Task<IEnumerable<RisqueActifDonnees>> ObtenirParActifDonneesAsync(Guid actifDonneesId)
    {
        await Task.Delay(100);
        return _risques.Where(r => r.ActifDonneesId == actifDonneesId && !r.EstArchive);
    }

    public async Task<IEnumerable<RisqueActifDonnees>> ObtenirParTypeRisqueAsync(TypeRisque typeRisque)
    {
        await Task.Delay(100);
        return _risques.Where(r => r.TypeRisque == typeRisque && !r.EstArchive);
    }

    public async Task<IEnumerable<RisqueActifDonnees>> ObtenirParNiveauRisqueAsync(NiveauRisque niveauRisque)
    {
        await Task.Delay(100);
        return _risques.Where(r => r.NiveauRisque == niveauRisque && !r.EstArchive);
    }

    public async Task<IEnumerable<RisqueActifDonnees>> ObtenirParStatutAsync(StatutRisque statut)
    {
        await Task.Delay(100);
        return _risques.Where(r => r.Statut == statut && !r.EstArchive);
    }

    public async Task<IEnumerable<RisqueActifDonnees>> ObtenirParResponsableAsync(string responsableId)
    {
        await Task.Delay(100);
        return _risques.Where(r => r.ResponsableId == responsableId && !r.EstArchive);
    }

    public async Task<IEnumerable<RisqueActifDonnees>> ObtenirRisquesEnRetardAsync()
    {
        await Task.Delay(100);
        return _risques.Where(r => r.DateLimite.HasValue && 
                                  r.DateLimite < DateTime.Now && 
                                  r.Statut != StatutRisque.Resolu && 
                                  r.Statut != StatutRisque.Mitige &&
                                  !r.EstArchive);
    }

    public async Task<IEnumerable<RisqueActifDonnees>> ObtenirRisquesCritiquesAsync()
    {
        await Task.Delay(100);
        return _risques.Where(r => (r.NiveauRisque == NiveauRisque.Eleve || r.NiveauRisque == NiveauRisque.Critique) && 
                                  !r.EstArchive);
    }

    public async Task<int> CalculerScoreRisqueGlobalAsync(Guid actifDonneesId)
    {
        await Task.Delay(100);
        var risquesActif = _risques.Where(r => r.ActifDonneesId == actifDonneesId && 
                                              r.Statut != StatutRisque.Resolu && 
                                              !r.EstArchive);
        return risquesActif.Sum(r => r.ScoreRisque);
    }

    public async Task<Dictionary<TypeRisque, int>> ObtenirStatistiquesParTypeAsync()
    {
        await Task.Delay(100);
        return _risques.Where(r => !r.EstArchive)
                      .GroupBy(r => r.TypeRisque)
                      .ToDictionary(g => g.Key, g => g.Count());
    }

    public async Task<Dictionary<NiveauRisque, int>> ObtenirStatistiquesParNiveauAsync()
    {
        await Task.Delay(100);
        return _risques.Where(r => !r.EstArchive)
                      .GroupBy(r => r.NiveauRisque)
                      .ToDictionary(g => g.Key, g => g.Count());
    }

    public async Task<Dictionary<StatutRisque, int>> ObtenirStatistiquesParStatutAsync()
    {
        await Task.Delay(100);
        return _risques.Where(r => !r.EstArchive)
                      .GroupBy(r => r.Statut)
                      .ToDictionary(g => g.Key, g => g.Count());
    }

    public async Task<RisqueActifDonnees> MettreAJourStatutAsync(Guid risqueId, StatutRisque nouveauStatut, string? commentaire = null)
    {
        await Task.Delay(100);
        var risque = _risques.FirstOrDefault(r => r.Id == risqueId);
        if (risque == null)
            throw new KeyNotFoundException($"Risque avec ID {risqueId} non trouvé");

        risque.Statut = nouveauStatut;
        risque.DateModification = DateTime.Now;
        
        if (nouveauStatut == StatutRisque.Resolu)
            risque.DateResolution = DateTime.Now;

        if (!string.IsNullOrEmpty(commentaire))
            risque.Commentaires = commentaire;

        return risque;
    }

    public async Task<RisqueActifDonnees> AssignerResponsableAsync(Guid risqueId, string responsableId, string responsableNom)
    {
        await Task.Delay(100);
        var risque = _risques.FirstOrDefault(r => r.Id == risqueId);
        if (risque == null)
            throw new KeyNotFoundException($"Risque avec ID {risqueId} non trouvé");

        risque.ResponsableId = responsableId;
        risque.ResponsableNom = responsableNom;
        risque.DateModification = DateTime.Now;

        return risque;
    }

    public async Task<bool> ArchiverAsync(Guid risqueId)
    {
        await Task.Delay(100);
        var risque = _risques.FirstOrDefault(r => r.Id == risqueId);
        if (risque == null)
            return false;

        risque.EstArchive = true;
        risque.DateModification = DateTime.Now;
        return true;
    }

    public async Task<IEnumerable<RisqueActifDonnees>> RechercherAsync(string motsCles)
    {
        await Task.Delay(200);
        if (string.IsNullOrWhiteSpace(motsCles))
            return _risques.Where(r => !r.EstArchive);

        return _risques.Where(r => !r.EstArchive &&
                                  (r.Description.Contains(motsCles, StringComparison.OrdinalIgnoreCase) ||
                                   (r.Impact?.Contains(motsCles, StringComparison.OrdinalIgnoreCase) ?? false) ||
                                   (r.MesuresMitigation?.Contains(motsCles, StringComparison.OrdinalIgnoreCase) ?? false)));
    }

    public async Task<RapportRisques> GenererRapportRisquesAsync(Guid actifDonneesId)
    {
        await Task.Delay(300);
        var risquesActif = _risques.Where(r => r.ActifDonneesId == actifDonneesId && !r.EstArchive).ToList();
        
        return new RapportRisques
        {
            ActifDonneesId = actifDonneesId,
            NomActif = "Actif de données",
            NombreRisquesTotal = risquesActif.Count,
            NombreRisquesCritiques = risquesActif.Count(r => r.NiveauRisque == NiveauRisque.Critique),
            NombreRisquesEleves = risquesActif.Count(r => r.NiveauRisque == NiveauRisque.Eleve),
            NombreRisquesMoyens = risquesActif.Count(r => r.NiveauRisque == NiveauRisque.Modere),
            NombreRisquesFaibles = risquesActif.Count(r => r.NiveauRisque == NiveauRisque.Faible || r.NiveauRisque == NiveauRisque.TresFaible),
            ScoreRisqueGlobal = risquesActif.Where(r => r.Statut != StatutRisque.Resolu).Sum(r => r.ScoreRisque),
            RisquesActifs = risquesActif.Where(r => r.Statut != StatutRisque.Resolu).ToList(),
            RisquesResolus = risquesActif.Where(r => r.Statut == StatutRisque.Resolu).ToList(),
            GenerePar = "Système de gestion des risques"
        };
    }

    public async Task<IEnumerable<RisqueActifDonnees>> EvaluerRisquesAutomatiquementAsync(Guid actifDonneesId)
    {
        await Task.Delay(500);
        // Simulation d'une évaluation automatique basée sur les caractéristiques de l'actif
        var risquesDetectes = new List<RisqueActifDonnees>();

        // Exemple de risques détectés automatiquement
        risquesDetectes.Add(new RisqueActifDonnees
        {
            Id = Guid.NewGuid(),
            ActifDonneesId = actifDonneesId,
            TypeRisque = TypeRisque.AccesNonAutorise,
            NiveauRisque = NiveauRisque.Modere,
            Description = "Risque détecté automatiquement : Accès non restreint détecté",
            Impact = "Accès potentiel non autorisé aux données",
            Probabilite = 3,
            ImpactScore = 3,
            Statut = StatutRisque.Identifie,
            DateIdentification = DateTime.Now,
            DateCreation = DateTime.Now,
            CreePar = "Évaluation automatique"
        });

        return risquesDetectes;
    }
}
