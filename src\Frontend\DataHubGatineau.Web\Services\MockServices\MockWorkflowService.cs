using DataHubGatineau.Web.Models.Workflow;
using DataHubGatineau.Web.Services.Interfaces;

namespace DataHubGatineau.Web.Services.MockServices;

/// <summary>
/// Service mock pour les opérations sur les workflows.
/// </summary>
public class MockWorkflowService : IWorkflowService
{
    private readonly List<WorkflowDefinition> _definitions;
    private readonly List<WorkflowInstance> _instances;
    private readonly List<WorkflowEtapeHistorique> _etapesHistorique;
    private readonly List<WorkflowCommentaire> _commentaires;

    public MockWorkflowService()
    {
        _definitions = CreerDefinitionsSimulees();
        _instances = CreerInstancesSimulees();
        _etapesHistorique = CreerEtapesHistoriqueSimulees();
        _commentaires = CreerCommentairesSimules();
    }

    public async Task<IEnumerable<WorkflowDefinition>> ObtenirTousAsync()
    {
        await Task.Delay(300);
        return _definitions;
    }

    public async Task<WorkflowDefinition?> ObtenirParIdAsync(Guid id)
    {
        await Task.Delay(200);
        return _definitions.FirstOrDefault(d => d.Id == id);
    }

    public async Task<WorkflowDefinition> AjouterAsync(WorkflowDefinition entite)
    {
        await Task.Delay(500);
        entite.Id = Guid.NewGuid();
        entite.DateCreation = DateTime.Now;
        entite.DateModification = DateTime.Now;
        _definitions.Add(entite);
        return entite;
    }

    public async Task<WorkflowDefinition> MettreAJourAsync(Guid id, WorkflowDefinition entite)
    {
        await Task.Delay(500);
        var definition = _definitions.FirstOrDefault(d => d.Id == id);
        if (definition != null)
        {
            definition.Nom = entite.Nom;
            definition.Description = entite.Description;
            definition.Type = entite.Type;
            definition.EstActif = entite.EstActif;
            definition.DefinitionJson = entite.DefinitionJson;
            definition.DateModification = DateTime.Now;
            definition.Version++;
        }
        return definition ?? entite;
    }

    public async Task<bool> SupprimerAsync(Guid id)
    {
        await Task.Delay(300);
        var definition = _definitions.FirstOrDefault(d => d.Id == id);
        if (definition != null)
        {
            _definitions.Remove(definition);
            return true;
        }
        return false;
    }

    public async Task<bool> ExisteAsync(Guid id)
    {
        await Task.Delay(100);
        return _definitions.Any(d => d.Id == id);
    }

    public async Task<IEnumerable<WorkflowDefinition>> ObtenirDefinitionsParTypeAsync(TypeWorkflow type)
    {
        await Task.Delay(200);
        return _definitions.Where(d => d.Type == type);
    }

    public async Task<IEnumerable<WorkflowInstance>> ObtenirToutesInstancesAsync()
    {
        await Task.Delay(300);
        return _instances;
    }

    public async Task<WorkflowInstance?> ObtenirInstanceParIdAsync(Guid id)
    {
        await Task.Delay(200);
        return _instances.FirstOrDefault(i => i.Id == id);
    }

    public async Task<IEnumerable<WorkflowInstance>> ObtenirInstancesParAssigneAsync(string assigneId)
    {
        await Task.Delay(250);
        return _instances.Where(i => i.AssigneId == assigneId && 
                                   i.Statut != WorkflowStatus.Complete && 
                                   i.Statut != WorkflowStatus.Annule);
    }

    public async Task<IEnumerable<WorkflowInstance>> ObtenirInstancesParInitiateurAsync(string initiateurId)
    {
        await Task.Delay(250);
        return _instances.Where(i => i.InitiateurId == initiateurId);
    }

    public async Task<WorkflowInstance> CreerInstanceAsync(Guid definitionId, string contexteJson, string initiateurId, string initiateurNom)
    {
        await Task.Delay(500);
        var instance = new WorkflowInstance
        {
            Id = Guid.NewGuid(),
            WorkflowDefinitionId = (int)definitionId.GetHashCode(),
            ContexteJson = contexteJson,
            InitiateurId = initiateurId,
            InitiateurNom = initiateurNom,
            Statut = WorkflowStatus.EnAttente,
            DateCreation = DateTime.Now,
            DateMiseAJour = DateTime.Now
        };
        _instances.Add(instance);
        return instance;
    }

    public async Task<WorkflowInstance> MettreAJourInstanceAsync(Guid instanceId, WorkflowStatus nouveauStatut, string? assigneId = null, string? assigneNom = null)
    {
        await Task.Delay(400);
        var instance = _instances.FirstOrDefault(i => i.Id == instanceId);
        if (instance != null)
        {
            instance.Statut = nouveauStatut;
            instance.DateMiseAJour = DateTime.Now;
            
            if (assigneId != null)
            {
                instance.AssigneId = assigneId;
                instance.AssigneNom = assigneNom;
            }

            if (nouveauStatut == WorkflowStatus.Complete)
            {
                instance.DateCompletion = DateTime.Now;
            }
        }
        return instance ?? throw new InvalidOperationException("Instance non trouvée");
    }

    public async Task<IEnumerable<WorkflowEtapeHistorique>> ObtenirHistoriqueEtapesAsync(Guid instanceId)
    {
        await Task.Delay(200);
        return _etapesHistorique.Where(e => e.WorkflowInstanceId == instanceId.GetHashCode())
                               .OrderBy(e => e.DateAction);
    }

    public async Task<WorkflowEtapeHistorique> AjouterEtapeHistoriqueAsync(Guid instanceId, string etapeNom, WorkflowStatus statut, string utilisateurId, string utilisateurNom, string? commentaire = null)
    {
        await Task.Delay(300);
        var etape = new WorkflowEtapeHistorique
        {
            Id = Guid.NewGuid(),
            WorkflowInstanceId = instanceId.GetHashCode(),
            NomEtape = etapeNom,
            Statut = statut,
            UtilisateurId = utilisateurId,
            UtilisateurNom = utilisateurNom,
            Commentaire = commentaire,
            DateAction = DateTime.Now
        };
        _etapesHistorique.Add(etape);
        return etape;
    }

    public async Task<IEnumerable<WorkflowCommentaire>> ObtenirCommentairesAsync(Guid instanceId)
    {
        await Task.Delay(200);
        return _commentaires.Where(c => c.WorkflowInstanceId == instanceId)
                           .OrderBy(c => c.DateCreation);
    }

    public async Task<WorkflowCommentaire> AjouterCommentaireAsync(Guid instanceId, string contenu, string utilisateurId, string utilisateurNom)
    {
        await Task.Delay(300);
        var commentaire = new WorkflowCommentaire
        {
            Id = Guid.NewGuid(),
            WorkflowInstanceId = instanceId,
            Contenu = contenu,
            UtilisateurId = utilisateurId,
            UtilisateurNom = utilisateurNom,
            DateCreation = DateTime.Now
        };
        _commentaires.Add(commentaire);
        return commentaire;
    }



    public async Task<WorkflowDefinition> ModifierAsync(WorkflowDefinition entite)
    {
        return await MettreAJourAsync(entite.Id, entite);
    }

    public async Task<IEnumerable<WorkflowInstance>> ObtenirInstancesParStatutAsync(WorkflowStatus statut)
    {
        await Task.Delay(200);
        return _instances.Where(i => i.Statut == statut);
    }

    public async Task<WorkflowInstance> MettreAJourStatutAsync(Guid instanceId, WorkflowStatus nouveauStatut, string utilisateurId, string utilisateurNom, string? commentaire = null)
    {
        await Task.Delay(400);
        var instance = _instances.FirstOrDefault(i => i.Id == instanceId);
        if (instance != null)
        {
            instance.Statut = nouveauStatut;
            instance.DateMiseAJour = DateTime.Now;

            if (nouveauStatut == WorkflowStatus.Complete)
            {
                instance.DateCompletion = DateTime.Now;
            }

            // Ajouter une étape à l'historique
            await AjouterEtapeHistoriqueAsync(instanceId, "Mise à jour du statut", nouveauStatut, utilisateurId, utilisateurNom, commentaire);
        }
        return instance ?? throw new InvalidOperationException("Instance non trouvée");
    }

    public async Task<WorkflowInstance> AssignerInstanceAsync(Guid instanceId, string assigneId, string assigneNom)
    {
        await Task.Delay(300);
        var instance = _instances.FirstOrDefault(i => i.Id == instanceId);
        if (instance != null)
        {
            instance.AssigneId = assigneId;
            instance.AssigneNom = assigneNom;
            instance.DateMiseAJour = DateTime.Now;
        }
        return instance ?? throw new InvalidOperationException("Instance non trouvée");
    }

    public async Task<bool> PeutExecuterActionAsync(Guid instanceId, string utilisateurId, string action)
    {
        await Task.Delay(100);
        var instance = _instances.FirstOrDefault(i => i.Id == instanceId);
        if (instance == null) return false;

        // Logique simplifiée pour la démo
        return instance.AssigneId == utilisateurId || instance.InitiateurId == utilisateurId;
    }

    public async Task<WorkflowInstance> ExecuterActionAsync(Guid instanceId, string action, string utilisateurId, string utilisateurNom, string? commentaire = null)
    {
        await Task.Delay(500);
        var instance = _instances.FirstOrDefault(i => i.Id == instanceId);
        if (instance == null)
            throw new InvalidOperationException("Instance non trouvée");

        // Logique simplifiée pour les actions
        switch (action.ToLower())
        {
            case "approuver":
                instance.Statut = WorkflowStatus.Approuve;
                break;
            case "rejeter":
                instance.Statut = WorkflowStatus.Rejete;
                break;
            case "completer":
                instance.Statut = WorkflowStatus.Complete;
                instance.DateCompletion = DateTime.Now;
                break;
            case "annuler":
                instance.Statut = WorkflowStatus.Annule;
                break;
            default:
                instance.Statut = WorkflowStatus.EnCours;
                break;
        }

        instance.DateMiseAJour = DateTime.Now;

        // Ajouter une étape à l'historique
        await AjouterEtapeHistoriqueAsync(instanceId, action, instance.Statut, utilisateurId, utilisateurNom, commentaire);

        return instance;
    }

    private List<WorkflowDefinition> CreerDefinitionsSimulees()
    {
        return new List<WorkflowDefinition>
        {
            new WorkflowDefinition
            {
                Id = Guid.Parse("11111111-1111-1111-1111-111111111111"),
                Nom = "Approbation d'actifs de données",
                Description = "Workflow pour l'approbation des nouveaux actifs de données avant publication",
                Type = TypeWorkflow.ApprobationActif,
                Version = 2,
                EstActif = true,
                DateCreation = DateTime.Now.AddDays(-30),
                DateModification = DateTime.Now.AddDays(-5),
                DefinitionJson = "{\"etapes\":[\"Soumission\",\"Révision\",\"Approbation\",\"Publication\"]}"
            },
            new WorkflowDefinition
            {
                Id = Guid.Parse("*************-2222-2222-************"),
                Nom = "Modification de métadonnées",
                Description = "Workflow pour la validation des modifications de métadonnées critiques",
                Type = TypeWorkflow.ModificationMetadonnee,
                Version = 1,
                EstActif = true,
                DateCreation = DateTime.Now.AddDays(-20),
                DateModification = DateTime.Now.AddDays(-20),
                DefinitionJson = "{\"etapes\":[\"Demande\",\"Validation\",\"Application\"]}"
            },
            new WorkflowDefinition
            {
                Id = Guid.Parse("*************-3333-3333-************"),
                Nom = "Certification de qualité",
                Description = "Workflow pour la certification de la qualité des données",
                Type = TypeWorkflow.CertificationQualite,
                Version = 1,
                EstActif = true,
                DateCreation = DateTime.Now.AddDays(-15),
                DateModification = DateTime.Now.AddDays(-15),
                DefinitionJson = "{\"etapes\":[\"Analyse\",\"Tests\",\"Certification\"]}"
            },
            new WorkflowDefinition
            {
                Id = Guid.Parse("*************-4444-4444-************"),
                Nom = "Demande d'accès aux données",
                Description = "Workflow pour les demandes d'accès aux données sensibles",
                Type = TypeWorkflow.DemandeAcces,
                Version = 3,
                EstActif = true,
                DateCreation = DateTime.Now.AddDays(-45),
                DateModification = DateTime.Now.AddDays(-2),
                DefinitionJson = "{\"etapes\":[\"Demande\",\"Évaluation\",\"Approbation\",\"Provisioning\"]}"
            },
            new WorkflowDefinition
            {
                Id = Guid.Parse("*************-5555-5555-************"),
                Nom = "Résolution de problème de qualité",
                Description = "Workflow pour la résolution des problèmes de qualité détectés",
                Type = TypeWorkflow.ResolutionProbleme,
                Version = 1,
                EstActif = false,
                DateCreation = DateTime.Now.AddDays(-10),
                DateModification = DateTime.Now.AddDays(-10),
                DefinitionJson = "{\"etapes\":[\"Identification\",\"Investigation\",\"Résolution\",\"Validation\"]}"
            }
        };
    }

    private List<WorkflowInstance> CreerInstancesSimulees()
    {
        return new List<WorkflowInstance>
        {
            new WorkflowInstance
            {
                Id = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
                WorkflowDefinitionId = "11111111-1111-1111-1111-111111111111".GetHashCode(),
                Statut = WorkflowStatus.EnAttenteApprobation,
                ContexteJson = "{\"actifId\":\"12345\",\"type\":\"Table\"}",
                InitiateurId = "user1",
                InitiateurNom = "Jean Dupont",
                AssigneId = "user2",
                AssigneNom = "Marie Tremblay",
                DateCreation = DateTime.Now.AddDays(-3),
                DateMiseAJour = DateTime.Now.AddHours(-2)
            },
            new WorkflowInstance
            {
                Id = Guid.Parse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"),
                WorkflowDefinitionId = "*************-2222-2222-************".GetHashCode(),
                Statut = WorkflowStatus.EnCours,
                ContexteJson = "{\"metadonneeId\":\"67890\",\"champ\":\"description\"}",
                InitiateurId = "user3",
                InitiateurNom = "Pierre Martin",
                AssigneId = "user2",
                AssigneNom = "Marie Tremblay",
                DateCreation = DateTime.Now.AddDays(-1),
                DateMiseAJour = DateTime.Now.AddMinutes(-30)
            },
            new WorkflowInstance
            {
                Id = Guid.Parse("cccccccc-cccc-cccc-cccc-cccccccccccc"),
                WorkflowDefinitionId = "*************-4444-4444-************".GetHashCode(),
                Statut = WorkflowStatus.EnAttente,
                ContexteJson = "{\"ressourceId\":\"54321\",\"niveau\":\"confidentiel\"}",
                InitiateurId = "user4",
                InitiateurNom = "Sophie Leblanc",
                DateCreation = DateTime.Now.AddHours(-6),
                DateMiseAJour = DateTime.Now.AddHours(-6)
            },
            new WorkflowInstance
            {
                Id = Guid.Parse("dddddddd-dddd-dddd-dddd-dddddddddddd"),
                WorkflowDefinitionId = "11111111-1111-1111-1111-111111111111".GetHashCode(),
                Statut = WorkflowStatus.Complete,
                ContexteJson = "{\"actifId\":\"98765\",\"type\":\"Vue\"}",
                InitiateurId = "user1",
                InitiateurNom = "Jean Dupont",
                AssigneId = "user2",
                AssigneNom = "Marie Tremblay",
                DateCreation = DateTime.Now.AddDays(-7),
                DateMiseAJour = DateTime.Now.AddDays(-5),
                DateCompletion = DateTime.Now.AddDays(-5)
            }
        };
    }

    private List<WorkflowEtapeHistorique> CreerEtapesHistoriqueSimulees()
    {
        return new List<WorkflowEtapeHistorique>
        {
            new WorkflowEtapeHistorique
            {
                Id = Guid.NewGuid(),
                WorkflowInstanceId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa").GetHashCode(),
                NomEtape = "Soumission",
                Statut = WorkflowStatus.EnAttente,
                UtilisateurId = "user1",
                UtilisateurNom = "Jean Dupont",
                Commentaire = "Soumission initiale de l'actif de données",
                DateAction = DateTime.Now.AddDays(-3)
            },
            new WorkflowEtapeHistorique
            {
                Id = Guid.NewGuid(),
                WorkflowInstanceId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa").GetHashCode(),
                NomEtape = "Révision",
                Statut = WorkflowStatus.EnCours,
                UtilisateurId = "user2",
                UtilisateurNom = "Marie Tremblay",
                Commentaire = "Révision en cours, quelques ajustements nécessaires",
                DateAction = DateTime.Now.AddDays(-2)
            },
            new WorkflowEtapeHistorique
            {
                Id = Guid.NewGuid(),
                WorkflowInstanceId = Guid.Parse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb").GetHashCode(),
                NomEtape = "Demande",
                Statut = WorkflowStatus.EnAttente,
                UtilisateurId = "user3",
                UtilisateurNom = "Pierre Martin",
                Commentaire = "Demande de modification de la description",
                DateAction = DateTime.Now.AddDays(-1)
            },
            new WorkflowEtapeHistorique
            {
                Id = Guid.NewGuid(),
                WorkflowInstanceId = Guid.Parse("dddddddd-dddd-dddd-dddd-dddddddddddd").GetHashCode(),
                NomEtape = "Soumission",
                Statut = WorkflowStatus.EnAttente,
                UtilisateurId = "user1",
                UtilisateurNom = "Jean Dupont",
                Commentaire = "Soumission de la vue pour approbation",
                DateAction = DateTime.Now.AddDays(-7)
            },
            new WorkflowEtapeHistorique
            {
                Id = Guid.NewGuid(),
                WorkflowInstanceId = Guid.Parse("dddddddd-dddd-dddd-dddd-dddddddddddd").GetHashCode(),
                NomEtape = "Approbation",
                Statut = WorkflowStatus.Approuve,
                UtilisateurId = "user2",
                UtilisateurNom = "Marie Tremblay",
                Commentaire = "Approuvé après révision",
                DateAction = DateTime.Now.AddDays(-5)
            }
        };
    }

    private List<WorkflowCommentaire> CreerCommentairesSimules()
    {
        return new List<WorkflowCommentaire>
        {
            new WorkflowCommentaire
            {
                Id = Guid.NewGuid(),
                WorkflowInstanceId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
                Contenu = "Les métadonnées semblent complètes, mais il faudrait vérifier la classification de sécurité.",
                UtilisateurId = "user2",
                UtilisateurNom = "Marie Tremblay",
                DateCreation = DateTime.Now.AddDays(-2).AddHours(-3)
            },
            new WorkflowCommentaire
            {
                Id = Guid.NewGuid(),
                WorkflowInstanceId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa"),
                Contenu = "Classification mise à jour selon les standards de sécurité.",
                UtilisateurId = "user1",
                UtilisateurNom = "Jean Dupont",
                DateCreation = DateTime.Now.AddDays(-2)
            },
            new WorkflowCommentaire
            {
                Id = Guid.NewGuid(),
                WorkflowInstanceId = Guid.Parse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb"),
                Contenu = "La nouvelle description est plus claire et précise.",
                UtilisateurId = "user2",
                UtilisateurNom = "Marie Tremblay",
                DateCreation = DateTime.Now.AddHours(-1)
            }
        };
    }
}
