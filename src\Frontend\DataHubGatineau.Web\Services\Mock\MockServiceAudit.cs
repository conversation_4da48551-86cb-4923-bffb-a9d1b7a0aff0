using DataHubGatineau.Web.Models.Auditoria;
using DataHubGatineau.Web.Services.Interfaces;
using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;
using System.Text.Json;

namespace DataHubGatineau.Web.Services.Mock
{
    /// <summary>
    /// Service mock pour l'audit - stockage en mémoire pour les tests
    /// </summary>
    public class MockServiceAudit : IServiceAudit
    {
        private static readonly List<EntreeAudit> _audits = new();
        private readonly ILogger<MockServiceAudit> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly AuthenticationStateProvider _authStateProvider;
        private readonly string _correlationId = Guid.NewGuid().ToString();

        static MockServiceAudit()
        {
            InitialiserDonneesAuditMock();
        }

        private readonly JsonSerializerOptions _jsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            WriteIndented = false
        };

        private static void InitialiserDonneesAuditMock()
        {
            var baseDate = DateTime.UtcNow;

            // Ajouter quelques entrées d'audit de test
            _audits.AddRange(new[]
            {
                new EntreeAudit
                {
                    Id = Guid.NewGuid(),
                    Action = "Connexion utilisateur réussie",
                    TypeEntite = "Utilisateur",
                    EntiteId = Guid.NewGuid(),
                    NomEntite = "<EMAIL>",
                    TypeAction = TypeActionAudit.Connexion,
                    NiveauGravite = NiveauGraviteAudit.Information,
                    Categorie = CategorieAudit.Securite,
                    UtilisateurId = "admin",
                    NomUtilisateur = "<EMAIL>",
                    NomCompletUtilisateur = "Admin Système",
                    DateAction = baseDate.AddMinutes(-30),
                    AdresseIP = "127.0.0.1",
                    Commentaire = "Connexion réussie",
                    Contexte = "Authentification"
                },
                new EntreeAudit
                {
                    Id = Guid.NewGuid(),
                    Action = "Création Utilisateur",
                    TypeEntite = "Utilisateur",
                    EntiteId = Guid.NewGuid(),
                    NomEntite = "<EMAIL>",
                    TypeAction = TypeActionAudit.Creation,
                    NiveauGravite = NiveauGraviteAudit.Information,
                    Categorie = CategorieAudit.GouvernanceDonnees,
                    UtilisateurId = "admin",
                    NomUtilisateur = "<EMAIL>",
                    NomCompletUtilisateur = "Admin Système",
                    DateAction = baseDate.AddMinutes(-25),
                    AdresseIP = "127.0.0.1",
                    Commentaire = "Création d'un nouvel utilisateur",
                    ValeursApres = "{\"Nom\":\"Nouveau\",\"Prenom\":\"Utilisateur\",\"Courriel\":\"<EMAIL>\"}"
                },
                new EntreeAudit
                {
                    Id = Guid.NewGuid(),
                    Action = "Modification Utilisateur",
                    TypeEntite = "Utilisateur",
                    EntiteId = Guid.NewGuid(),
                    NomEntite = "<EMAIL>",
                    TypeAction = TypeActionAudit.Modification,
                    NiveauGravite = NiveauGraviteAudit.Information,
                    Categorie = CategorieAudit.GouvernanceDonnees,
                    UtilisateurId = "admin",
                    NomUtilisateur = "<EMAIL>",
                    NomCompletUtilisateur = "Admin Système",
                    DateAction = baseDate.AddMinutes(-20),
                    AdresseIP = "127.0.0.1",
                    Commentaire = "Modification des informations utilisateur",
                    ValeursAvant = "{\"Telephone\":\"\",\"Poste\":\"\"}",
                    ValeursApres = "{\"Telephone\":\"(*************\",\"Poste\":\"1002\"}"
                },
                new EntreeAudit
                {
                    Id = Guid.NewGuid(),
                    Action = "Création Actif de données",
                    TypeEntite = "ActifDonnees",
                    EntiteId = Guid.NewGuid(),
                    NomEntite = "Base de données clients",
                    TypeAction = TypeActionAudit.Creation,
                    NiveauGravite = NiveauGraviteAudit.Information,
                    Categorie = CategorieAudit.GouvernanceDonnees,
                    UtilisateurId = "gestionnaire",
                    NomUtilisateur = "<EMAIL>",
                    NomCompletUtilisateur = "Jean Dupont",
                    DateAction = baseDate.AddMinutes(-15),
                    AdresseIP = "*************",
                    Commentaire = "Création d'un nouvel actif de données",
                    ClassificationSensibilite = ClassificationSensibilite.Confidentiel,
                    RequiertConformite = true
                },
                new EntreeAudit
                {
                    Id = Guid.NewGuid(),
                    Action = "Évaluation de conformité GDPR",
                    TypeEntite = "ActifDonnees",
                    EntiteId = Guid.NewGuid(),
                    NomEntite = "Base de données clients",
                    TypeAction = TypeActionAudit.EvaluationConformite,
                    NiveauGravite = NiveauGraviteAudit.Conformite,
                    Categorie = CategorieAudit.ConformiteReglementaire,
                    UtilisateurId = "auditeur",
                    NomUtilisateur = "<EMAIL>",
                    NomCompletUtilisateur = "Marie Auditeur",
                    DateAction = baseDate.AddMinutes(-10),
                    AdresseIP = "*************",
                    Commentaire = "Évaluation de conformité réussie",
                    Contexte = "Conformité",
                    RequiertConformite = true,
                    ReglementationsApplicables = "GDPR, Loi 25"
                }
            });
        }

        public MockServiceAudit(
            ILogger<MockServiceAudit> logger,
            IHttpContextAccessor httpContextAccessor,
            AuthenticationStateProvider authStateProvider)
        {
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _authStateProvider = authStateProvider;
        }

        public async Task EnregistrerAuditAsync(EntreeAudit entree)
        {
            try
            {
                // Compléter les informations de contexte
                await CompleterInformationsContexte(entree);
                
                // Ajouter un ID unique
                entree.Id = Guid.NewGuid();
                entree.DateAction = DateTime.UtcNow;
                entree.CorrelationId = _correlationId;

                // Stocker en mémoire (insertar al principio para que aparezca primero)
                _audits.Insert(0, entree);

                // Limitar a 1000 entradas para evitar problemas de memoria
                if (_audits.Count > 1000)
                {
                    _audits.RemoveAt(_audits.Count - 1);
                }

                // Log local pour traçabilité
                _logger.LogInformation("Audit enregistré: {Action} par {Utilisateur} sur {Entite}", 
                    entree.Action, entree.NomUtilisateur, entree.TypeEntite);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'enregistrement de l'audit: {Action}", entree.Action);
            }
        }

        public async Task EnregistrerActionAsync(
            string action,
            string typeEntite,
            Guid? entiteId = null,
            string? nomEntite = null,
            TypeActionAudit typeAction = TypeActionAudit.Lecture,
            NiveauGraviteAudit niveauGravite = NiveauGraviteAudit.Information,
            CategorieAudit categorie = CategorieAudit.GouvernanceDonnees,
            string? valeursAvant = null,
            string? valeursApres = null,
            string? commentaire = null,
            string? contexte = null,
            string? domaineAffaires = null,
            ClassificationSensibilite? classification = null,
            bool requiertConformite = false,
            string? reglementations = null)
        {
            var entreeAudit = new EntreeAudit
            {
                Action = action,
                TypeEntite = typeEntite,
                EntiteId = entiteId,
                NomEntite = nomEntite,
                TypeAction = typeAction,
                NiveauGravite = niveauGravite,
                Categorie = categorie,
                ValeursAvant = valeursAvant,
                ValeursApres = valeursApres,
                Commentaire = commentaire,
                Contexte = contexte,
                DomaineAffaires = domaineAffaires,
                ClassificationSensibilite = classification,
                RequiertConformite = requiertConformite,
                ReglementationsApplicables = reglementations,
                CorrelationId = _correlationId,
                DateAction = DateTime.UtcNow
            };

            await EnregistrerAuditAsync(entreeAudit);
        }

        public async Task EnregistrerActionAsync(EntreeAudit entree)
        {
            await EnregistrerAuditAsync(entree);
        }

        public async Task EnregistrerConnexionAsync(string utilisateurId, string nomUtilisateur, bool succes = true, string? messageErreur = null)
        {
            await EnregistrerActionAsync(
                succes ? "Connexion utilisateur réussie" : "Échec de connexion utilisateur",
                "Utilisateur",
                Guid.TryParse(utilisateurId, out var id) ? id : null,
                nomUtilisateur,
                succes ? TypeActionAudit.Connexion : TypeActionAudit.EchecConnexion,
                succes ? NiveauGraviteAudit.Information : NiveauGraviteAudit.Avertissement,
                CategorieAudit.Securite,
                commentaire: succes ? "Connexion réussie" : $"Échec de connexion: {messageErreur}",
                contexte: "Authentification",
                requiertConformite: true,
                reglementations: "GDPR, Politique de sécurité interne");
        }

        public async Task EnregistrerDeconnexionAsync(string utilisateurId, string nomUtilisateur)
        {
            await EnregistrerActionAsync(
                "Déconnexion utilisateur",
                "Utilisateur",
                Guid.TryParse(utilisateurId, out var id) ? id : null,
                nomUtilisateur,
                TypeActionAudit.Deconnexion,
                NiveauGraviteAudit.Information,
                CategorieAudit.Securite,
                commentaire: "Déconnexion utilisateur",
                contexte: "Authentification");
        }

        public async Task EnregistrerCreationAsync(string typeEntite, Guid entiteId, string? nomEntite = null, 
            object? valeurs = null, string? commentaire = null, CategorieAudit categorie = CategorieAudit.GouvernanceDonnees, 
            ClassificationSensibilite? classification = null)
        {
            await EnregistrerActionAsync(
                $"Création {typeEntite}",
                typeEntite,
                entiteId,
                nomEntite,
                TypeActionAudit.Creation,
                NiveauGraviteAudit.Information,
                categorie,
                valeursApres: valeurs != null ? JsonSerializer.Serialize(valeurs, _jsonOptions) : null,
                commentaire: commentaire,
                classification: classification,
                requiertConformite: classification >= ClassificationSensibilite.Confidentiel);
        }

        public async Task EnregistrerModificationAsync(string typeEntite, Guid entiteId, string? nomEntite = null, 
            object? valeursAvant = null, object? valeursApres = null, string? commentaire = null, 
            CategorieAudit categorie = CategorieAudit.GouvernanceDonnees, ClassificationSensibilite? classification = null)
        {
            await EnregistrerActionAsync(
                $"Modification {typeEntite}",
                typeEntite,
                entiteId,
                nomEntite,
                TypeActionAudit.Modification,
                NiveauGraviteAudit.Information,
                categorie,
                valeursAvant: valeursAvant != null ? JsonSerializer.Serialize(valeursAvant, _jsonOptions) : null,
                valeursApres: valeursApres != null ? JsonSerializer.Serialize(valeursApres, _jsonOptions) : null,
                commentaire: commentaire,
                classification: classification,
                requiertConformite: classification >= ClassificationSensibilite.Confidentiel);
        }

        public async Task EnregistrerSuppressionAsync(string typeEntite, Guid entiteId, string? nomEntite = null, 
            object? valeurs = null, string? commentaire = null, CategorieAudit categorie = CategorieAudit.GouvernanceDonnees, 
            ClassificationSensibilite? classification = null)
        {
            await EnregistrerActionAsync(
                $"Suppression {typeEntite}",
                typeEntite,
                entiteId,
                nomEntite,
                TypeActionAudit.Suppression,
                NiveauGraviteAudit.Avertissement,
                categorie,
                valeursAvant: valeurs != null ? JsonSerializer.Serialize(valeurs, _jsonOptions) : null,
                commentaire: commentaire,
                classification: classification,
                requiertConformite: true,
                reglementations: "GDPR, Politique de rétention des données");
        }

        public async Task EnregistrerEvenementSecuriteAsync(string action, string details, NiveauGraviteAudit gravite = NiveauGraviteAudit.Securite)
        {
            await EnregistrerActionAsync(
                action,
                "Sécurité",
                typeAction: TypeActionAudit.DetectionAnomalie,
                niveauGravite: gravite,
                categorie: CategorieAudit.Securite,
                commentaire: details,
                contexte: "Sécurité",
                requiertConformite: true,
                reglementations: "Politique de sécurité, SOX, GDPR");
        }

        public async Task EnregistrerEvenementConformiteAsync(string action, string typeEntite, Guid? entiteId = null, 
            string? reglementations = null, bool conforme = true, string? details = null)
        {
            await EnregistrerActionAsync(
                action,
                typeEntite,
                entiteId,
                typeAction: conforme ? TypeActionAudit.EvaluationConformite : TypeActionAudit.ViolationPolitique,
                niveauGravite: conforme ? NiveauGraviteAudit.Conformite : NiveauGraviteAudit.Critique,
                categorie: CategorieAudit.ConformiteReglementaire,
                commentaire: details,
                contexte: "Conformité",
                requiertConformite: true,
                reglementations: reglementations);
        }

        public async Task<ResultatPagineAudit> ObtenirAuditsAsync(FiltresAudit filtres)
        {
            await Task.Delay(100); // Simuler un délai

            var query = _audits.AsQueryable();

            // Appliquer les filtres
            if (!string.IsNullOrEmpty(filtres.TexteRecherche))
            {
                query = query.Where(a => a.Action.Contains(filtres.TexteRecherche, StringComparison.OrdinalIgnoreCase) ||
                                        a.TypeEntite.Contains(filtres.TexteRecherche, StringComparison.OrdinalIgnoreCase) ||
                                        (a.NomUtilisateur != null && a.NomUtilisateur.Contains(filtres.TexteRecherche, StringComparison.OrdinalIgnoreCase)));
            }

            if (filtres.DateDebut.HasValue)
                query = query.Where(a => a.DateAction >= filtres.DateDebut.Value);

            if (filtres.DateFin.HasValue)
                query = query.Where(a => a.DateAction <= filtres.DateFin.Value);

            if (filtres.TypesAction?.Any() == true)
                query = query.Where(a => filtres.TypesAction.Contains(a.TypeAction));

            if (filtres.Categories?.Any() == true)
                query = query.Where(a => filtres.Categories.Contains(a.Categorie));

            if (filtres.NiveauxGravite?.Any() == true)
                query = query.Where(a => filtres.NiveauxGravite.Contains(a.NiveauGravite));

            // Ordonner par date décroissante
            query = query.OrderByDescending(a => a.DateAction);

            var total = query.Count();
            var audits = query.Skip((filtres.Page - 1) * filtres.Taille)
                             .Take(filtres.Taille)
                             .ToList();

            return new ResultatPagineAudit
            {
                Entrees = audits,
                TotalEntrees = total,
                Page = filtres.Page,
                Taille = filtres.Taille
            };
        }

        public async Task<List<EntreeAudit>> ObtenirHistoriqueEntiteAsync(string typeEntite, Guid entiteId)
        {
            await Task.Delay(50);
            return _audits.Where(a => a.TypeEntite == typeEntite && a.EntiteId == entiteId)
                         .OrderByDescending(a => a.DateAction)
                         .ToList();
        }

        public async Task<ResultatAuditUtilisateur> ObtenirAuditsUtilisateurAsync(string utilisateurId, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            await Task.Delay(50);
            var auditsUtilisateur = _audits.Where(a => a.UtilisateurId == utilisateurId);
            
            if (dateDebut.HasValue)
                auditsUtilisateur = auditsUtilisateur.Where(a => a.DateAction >= dateDebut.Value);
            
            if (dateFin.HasValue)
                auditsUtilisateur = auditsUtilisateur.Where(a => a.DateAction <= dateFin.Value);

            return new ResultatAuditUtilisateur
            {
                UtilisateurId = utilisateurId,
                Entrees = auditsUtilisateur.OrderByDescending(a => a.DateAction).ToList()
            };
        }

        public async Task<StatistiquesAuditAvancees> ObtenirStatistiquesAvanceesAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            await Task.Delay(100);
            var query = _audits.AsQueryable();
            
            if (dateDebut.HasValue)
                query = query.Where(a => a.DateAction >= dateDebut.Value);
            
            if (dateFin.HasValue)
                query = query.Where(a => a.DateAction <= dateFin.Value);

            return new StatistiquesAuditAvancees
            {
                TotalEntrees = query.Count(),
                ActionsParType = query.GroupBy(a => a.TypeAction)
                                     .ToDictionary(g => g.Key, g => g.Count()),
                ActionsParCategorie = query.GroupBy(a => a.Categorie)
                                          .ToDictionary(g => g.Key, g => g.Count())
            };
        }

        public async Task<List<EntreeAudit>> ObtenirEvenementsSecuriteAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            await Task.Delay(50);
            var query = _audits.Where(a => a.Categorie == CategorieAudit.Securite);
            
            if (dateDebut.HasValue)
                query = query.Where(a => a.DateAction >= dateDebut.Value);
            
            if (dateFin.HasValue)
                query = query.Where(a => a.DateAction <= dateFin.Value);

            return query.OrderByDescending(a => a.DateAction).ToList();
        }

        public async Task<RapportConformiteAudit> ObtenirRapportConformiteAsync(string? reglementation = null, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            await Task.Delay(100);
            return new RapportConformiteAudit
            {
                Reglementation = reglementation ?? "Toutes",
                DateDebut = dateDebut ?? DateTime.Now.AddDays(-30),
                DateFin = dateFin ?? DateTime.Now,
                ActionsConformes = _audits.Count(a => a.RequiertConformite && a.NiveauGravite != NiveauGraviteAudit.Critique),
                ActionsNonConformes = _audits.Count(a => a.NiveauGravite == NiveauGraviteAudit.Critique),
                TotalActionsConformite = _audits.Count(a => a.RequiertConformite)
            };
        }

        public async Task<byte[]> ExporterAuditsAsync(FiltresAudit filtres, FormatExport format = FormatExport.Excel)
        {
            await Task.Delay(200);
            // Retourner un tableau vide pour le moment
            return Array.Empty<byte>();
        }

        private async Task CompleterInformationsContexte(EntreeAudit entree)
        {
            try
            {
                // Obtenir les informations de l'utilisateur connecté
                var authState = await _authStateProvider.GetAuthenticationStateAsync();
                if (authState.User.Identity?.IsAuthenticated == true)
                {
                    entree.UtilisateurId = authState.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "Anonyme";
                    entree.NomUtilisateur = authState.User.FindFirst(ClaimTypes.Name)?.Value ?? "Utilisateur inconnu";
                    entree.NomCompletUtilisateur = authState.User.FindFirst(ClaimTypes.GivenName)?.Value + " " + 
                                                  authState.User.FindFirst(ClaimTypes.Surname)?.Value;
                }
                else
                {
                    entree.UtilisateurId = "Anonyme";
                    entree.NomUtilisateur = "Utilisateur non authentifié";
                }

                // Obtenir les informations de contexte HTTP
                var httpContext = _httpContextAccessor.HttpContext;
                if (httpContext != null)
                {
                    entree.AdresseIP = httpContext.Connection.RemoteIpAddress?.ToString();
                    entree.UserAgent = httpContext.Request.Headers["User-Agent"].FirstOrDefault();
                    entree.SessionId = httpContext.Session?.Id;
                }

                // Ajouter des métadonnées système
                var metadonnees = new
                {
                    Version = "1.0",
                    Environnement = "Mock",
                    MachineName = Environment.MachineName,
                    ProcessId = Environment.ProcessId,
                    ThreadId = Environment.CurrentManagedThreadId
                };
                entree.Metadonnees = JsonSerializer.Serialize(metadonnees, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erreur lors de la complétion des informations de contexte pour l'audit");
            }
        }
    }
}
