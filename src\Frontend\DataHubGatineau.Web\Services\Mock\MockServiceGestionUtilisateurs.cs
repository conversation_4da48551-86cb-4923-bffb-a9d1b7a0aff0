using DataHubGatineau.Web.Models;
using DataHubGatineau.Web.Services.Interfaces;
using DataHubGatineau.Web.Models.Auditoria;

namespace DataHubGatineau.Web.Services.Mock
{
    /// <summary>
    /// Service mock pour la gestion des utilisateurs - utilisé temporairement
    /// </summary>
    public class MockServiceGestionUtilisateurs : IServiceGestionUtilisateurs
    {
        private static readonly List<UtilisateurAvecRoles> _utilisateurs = new();
        private static readonly List<InfoRole> _roles = new();
        private readonly IServiceAudit _serviceAudit;

        static MockServiceGestionUtilisateurs()
        {
            InitialiserDonneesMock();
        }

        public MockServiceGestionUtilisateurs(IServiceAudit serviceAudit)
        {
            _serviceAudit = serviceAudit;
        }

        private static void InitialiserDonneesMock()
        {
            // Initialiser les rôles
            _roles.AddRange(new[]
            {
                new InfoRole { Id = Guid.NewGuid(), Nom = "Administrateur", Description = "Administrateur système" },
                new InfoRole { Id = Guid.NewGuid(), Nom = "Gestionnaire", Description = "Gestionnaire de données" },
                new InfoRole { Id = Guid.NewGuid(), Nom = "Utilisateur", Description = "Utilisateur standard" }
            });

            // Initialiser quelques utilisateurs de test
            _utilisateurs.AddRange(new[]
            {
                new UtilisateurAvecRoles
                {
                    Id = Guid.NewGuid(),
                    NomUtilisateur = "admin",
                    Courriel = "<EMAIL>",
                    Prenom = "Admin",
                    Nom = "Système",
                    Telephone = "(*************",
                    Poste = "1001",
                    EstActif = true,
                    DateCreation = DateTime.Now.AddDays(-30),
                    Roles = new List<InfoRole> { _roles[0] }
                },
                new UtilisateurAvecRoles
                {
                    Id = Guid.NewGuid(),
                    NomUtilisateur = "gestionnaire",
                    Courriel = "<EMAIL>",
                    Prenom = "Jean",
                    Nom = "Dupont",
                    Telephone = "(*************",
                    Poste = "1002",
                    EstActif = true,
                    DateCreation = DateTime.Now.AddDays(-20),
                    Roles = new List<InfoRole> { _roles[1] }
                }
            });
        }

        public async Task<List<UtilisateurAvecRoles>> ObtenirTousUtilisateursAsync()
        {
            await Task.Delay(200);
            return _utilisateurs.ToList();
        }

        public async Task<UtilisateurAvecRoles?> ObtenirUtilisateurParIdAsync(Guid id)
        {
            await Task.Delay(100);
            return _utilisateurs.FirstOrDefault(u => u.Id == id);
        }

        public async Task<UtilisateurAvecRoles> CreerUtilisateurAsync(DemandeCreationUtilisateur demande)
        {
            await Task.Delay(300);

            var nouvelUtilisateur = new UtilisateurAvecRoles
            {
                Id = Guid.NewGuid(),
                NomUtilisateur = demande.NomUtilisateur,
                Courriel = demande.Courriel,
                Prenom = demande.Prenom,
                Nom = demande.Nom,
                Telephone = demande.Telephone, // Assuré d'être inclus
                Poste = demande.Poste, // Assuré d'être inclus
                EstActif = demande.EstActif,
                DateCreation = DateTime.Now,
                Roles = demande.IdsRoles?.Select(id => _roles.FirstOrDefault(r => r.Id == id))
                                        .Where(r => r != null)
                                        .ToList() ?? new List<InfoRole>()
            };

            _utilisateurs.Add(nouvelUtilisateur);

            // Enregistrer l'audit
            _ = Task.Run(async () =>
            {
                try
                {
                    await _serviceAudit.EnregistrerCreationAsync(
                        "Utilisateur",
                        nouvelUtilisateur.Id,
                        $"{nouvelUtilisateur.Prenom} {nouvelUtilisateur.Nom}",
                        nouvelUtilisateur,
                        "Création d'un nouvel utilisateur via l'interface d'administration",
                        CategorieAudit.GouvernanceDonnees,
                        DataHubGatineau.Web.Models.Auditoria.ClassificationSensibilite.Interne);
                }
                catch (Exception ex)
                {
                    // Log l'erreur mais ne pas interrompre l'opération principale
                    Console.WriteLine($"Erreur lors de l'enregistrement de l'audit: {ex.Message}");
                }
            });

            return nouvelUtilisateur;
        }

        public async Task<UtilisateurAvecRoles> MettreAJourUtilisateurAsync(Guid id, DemandeMiseAJourUtilisateur demande)
        {
            await Task.Delay(300);

            var utilisateur = _utilisateurs.FirstOrDefault(u => u.Id == id);
            if (utilisateur == null)
                throw new KeyNotFoundException($"Utilisateur avec ID {id} non trouvé");

            // Sauvegarder les valeurs avant modification pour l'audit
            var valeursAvant = new
            {
                utilisateur.Courriel,
                utilisateur.Prenom,
                utilisateur.Nom,
                utilisateur.Telephone,
                utilisateur.Poste,
                utilisateur.EstActif,
                Roles = utilisateur.Roles.Select(r => r.Nom).ToList()
            };

            // Mettre à jour les propriétés
            if (!string.IsNullOrEmpty(demande.Courriel))
                utilisateur.Courriel = demande.Courriel;
            if (!string.IsNullOrEmpty(demande.Prenom))
                utilisateur.Prenom = demande.Prenom;
            if (!string.IsNullOrEmpty(demande.Nom))
                utilisateur.Nom = demande.Nom;
            if (!string.IsNullOrEmpty(demande.Telephone))
                utilisateur.Telephone = demande.Telephone;
            if (!string.IsNullOrEmpty(demande.Poste))
                utilisateur.Poste = demande.Poste;
            if (demande.EstActif.HasValue)
                utilisateur.EstActif = demande.EstActif.Value;

            // Mettre à jour les rôles
            if (demande.IdsRoles != null)
            {
                utilisateur.Roles = demande.IdsRoles.Select(id => _roles.FirstOrDefault(r => r.Id == id))
                                                   .Where(r => r != null)
                                                   .ToList();
            }

            // Enregistrer l'audit de modification
            var valeursApres = new
            {
                utilisateur.Courriel,
                utilisateur.Prenom,
                utilisateur.Nom,
                utilisateur.Telephone,
                utilisateur.Poste,
                utilisateur.EstActif,
                Roles = utilisateur.Roles.Select(r => r.Nom).ToList()
            };

            _ = Task.Run(async () =>
            {
                try
                {
                    await _serviceAudit.EnregistrerModificationAsync(
                        "Utilisateur",
                        utilisateur.Id,
                        $"{utilisateur.Prenom} {utilisateur.Nom}",
                        valeursAvant,
                        valeursApres,
                        "Modification des informations utilisateur via l'interface d'administration",
                        CategorieAudit.GouvernanceDonnees,
                        DataHubGatineau.Web.Models.Auditoria.ClassificationSensibilite.Interne);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Erreur lors de l'enregistrement de l'audit: {ex.Message}");
                }
            });

            return utilisateur;
        }

        public async Task<bool> SupprimerUtilisateurAsync(Guid id)
        {
            await Task.Delay(200);
            var utilisateur = _utilisateurs.FirstOrDefault(u => u.Id == id);
            if (utilisateur != null)
            {
                _utilisateurs.Remove(utilisateur);
                return true;
            }
            return false;
        }

        public async Task<bool> BasculerActivationUtilisateurAsync(Guid id)
        {
            await Task.Delay(200);
            var utilisateur = _utilisateurs.FirstOrDefault(u => u.Id == id);
            if (utilisateur != null)
            {
                utilisateur.EstActif = !utilisateur.EstActif;
                return true;
            }
            return false;
        }

        public async Task<List<InfoRole>> ObtenirTousRolesAsync()
        {
            await Task.Delay(100);
            return _roles.ToList();
        }

        public async Task<InfoRole?> ObtenirRoleParIdAsync(Guid id)
        {
            await Task.Delay(50);
            return _roles.FirstOrDefault(r => r.Id == id);
        }

        public async Task<InfoRole> CreerRoleAsync(DemandeCreationRole demande)
        {
            await Task.Delay(200);
            var nouveauRole = new InfoRole
            {
                Id = Guid.NewGuid(),
                Nom = demande.Nom,
                Description = demande.Description
            };
            _roles.Add(nouveauRole);
            return nouveauRole;
        }

        public async Task<InfoRole> MettreAJourRoleAsync(Guid id, DemandeMiseAJourRole demande)
        {
            await Task.Delay(200);
            var role = _roles.FirstOrDefault(r => r.Id == id);
            if (role == null)
                throw new KeyNotFoundException($"Rôle avec ID {id} non trouvé");

            role.Nom = demande.Nom;
            role.Description = demande.Description;
            return role;
        }

        public async Task<bool> SupprimerRoleAsync(Guid id)
        {
            await Task.Delay(200);
            var role = _roles.FirstOrDefault(r => r.Id == id);
            if (role != null)
            {
                _roles.Remove(role);
                return true;
            }
            return false;
        }

        // Méthodes non implémentées pour ce mock
        public Task<List<UtilisateurAvecRoles>> ObtenirApprobaiteursDisponiblesAsync(string typeEtape)
        {
            return Task.FromResult(new List<UtilisateurAvecRoles>());
        }

        public Task<Dictionary<string, Guid>> AssignerApprobaiteursAutomatiqueAsync(Guid domaineId)
        {
            return Task.FromResult(new Dictionary<string, Guid>());
        }

        public Task<StatistiquesUtilisateurs> ObtenirStatistiquesAsync()
        {
            return Task.FromResult(new StatistiquesUtilisateurs());
        }

        public Task<List<UtilisateurAvecRoles>> RechercherUtilisateursAsync(string terme)
        {
            var resultats = _utilisateurs.Where(u => 
                u.NomUtilisateur.Contains(terme, StringComparison.OrdinalIgnoreCase) ||
                u.Prenom.Contains(terme, StringComparison.OrdinalIgnoreCase) ||
                u.Nom.Contains(terme, StringComparison.OrdinalIgnoreCase) ||
                u.Courriel.Contains(terme, StringComparison.OrdinalIgnoreCase)
            ).ToList();
            
            return Task.FromResult(resultats);
        }

        public Task<List<UtilisateurAvecRoles>> ObtenirUtilisateursParRoleAsync(Guid roleId)
        {
            var resultats = _utilisateurs.Where(u => u.Roles.Any(r => r.Id == roleId)).ToList();
            return Task.FromResult(resultats);
        }

        public Task<bool> AssignerRoleAsync(Guid utilisateurId, Guid roleId)
        {
            var utilisateur = _utilisateurs.FirstOrDefault(u => u.Id == utilisateurId);
            var role = _roles.FirstOrDefault(r => r.Id == roleId);
            
            if (utilisateur != null && role != null && !utilisateur.Roles.Any(r => r.Id == roleId))
            {
                utilisateur.Roles.Add(role);
                return Task.FromResult(true);
            }
            
            return Task.FromResult(false);
        }

        public Task<bool> RetirerRoleAsync(Guid utilisateurId, Guid roleId)
        {
            var utilisateur = _utilisateurs.FirstOrDefault(u => u.Id == utilisateurId);
            if (utilisateur != null)
            {
                var role = utilisateur.Roles.FirstOrDefault(r => r.Id == roleId);
                if (role != null)
                {
                    utilisateur.Roles.Remove(role);
                    return Task.FromResult(true);
                }
            }
            
            return Task.FromResult(false);
        }

        public Task<Dictionary<string, List<InfoRole>>> ObtenirRolesParNiveauAsync()
        {
            var rolesParNiveau = new Dictionary<string, List<InfoRole>>
            {
                { "Administrateur", _roles.Where(r => r.Nom.Contains("Admin")).ToList() },
                { "Gestionnaire", _roles.Where(r => r.Nom.Contains("Gestionnaire")).ToList() },
                { "Utilisateur", _roles.Where(r => r.Nom.Contains("Utilisateur")).ToList() }
            };
            return Task.FromResult(rolesParNiveau);
        }

        public Task<bool> AjouterRoleUtilisateurAsync(Guid utilisateurId, Guid roleId)
        {
            return AssignerRoleAsync(utilisateurId, roleId);
        }

        public Task<bool> SupprimerRoleUtilisateurAsync(Guid utilisateurId, Guid roleId)
        {
            return RetirerRoleAsync(utilisateurId, roleId);
        }

        public Task<List<ActiviteUtilisateur>> ObtenirActiviteRecenteAsync(int nombreJours = 7)
        {
            // Retourner quelques activités de test
            var activites = new List<ActiviteUtilisateur>
            {
                new ActiviteUtilisateur
                {
                    UtilisateurId = _utilisateurs.FirstOrDefault()?.Id ?? Guid.NewGuid(),
                    NomUtilisateur = "admin",
                    NomComplet = "Admin Système",
                    Action = "Connexion au système",
                    DateAction = DateTime.Now.AddHours(-1),
                    Details = "Connexion réussie depuis l'interface web"
                },
                new ActiviteUtilisateur
                {
                    UtilisateurId = _utilisateurs.Skip(1).FirstOrDefault()?.Id ?? Guid.NewGuid(),
                    NomUtilisateur = "gestionnaire",
                    NomComplet = "Jean Dupont",
                    Action = "Modification utilisateur",
                    DateAction = DateTime.Now.AddHours(-2),
                    Details = "Modification des informations de profil"
                }
            };

            return Task.FromResult(activites);
        }
    }
}
