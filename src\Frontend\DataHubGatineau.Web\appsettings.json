{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ApiBaseUrl": "http://localhost:5082", "UseRealServices": true, "ApiSettings": {"BaseUrl": "http://localhost:5082"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "TenantId": "PLACEHOLDER-TENANT-ID", "ClientId": "PLACEHOLDER-CLIENT-ID", "CallbackPath": "/signin-oidc", "SignedOutCallbackPath": "/signout-callback-oidc"}}